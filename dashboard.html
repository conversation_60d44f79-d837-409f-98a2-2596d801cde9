<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard - SantriMental</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Inter', sans-serif;
        }
        
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        
        .glass-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(25px);
            -webkit-backdrop-filter: blur(25px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .sidebar-active {
            background: rgba(255, 255, 255, 0.2);
            border-right: 3px solid #a78bfa;
        }
        
        .fade-in {
            animation: fadeIn 0.6s ease-out forwards;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        .slide-in {
            animation: slideIn 0.5s ease-out forwards;
        }
        
        @keyframes slideIn {
            from { opacity: 0; transform: translateX(-20px); }
            to { opacity: 1; transform: translateX(0); }
        }
        
        .pulse-glow {
            animation: pulseGlow 2s ease-in-out infinite alternate;
        }
        
        @keyframes pulseGlow {
            from { box-shadow: 0 0 20px rgba(102, 126, 234, 0.4); }
            to { box-shadow: 0 0 40px rgba(102, 126, 234, 0.8); }
        }
        
        .stat-card:hover {
            transform: translateY(-5px);
            transition: all 0.3s ease;
        }
        
        .mobile-menu-open {
            transform: translateX(0);
        }
        
        .mobile-menu-closed {
            transform: translateX(-100%);
        }
    </style>
</head>
<body class="gradient-bg min-h-screen">
    
    <!-- Mobile Menu Overlay -->
    <div id="mobile-overlay" class="fixed inset-0 bg-black/50 z-40 lg:hidden hidden"></div>
    
    <!-- Sidebar -->
    <aside id="sidebar" class="fixed left-0 top-0 h-full w-64 glass-card z-50 transform mobile-menu-closed lg:mobile-menu-open transition-transform duration-300">
        <div class="p-6">
            <!-- Logo -->
            <div class="flex items-center mb-8">
                <div class="w-10 h-10 bg-gradient-to-r from-purple-500 to-pink-500 rounded-lg flex items-center justify-center mr-3">
                    <span class="text-white font-bold text-xl">S</span>
                </div>
                <h1 class="text-xl font-bold text-white">SantriMental</h1>
            </div>
            
            <!-- Navigation -->
            <nav class="space-y-2">
                <a href="#" data-page="dashboard" class="sidebar-link sidebar-active flex items-center px-4 py-3 text-white rounded-lg transition-all duration-300">
                    <svg class="w-5 h-5 mr-3" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"/>
                    </svg>
                    Dashboard
                </a>
                
                <a href="#" data-page="assessment" class="sidebar-link flex items-center px-4 py-3 text-white/70 hover:text-white hover:bg-white/10 rounded-lg transition-all duration-300">
                    <svg class="w-5 h-5 mr-3" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M3 4a1 1 0 011-1h4a1 1 0 010 2H6.414l2.293 2.293a1 1 0 01-1.414 1.414L5 6.414V8a1 1 0 01-2 0V4zm9 1a1 1 0 010-2h4a1 1 0 011 1v4a1 1 0 01-2 0V6.414l-2.293 2.293a1 1 0 11-1.414-1.414L13.586 5H12zm-9 7a1 1 0 012 0v1.586l2.293-2.293a1 1 0 111.414 1.414L6.414 15H8a1 1 0 010 2H4a1 1 0 01-1-1v-4zm13-1a1 1 0 011 1v4a1 1 0 01-1 1h-4a1 1 0 010-2h1.586l-2.293-2.293a1 1 0 111.414-1.414L15 13.586V12a1 1 0 011-1z" clip-rule="evenodd"/>
                    </svg>
                    Tes SRQ-20
                </a>
                
                <a href="#" data-page="history" class="sidebar-link flex items-center px-4 py-3 text-white/70 hover:text-white hover:bg-white/10 rounded-lg transition-all duration-300">
                    <svg class="w-5 h-5 mr-3" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd"/>
                    </svg>
                    Riwayat
                </a>
                
                <a href="#" data-page="analytics" class="sidebar-link flex items-center px-4 py-3 text-white/70 hover:text-white hover:bg-white/10 rounded-lg transition-all duration-300">
                    <svg class="w-5 h-5 mr-3" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M2 11a1 1 0 011-1h2a1 1 0 011 1v5a1 1 0 01-1 1H3a1 1 0 01-1-1v-5zM8 7a1 1 0 011-1h2a1 1 0 011 1v9a1 1 0 01-1 1H9a1 1 0 01-1-1V7zM14 4a1 1 0 011-1h2a1 1 0 011 1v12a1 1 0 01-1 1h-2a1 1 0 01-1-1V4z"/>
                    </svg>
                    Analitik
                </a>
                
                <a href="#" data-page="profile" class="sidebar-link flex items-center px-4 py-3 text-white/70 hover:text-white hover:bg-white/10 rounded-lg transition-all duration-300">
                    <svg class="w-5 h-5 mr-3" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd"/>
                    </svg>
                    Profil
                </a>
            </nav>
        </div>
        
        <!-- Logout Button -->
        <div class="absolute bottom-6 left-6 right-6">
            <button id="logout-btn" class="w-full flex items-center px-4 py-3 text-white/70 hover:text-white hover:bg-red-500/20 rounded-lg transition-all duration-300">
                <svg class="w-5 h-5 mr-3" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M3 3a1 1 0 00-1 1v12a1 1 0 102 0V4a1 1 0 00-1-1zm10.293 9.293a1 1 0 001.414 1.414l3-3a1 1 0 000-1.414l-3-3a1 1 0 10-1.414 1.414L14.586 9H7a1 1 0 100 2h7.586l-1.293 1.293z" clip-rule="evenodd"/>
                </svg>
                Keluar
            </button>
        </div>
    </aside>
    
    <!-- Main Content -->
    <main class="lg:ml-64 min-h-screen">
        
        <!-- Header -->
        <header class="glass-card p-4 lg:p-6">
            <div class="flex items-center justify-between">
                <!-- Mobile Menu Button -->
                <button id="mobile-menu-btn" class="lg:hidden text-white p-2">
                    <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M3 5a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM3 10a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM3 15a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clip-rule="evenodd"/>
                    </svg>
                </button>
                
                <!-- Page Title -->
                <h2 id="page-title" class="text-xl lg:text-2xl font-bold text-white">Dashboard</h2>
                
                <!-- User Info -->
                <div class="flex items-center space-x-4">
                    <div class="hidden sm:block text-right">
                        <p id="user-name" class="text-white font-medium">Loading...</p>
                        <p class="text-purple-200 text-sm" id="current-time"></p>
                    </div>
                    <div id="user-avatar" class="w-10 h-10 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center">
                        <span id="user-initials" class="text-white font-bold">?</span>
                    </div>
                </div>
            </div>
        </header>
        
        <!-- Page Content -->
        <div class="p-4 lg:p-6">
            
            <!-- Dashboard Page -->
            <div id="dashboard-page" class="page-content">
                
                <!-- Welcome Section -->
                <div class="glass-card rounded-2xl p-6 mb-6 fade-in">
                    <div class="flex flex-col lg:flex-row items-center justify-between">
                        <div>
                            <h3 class="text-2xl font-bold text-white mb-2">Selamat Datang di SantriMental! 👋</h3>
                            <p class="text-purple-200">Pantau kesehatan mental Anda dengan mudah dan akurat</p>
                        </div>
                        <button id="start-assessment-btn" class="mt-4 lg:mt-0 bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 text-white font-bold py-3 px-6 rounded-lg transition-all duration-300 transform hover:scale-105 pulse-glow">
                            Mulai Tes SRQ-20
                        </button>
                    </div>
                </div>
                
                <!-- Stats Cards -->
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
                    <div class="stat-card glass-card rounded-xl p-6 fade-in">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-purple-200 text-sm">Total Tes</p>
                                <p id="total-tests" class="text-2xl font-bold text-white">0</p>
                            </div>
                            <div class="w-12 h-12 bg-blue-500/20 rounded-lg flex items-center justify-center">
                                <svg class="w-6 h-6 text-blue-400" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                </svg>
                            </div>
                        </div>
                    </div>
                    
                    <div class="stat-card glass-card rounded-xl p-6 fade-in" style="animation-delay: 0.1s;">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-purple-200 text-sm">Skor Terakhir</p>
                                <p id="last-score" class="text-2xl font-bold text-white">-</p>
                            </div>
                            <div class="w-12 h-12 bg-green-500/20 rounded-lg flex items-center justify-center">
                                <svg class="w-6 h-6 text-green-400" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                                </svg>
                            </div>
                        </div>
                    </div>
                    
                    <div class="stat-card glass-card rounded-xl p-6 fade-in" style="animation-delay: 0.2s;">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-purple-200 text-sm">Rata-rata Skor</p>
                                <p id="avg-score" class="text-2xl font-bold text-white">-</p>
                            </div>
                            <div class="w-12 h-12 bg-yellow-500/20 rounded-lg flex items-center justify-center">
                                <svg class="w-6 h-6 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M2 11a1 1 0 011-1h2a1 1 0 011 1v5a1 1 0 01-1 1H3a1 1 0 01-1-1v-5zM8 7a1 1 0 011-1h2a1 1 0 011 1v9a1 1 0 01-1 1H9a1 1 0 01-1-1V7zM14 4a1 1 0 011-1h2a1 1 0 011 1v12a1 1 0 01-1 1h-2a1 1 0 01-1-1V4z"/>
                                </svg>
                            </div>
                        </div>
                    </div>
                    
                    <div class="stat-card glass-card rounded-xl p-6 fade-in" style="animation-delay: 0.3s;">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-purple-200 text-sm">Tes Bulan Ini</p>
                                <p id="monthly-tests" class="text-2xl font-bold text-white">0</p>
                            </div>
                            <div class="w-12 h-12 bg-purple-500/20 rounded-lg flex items-center justify-center">
                                <svg class="w-6 h-6 text-purple-400" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clip-rule="evenodd"/>
                                </svg>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Charts Section -->
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
                    <!-- Score Trend Chart -->
                    <div class="glass-card rounded-xl p-6 fade-in" style="animation-delay: 0.4s;">
                        <h4 class="text-lg font-semibold text-white mb-4">Tren Skor SRQ-20</h4>
                        <div class="h-64">
                            <canvas id="score-trend-chart"></canvas>
                        </div>
                    </div>
                    
                    <!-- Monthly Distribution -->
                    <div class="glass-card rounded-xl p-6 fade-in" style="animation-delay: 0.5s;">
                        <h4 class="text-lg font-semibold text-white mb-4">Distribusi Bulanan</h4>
                        <div class="h-64">
                            <canvas id="monthly-chart"></canvas>
                        </div>
                    </div>
                </div>
                
                <!-- Recent Activity -->
                <div class="glass-card rounded-xl p-6 fade-in" style="animation-delay: 0.6s;">
                    <h4 class="text-lg font-semibold text-white mb-4">Aktivitas Terbaru</h4>
                    <div id="recent-activity" class="space-y-4">
                        <!-- Activity items will be inserted here -->
                    </div>
                </div>
                
            </div>
            
            <!-- Other pages will be loaded here -->
            <div id="assessment-page" class="page-content hidden">
                <div class="glass-card rounded-xl p-6 text-center">
                    <h3 class="text-xl font-bold text-white mb-4">Tes SRQ-20</h3>
                    <p class="text-purple-200 mb-6">Halaman tes akan dimuat di sini</p>
                    <button onclick="window.location.href='srq20-form.html'" class="bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 text-white font-bold py-3 px-6 rounded-lg transition-all duration-300">
                        Mulai Tes
                    </button>
                </div>
            </div>
            
            <div id="history-page" class="page-content hidden">
                <div class="glass-card rounded-xl p-6">
                    <h3 class="text-xl font-bold text-white mb-4">Riwayat Tes</h3>
                    <p class="text-purple-200">Riwayat tes Anda akan ditampilkan di sini</p>
                </div>
            </div>
            
            <div id="analytics-page" class="page-content hidden">
                <div class="glass-card rounded-xl p-6">
                    <h3 class="text-xl font-bold text-white mb-4">Analitik</h3>
                    <p class="text-purple-200">Analitik mendalam akan ditampilkan di sini</p>
                </div>
            </div>
            
            <div id="profile-page" class="page-content hidden">
                <div class="glass-card rounded-xl p-6">
                    <h3 class="text-xl font-bold text-white mb-4">Profil Pengguna</h3>
                    <p class="text-purple-200">Pengaturan profil akan ditampilkan di sini</p>
                </div>
            </div>
            
        </div>
    </main>

    <script src="js/auth.js"></script>
    <script src="js/utils.js"></script>
    <script src="js/dashboard.js"></script>
</body>
</html>
