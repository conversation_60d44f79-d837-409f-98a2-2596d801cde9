<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Riwayat Skrining - SantriMental</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Inter', sans-serif;
        }
        
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        
        .glass-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(25px);
            -webkit-backdrop-filter: blur(25px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .fade-in {
            animation: fadeIn 0.6s ease-out forwards;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        .history-card {
            transition: all 0.3s ease;
        }
        
        .history-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        }
    </style>
</head>
<body class="gradient-bg min-h-screen p-4">
    
    <!-- Navigation Header -->
    <nav class="glass-card rounded-xl p-4 mb-6 max-w-6xl mx-auto">
        <div class="flex items-center justify-between">
            <div class="flex items-center">
                <a href="{{ route('dashboard') }}" class="text-white hover:text-purple-200 transition-colors mr-4">
                    <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z" clip-rule="evenodd"/>
                    </svg>
                </a>
                <div class="w-8 h-8 bg-gradient-to-r from-purple-500 to-pink-500 rounded-lg flex items-center justify-center mr-3">
                    <span class="text-white font-bold text-sm">S</span>
                </div>
                <h1 class="text-xl font-bold text-white">SantriMental</h1>
            </div>
            <div class="text-purple-200 text-sm">
                Riwayat Skrining
            </div>
        </div>
    </nav>

    <main class="max-w-6xl mx-auto">
        
        <!-- Header -->
        <div class="glass-card rounded-xl p-6 mb-6">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-2xl font-bold text-white mb-2">Riwayat Skrining SRQ-20</h1>
                    <p class="text-purple-200">Lihat semua hasil skrining kesehatan mental Anda</p>
                </div>
                <a href="{{ route('srq20-form') }}" class="bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 text-white font-bold py-3 px-6 rounded-lg transition-all duration-300 transform hover:scale-105">
                    Skrining Baru
                </a>
            </div>
        </div>

        <!-- Statistics Summary -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
            <div class="glass-card p-6 rounded-xl text-center">
                <div class="w-12 h-12 bg-blue-500/20 rounded-lg flex items-center justify-center mx-auto mb-3">
                    <svg class="w-6 h-6 text-blue-400" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                    </svg>
                </div>
                <p class="text-2xl font-bold text-white" id="total-count">0</p>
                <p class="text-purple-200 text-sm">Total Skrining</p>
            </div>
            
            <div class="glass-card p-6 rounded-xl text-center">
                <div class="w-12 h-12 bg-green-500/20 rounded-lg flex items-center justify-center mx-auto mb-3">
                    <svg class="w-6 h-6 text-green-400" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                    </svg>
                </div>
                <p class="text-2xl font-bold text-white" id="avg-score">0</p>
                <p class="text-purple-200 text-sm">Rata-rata Skor</p>
            </div>
            
            <div class="glass-card p-6 rounded-xl text-center">
                <div class="w-12 h-12 bg-yellow-500/20 rounded-lg flex items-center justify-center mx-auto mb-3">
                    <svg class="w-6 h-6 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z"/>
                    </svg>
                </div>
                <p class="text-2xl font-bold text-white" id="latest-score">-</p>
                <p class="text-purple-200 text-sm">Skor Terakhir</p>
            </div>
            
            <div class="glass-card p-6 rounded-xl text-center">
                <div class="w-12 h-12 bg-purple-500/20 rounded-lg flex items-center justify-center mx-auto mb-3">
                    <svg class="w-6 h-6 text-purple-400" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clip-rule="evenodd"/>
                    </svg>
                </div>
                <p class="text-2xl font-bold text-white" id="this-month">0</p>
                <p class="text-purple-200 text-sm">Bulan Ini</p>
            </div>
        </div>

        <!-- History List -->
        <div class="glass-card rounded-xl p-6">
            <div class="flex items-center justify-between mb-6">
                <h2 class="text-xl font-bold text-white">Riwayat Skrining</h2>
                <div class="flex items-center space-x-4">
                    <select id="filter-status" class="bg-white/10 border border-white/30 rounded-lg px-3 py-2 text-white text-sm">
                        <option value="">Semua Status</option>
                        <option value="normal">Normal</option>
                        <option value="concern">Perlu Perhatian</option>
                        <option value="high_risk">Risiko Tinggi</option>
                    </select>
                    <select id="filter-period" class="bg-white/10 border border-white/30 rounded-lg px-3 py-2 text-white text-sm">
                        <option value="">Semua Periode</option>
                        <option value="week">Minggu Ini</option>
                        <option value="month">Bulan Ini</option>
                        <option value="3months">3 Bulan Terakhir</option>
                    </select>
                </div>
            </div>

            <!-- Loading State -->
            <div id="loading-state" class="text-center py-12">
                <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-white mx-auto mb-4"></div>
                <p class="text-purple-200">Memuat riwayat...</p>
            </div>

            <!-- Empty State -->
            <div id="empty-state" class="hidden text-center py-12">
                <div class="w-16 h-16 bg-white/10 rounded-full flex items-center justify-center mx-auto mb-4">
                    <svg class="w-8 h-8 text-purple-300" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M4 4a2 2 0 012-2h8a2 2 0 012 2v12a1 1 0 110 2h-3a1 1 0 01-1-1v-2a1 1 0 00-1-1H9a1 1 0 00-1 1v2a1 1 0 01-1 1H4a1 1 0 110-2V4zm3 1h2v2H7V5zm2 4H7v2h2V9zm2-4h2v2h-2V5zm2 4h-2v2h2V9z" clip-rule="evenodd"/>
                    </svg>
                </div>
                <h3 class="text-lg font-semibold text-white mb-2">Belum Ada Riwayat</h3>
                <p class="text-purple-200 mb-4">Anda belum pernah melakukan skrining SRQ-20</p>
                <a href="{{ route('srq20-form') }}" class="bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 text-white font-bold py-2 px-4 rounded-lg transition-all duration-300">
                    Mulai Skrining Pertama
                </a>
            </div>

            <!-- History Items -->
            <div id="history-list" class="hidden space-y-4">
                <!-- History items will be inserted here -->
            </div>

            <!-- Pagination -->
            <div id="pagination" class="hidden flex items-center justify-center mt-6 space-x-2">
                <!-- Pagination will be inserted here -->
            </div>
        </div>

    </main>

    <!-- Toast Notification -->
    <div id="toast" class="hidden fixed top-4 right-4 glass-card p-4 rounded-lg shadow-lg z-50 max-w-sm">
        <div class="flex items-center">
            <div id="toast-icon" class="mr-3"></div>
            <div>
                <p id="toast-title" class="font-semibold text-white"></p>
                <p id="toast-message" class="text-purple-200 text-sm"></p>
            </div>
        </div>
    </div>

    <script src="{{ asset('js/auth.js') }}"></script>
    <script src="{{ asset('js/utils.js') }}"></script>
    <script src="{{ asset('js/history.js') }}"></script>
</body>
</html>
