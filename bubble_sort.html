<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simulasi Bubble Sort (Canvas)</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Inter', sans-serif;
            background-color: #f0f4f8;
            color: #333;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            margin: 0;
            padding: 20px;
            box-sizing: border-box;
            overflow: hidden; /* Prevent body scroll */
        }

        .container {
            background-color: #ffffff;
            padding: 40px;
            border-radius: 12px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            text-align: center;
            max-width: 900px;
            width: 100%;
            display: flex;
            flex-direction: column;
            align-items: center;
        }

        h1 {
            color: #0056b3;
            margin-top: 0;
            margin-bottom: 10px;
            font-size: 2.5em;
            font-weight: 700;
        }

        p {
            font-size: 1.1em;
            color: #555;
            margin-bottom: 30px;
        }

        canvas {
            background-color: #fafafa;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            margin: 40px 0;
            display: block; /* Remove extra space below canvas */
        }

        .controls {
            margin-top: 20px;
            display: flex;
            justify-content: center;
            gap: 15px;
            width: 100%;
        }

        button {
            padding: 12px 25px;
            font-size: 1.1em;
            font-weight: 600;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            flex-grow: 1; /* Allow buttons to grow */
            max-width: 200px; /* Limit button width */
        }

        button#start-btn {
            background-color: #28a745;
            color: white;
            box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
        }

        button#start-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(40, 167, 69, 0.4);
        }

        button#start-btn:disabled {
            background-color: #ccc;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        button#reset-btn {
            background-color: #6c757d;
            color: white;
            box-shadow: 0 4px 15px rgba(108, 117, 125, 0.3);
        }

        button#reset-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(108, 117, 125, 0.4);
        }

        .info {
            margin-top: 30px;
            background-color: #e9ecef;
            padding: 15px;
            border-radius: 8px;
            display: flex;
            justify-content: center;
            gap: 40px;
            width: 100%;
        }

        .info-item {
            font-size: 1.1em;
            font-weight: 500;
            color: #495057;
        }

        .info-label {
            font-weight: 600;
            color: #212529;
        }

        #comparisons-count, #swaps-count {
            font-weight: bold;
            color: #007bff;
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            .container {
                padding: 20px;
            }
            h1 {
                font-size: 2em;
            }
            p {
                font-size: 1em;
            }
            .controls {
                flex-direction: column;
                align-items: center;
            }
            button {
                width: 100%;
                max-width: 250px;
                margin-bottom: 10px;
            }
            .info {
                flex-direction: column;
                gap: 10px;
                text-align: left;
            }
        }
    </style>
</head>
<body>

    <div class="container">
        <h1>Simulasi Bubble Sort</h1>
        <p>Algoritma pengurutan sederhana yang berulang kali menelusuri daftar, membandingkan setiap pasangan elemen yang berdekatan, dan menukarnya jika berada dalam urutan yang salah.</p>
        
        <canvas id="sortCanvas" width="800" height="400"></canvas>
        
        <div class="controls">
            <button id="start-btn">Mulai Sorting</button>
            <button id="reset-btn">Reset</button>
        </div>
        
        <div class="info">
            <div class="info-item">
                <span class="info-label">Jumlah Pembandingan:</span>
                <span id="comparisons-count">0</span>
            </div>
            <div class="info-item">
                <span class="info-label">Jumlah Pertukaran:</span>
                <span id="swaps-count">0</span>
            </div>
        </div>
    </div>

    <script>
        // Deklarasi elemen DOM
        const canvas = document.getElementById('sortCanvas');
        const ctx = canvas.getContext('2d');
        const startBtn = document.getElementById('start-btn');
        const resetBtn = document.getElementById('reset-btn');
        const comparisonsCountEl = document.getElementById('comparisons-count');
        const swapsCountEl = document.getElementById('swaps-count');

        // Konfigurasi
        const ARRAY_SIZE = 50; // Ukuran array
        const ANIMATION_SPEED = 30; // Kecepatan animasi dalam milidetik (lebih kecil lebih cepat)
        const MAX_VALUE = 100; // Nilai maksimum untuk elemen array
        const BAR_SPACING = 2; // Spasi antar bar
        const PADDING_X = 10; // Padding horizontal di kanvas
        const PADDING_Y = 10; // Padding vertikal di kanvas

        let array = [];
        let comparisonsCount = 0;
        let swapsCount = 0;
        let isSorting = false;
        let highlightedIndices = []; // Indeks bar yang sedang dibandingkan
        let swappingIndices = [];    // Indeks bar yang sedang ditukar
        let sortedIndices = [];      // Indeks bar yang sudah diurutkan

        // Fungsi untuk menginisialisasi ukuran kanvas agar responsif
        function resizeCanvas() {
            const containerWidth = canvas.parentElement.clientWidth;
            // Set canvas width attribute, not CSS width
            canvas.width = containerWidth - (2 * PADDING_X); 
            // Keep height fixed or calculate based on aspect ratio
            canvas.height = 400; // You can adjust this fixed height
            drawBars(); // Redraw after resizing
        }

        // Fungsi untuk menghasilkan array acak
        function generateRandomArray() {
            array = [];
            for (let i = 0; i < ARRAY_SIZE; i++) {
                const value = Math.floor(Math.random() * MAX_VALUE) + 5; // Nilai min 5 untuk visual
                array.push(value);
            }
            // Reset state
            comparisonsCount = 0;
            swapsCount = 0;
            highlightedIndices = [];
            swappingIndices = [];
            sortedIndices = [];
            comparisonsCountEl.textContent = comparisonsCount;
            swapsCountEl.textContent = swapsCount;
            drawBars(); // Gambar array awal
        }

        // Fungsi untuk menggambar semua bar di kanvas
        function drawBars() {
            ctx.clearRect(0, 0, canvas.width, canvas.height); // Bersihkan kanvas

            const barWidth = (canvas.width - (2 * PADDING_X) - (ARRAY_SIZE - 1) * BAR_SPACING) / ARRAY_SIZE;
            const maxHeight = canvas.height - (2 * PADDING_Y); // Ketinggian maksimum bar

            for (let i = 0; i < array.length; i++) {
                const value = array[i];
                const barHeight = (value / MAX_VALUE) * maxHeight;
                const x = PADDING_X + i * (barWidth + BAR_SPACING);
                const y = canvas.height - barHeight - PADDING_Y;

                // Tentukan warna bar berdasarkan status
                let color = '#4CAF50'; // Default green
                if (highlightedIndices.includes(i)) {
                    color = '#ff9800'; // Orange for comparing
                }
                if (swappingIndices.includes(i)) {
                    color = '#f44336'; // Red for swapping
                }
                if (sortedIndices.includes(i)) {
                    color = '#2196f3'; // Blue for sorted
                }

                // Gambar bar
                ctx.fillStyle = color;
                // Draw a rectangle with rounded corners (manual approach)
                const radius = 4;
                ctx.beginPath();
                ctx.moveTo(x + radius, y);
                ctx.lineTo(x + barWidth - radius, y);
                ctx.quadraticCurveTo(x + barWidth, y, x + barWidth, y + radius);
                ctx.lineTo(x + barWidth, y + barHeight);
                ctx.lineTo(x, y + barHeight);
                ctx.lineTo(x, y + radius);
                ctx.quadraticCurveTo(x, y, x + radius, y);
                ctx.closePath();
                ctx.fill();

                // Gambar nilai di atas bar
                ctx.fillStyle = '#ffffff';
                ctx.font = `${Math.max(10, barWidth / 2.5)}px Inter`; // Adjust font size based on bar width
                ctx.textAlign = 'center';
                ctx.textBaseline = 'bottom';
                ctx.fillText(value.toString(), x + barWidth / 2, y + barHeight - (barHeight > 20 ? 5 : -5)); // Position text
            }
        }

        // Fungsi utama Bubble Sort dengan animasi
        async function bubbleSort() {
            isSorting = true;
            startBtn.disabled = true; // Nonaktifkan tombol mulai saat sorting berjalan
            
            let n = array.length;
            let swapped;
            
            for (let i = 0; i < n - 1; i++) {
                swapped = false;
                
                for (let j = 0; j < n - i - 1; j++) {
                    comparisonsCount++;
                    comparisonsCountEl.textContent = comparisonsCount;
                    
                    // Tandai bar yang sedang dibandingkan
                    highlightedIndices = [j, j + 1];
                    drawBars();
                    await new Promise(resolve => setTimeout(resolve, ANIMATION_SPEED));
                    
                    if (array[j] > array[j + 1]) {
                        swapsCount++;
                        swapsCountEl.textContent = swapsCount;
                        
                        // Tandai bar yang sedang ditukar
                        swappingIndices = [j, j + 1];
                        highlightedIndices = []; // Clear comparison highlight
                        drawBars();
                        await new Promise(resolve => setTimeout(resolve, ANIMATION_SPEED * 1.5)); // Waktu lebih lama untuk swap
                        
                        // Tukar elemen dalam array
                        [array[j], array[j + 1]] = [array[j + 1], array[j]];
                        
                        drawBars(); // Gambar ulang setelah pertukaran
                        await new Promise(resolve => setTimeout(resolve, ANIMATION_SPEED * 0.5)); // Sedikit jeda setelah swap
                        
                        swapped = true;
                    }
                    swappingIndices = []; // Clear swap highlight
                    highlightedIndices = []; // Clear comparison highlight
                }
                
                // Tandai elemen terakhir yang sudah diurutkan di iterasi ini
                sortedIndices.push(n - 1 - i);
                drawBars();
                await new Promise(resolve => setTimeout(resolve, ANIMATION_SPEED * 2)); // Jeda setelah satu pass selesai

                // Jika tidak ada pertukaran di iterasi, array sudah diurutkan
                if (!swapped) {
                    // Tandai semua sisa bar sebagai sudah diurutkan
                    for (let k = 0; k < n - i - 1; k++) {
                        if (!sortedIndices.includes(k)) {
                            sortedIndices.push(k);
                        }
                    }
                    drawBars();
                    break;
                }
            }
            
            // Pastikan semua bar ditandai 'sorted' setelah selesai
            for (let k = 0; k < n; k++) {
                if (!sortedIndices.includes(k)) {
                    sortedIndices.push(k);
                }
            }
            drawBars(); // Gambar final state

            isSorting = false;
            startBtn.disabled = false;
            // Using custom alert instead of window.alert()
            showCustomAlert('Pengurutan Selesai!', 'blue');
        }

        // Fungsi untuk menampilkan pesan kustom
        function showCustomAlert(message, type = 'green') {
            const alertDiv = document.createElement('div');
            alertDiv.className = `fixed bottom-5 right-5 p-4 rounded-lg shadow-lg text-white ${type === 'green' ? 'bg-green-500' : type === 'red' ? 'bg-red-500' : 'bg-blue-500'}`;
            alertDiv.textContent = message;
            document.body.appendChild(alertDiv);
            setTimeout(() => {
                alertDiv.remove();
            }, 3000); // Hapus setelah 3 detik
        }

        // Fungsi untuk mereset simulasi
        function resetSimulation() {
            if (isSorting) {
                // Memberi tahu pengguna bahwa tidak dapat mereset saat sorting berlangsung
                showCustomAlert('Tidak bisa mereset saat simulasi berjalan. Harap tunggu.', 'red');
                return;
            }
            generateRandomArray();
            startBtn.disabled = false;
        }

        // Event listener untuk tombol
        startBtn.addEventListener('click', () => {
            if (!isSorting) {
                bubbleSort();
            }
        });

        resetBtn.addEventListener('click', resetSimulation);

        // Event listener untuk penyesuaian ukuran kanvas saat jendela diubah ukurannya
        window.addEventListener('resize', resizeCanvas);

        // Inisialisasi awal saat halaman dimuat
        document.addEventListener('DOMContentLoaded', () => {
            resizeCanvas(); // Set initial size
            generateRandomArray();
        });
    </script>
</body>
</html>
