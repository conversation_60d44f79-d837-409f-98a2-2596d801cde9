#!/bin/bash

# SantriMental Development Setup Script
echo "🚀 Setting up SantriMental Development Environment..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if we're in the right directory
if [ ! -f "README.md" ] || [ ! -d "backend" ]; then
    print_error "Please run this script from the project root directory"
    exit 1
fi

# Check prerequisites
print_status "Checking prerequisites..."

# Check PHP
if ! command -v php &> /dev/null; then
    print_error "PHP is not installed. Please install PHP 8.1 or higher."
    exit 1
fi

PHP_VERSION=$(php -v | head -n1 | cut -d' ' -f2 | cut -d'.' -f1,2)
if [ "$(printf '%s\n' "8.1" "$PHP_VERSION" | sort -V | head -n1)" != "8.1" ]; then
    print_error "PHP 8.1 or higher is required. Current version: $PHP_VERSION"
    exit 1
fi

print_success "PHP $PHP_VERSION found"

# Check Composer
if ! command -v composer &> /dev/null; then
    print_error "Composer is not installed. Please install Composer."
    exit 1
fi

print_success "Composer found"

# Check MySQL
if ! command -v mysql &> /dev/null; then
    print_warning "MySQL client not found. Make sure MySQL server is running."
fi

# Setup Backend
print_status "Setting up Laravel backend..."

cd backend

# Install PHP dependencies
print_status "Installing PHP dependencies..."
composer install

if [ $? -ne 0 ]; then
    print_error "Failed to install PHP dependencies"
    exit 1
fi

print_success "PHP dependencies installed"

# Copy environment file
if [ ! -f ".env" ]; then
    print_status "Creating environment file..."
    cp .env.example .env
    print_success "Environment file created"
else
    print_warning "Environment file already exists"
fi

# Generate application key
print_status "Generating application key..."
php artisan key:generate

# Create database if it doesn't exist
print_status "Setting up database..."

# Ask for database credentials
echo ""
print_status "Database Configuration"
read -p "Enter database host (default: 127.0.0.1): " DB_HOST
DB_HOST=${DB_HOST:-127.0.0.1}

read -p "Enter database port (default: 3306): " DB_PORT
DB_PORT=${DB_PORT:-3306}

read -p "Enter database name (default: santrimental): " DB_NAME
DB_NAME=${DB_NAME:-santrimental}

read -p "Enter database username (default: root): " DB_USER
DB_USER=${DB_USER:-root}

read -s -p "Enter database password: " DB_PASS
echo ""

# Update .env file
print_status "Updating environment configuration..."
sed -i.bak "s/DB_HOST=.*/DB_HOST=$DB_HOST/" .env
sed -i.bak "s/DB_PORT=.*/DB_PORT=$DB_PORT/" .env
sed -i.bak "s/DB_DATABASE=.*/DB_DATABASE=$DB_NAME/" .env
sed -i.bak "s/DB_USERNAME=.*/DB_USERNAME=$DB_USER/" .env
sed -i.bak "s/DB_PASSWORD=.*/DB_PASSWORD=$DB_PASS/" .env

# Test database connection
print_status "Testing database connection..."
php artisan migrate:status &> /dev/null

if [ $? -ne 0 ]; then
    print_error "Cannot connect to database. Please check your credentials."
    exit 1
fi

print_success "Database connection successful"

# Run migrations
print_status "Running database migrations..."
php artisan migrate

if [ $? -ne 0 ]; then
    print_error "Failed to run migrations"
    exit 1
fi

print_success "Database migrations completed"

# Run seeders
print_status "Seeding database..."
php artisan db:seed --class=UserSeeder
php artisan db:seed --class=FormTemplateSeeder

if [ $? -ne 0 ]; then
    print_warning "Some seeders failed, but continuing..."
else
    print_success "Database seeding completed"
fi

# Create storage link
print_status "Creating storage link..."
php artisan storage:link

# Set permissions
print_status "Setting file permissions..."
chmod -R 775 storage bootstrap/cache

# Install Node.js dependencies (if package.json exists)
if [ -f "package.json" ]; then
    print_status "Installing Node.js dependencies..."
    
    if command -v npm &> /dev/null; then
        npm install
        npm run build
        print_success "Node.js dependencies installed and assets built"
    else
        print_warning "npm not found. Skipping Node.js dependencies."
    fi
fi

cd ..

# Create demo data
print_status "Creating demo user..."
cd backend
php artisan tinker --execute="
\$user = \App\Models\User::firstOrCreate(
    ['email' => '<EMAIL>'],
    [
        'name' => 'Demo User',
        'password' => bcrypt('demo123'),
        'email_verified_at' => now()
    ]
);
echo 'Demo user created: ' . \$user->email . PHP_EOL;
"

cd ..

# Final setup
print_status "Final setup..."

# Create necessary directories
mkdir -p logs
mkdir -p uploads

print_success "Setup completed successfully!"

echo ""
echo "🎉 SantriMental is ready for development!"
echo ""
echo "📋 Next steps:"
echo "1. Start the development server:"
echo "   cd backend && php artisan serve"
echo ""
echo "2. Open your browser and visit:"
echo "   http://127.0.0.1:8000"
echo ""
echo "3. Login with demo credentials:"
echo "   Email: <EMAIL>"
echo "   Password: demo123"
echo ""
echo "📚 Documentation:"
echo "   - API docs: http://127.0.0.1:8000/api/documentation"
echo "   - README: ./README.md"
echo ""
echo "🐛 Troubleshooting:"
echo "   - Check logs: ./backend/storage/logs/"
echo "   - Database issues: Check .env configuration"
echo "   - Permission issues: Run 'chmod -R 775 backend/storage backend/bootstrap/cache'"
echo ""

print_success "Happy coding! 🚀"
