Here is the comprehensive test plan to ensure all features and functions of the SantriMental application are working properly:

1. Backend API Testing:
   - Test authentication endpoints: register, login, get user profile, logout.
   - Test assessment endpoints: submit assessment, get assessment history, get dashboard statistics.

2. Frontend Testing:
   - Authentication flow: registration, login, session management, Google OAuth, QR code login.
   - SRQ-20 assessment: form rendering, validation, submission, result display, recommendation.
   - Assessment history: listing, pagination, filtering, detail view.
   - Dashboard: statistics display, real-time updates, loading and error states.
   - Offline/mock mode: API health check, fallback to mock data, notifications, data consistency.

3. Integration Testing:
   - End-to-end flows: registration to assessment submission, login to dashboard, profile updates.
   - Error handling: network, API, validation, authentication errors.
   - Performance: load time, response time, animation smoothness, memory usage.

4. Cross-browser Testing:
   - Chrome, Firefox, Safari, Edge.

5. Responsive Design Testing:
   - Desktop, laptop, tablet, mobile screen sizes.

6. Security Testing:
   - Authentication token validation, CSRF, XSS, SQL injection prevention.
   - Data protection: encryption, secure communication, access control.

Please confirm if you approve this test plan so I can proceed with executing the tests.
