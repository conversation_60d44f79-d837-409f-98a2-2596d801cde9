<?php

use App\Http\Controllers\Api\AuthController;
use App\Http\Controllers\Api\AssessmentController;
use App\Http\Controllers\Api\FormTemplateController;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
*/

// Public routes
Route::post('/register', [AuthController::class, 'register']);
Route::post('/login', [AuthController::class, 'login']);
Route::post('/auth/google', [AuthController::class, 'googleAuth']);
Route::post('/auth/qr', [AuthController::class, 'qrLogin']);

// Protected routes
Route::middleware('auth:sanctum')->group(function () {
    // Auth routes
    Route::get('/user', [AuthController::class, 'user']);
    Route::post('/logout', [AuthController::class, 'logout']);

    // Assessment routes (legacy)
    Route::prefix('assessments')->group(function () {
        Route::get('/', [AssessmentController::class, 'index']);
        Route::post('/', [AssessmentController::class, 'store']);
        Route::get('/questions', [AssessmentController::class, 'questions']);
        Route::get('/dashboard-stats', [AssessmentController::class, 'dashboardStats']);
        Route::get('/monthly-report', [AssessmentController::class, 'monthlyReport']);
        Route::get('/{assessment}', [AssessmentController::class, 'show']);
    });

    // Dynamic Form System routes
    Route::prefix('forms')->group(function () {
        Route::get('/', [FormTemplateController::class, 'index']);
        Route::get('/{code}', [FormTemplateController::class, 'show']);
        Route::post('/{code}/submit', [FormTemplateController::class, 'submitResponse']);
        Route::get('/{code}/responses', [FormTemplateController::class, 'getUserResponses']);
    });

    // Dashboard stats (new dynamic system)
    Route::get('/dashboard/stats', [FormTemplateController::class, 'getDashboardStats']);
});

// Fallback for undefined routes
Route::fallback(function () {
    return response()->json([
        'message' => 'Route not found.'
    ], 404);
});
