<?php

use App\Http\Controllers\Api\AuthController;
use App\Http\Controllers\Api\AssessmentController;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
*/

// Public routes
Route::post('/register', [AuthController::class, 'register']);
Route::post('/login', [AuthController::class, 'login']);
Route::post('/auth/google', [AuthController::class, 'googleAuth']);
Route::post('/auth/qr', [AuthController::class, 'qrLogin']);

// Protected routes
Route::middleware('auth:sanctum')->group(function () {
    // Auth routes
    Route::get('/user', [AuthController::class, 'user']);
    Route::post('/logout', [AuthController::class, 'logout']);

    // Assessment routes
    Route::prefix('assessments')->group(function () {
        Route::get('/', [AssessmentController::class, 'index']);
        Route::post('/', [AssessmentController::class, 'store']);
        Route::get('/questions', [AssessmentController::class, 'questions']);
        Route::get('/dashboard-stats', [AssessmentController::class, 'dashboardStats']);
        Route::get('/monthly-report', [AssessmentController::class, 'monthlyReport']);
        Route::get('/{assessment}', [AssessmentController::class, 'show']);
    });
});

// Fallback for undefined routes
Route::fallback(function () {
    return response()->json([
        'message' => 'Route not found.'
    ], 404);
});
