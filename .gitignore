# Laravel Backend
/backend/vendor/
/backend/node_modules/
/backend/public/hot
/backend/public/storage
/backend/storage/*.key
/backend/.env
/backend/.env.backup
/backend/.env.production
/backend/.phpunit.result.cache
/backend/Homestead.json
/backend/Homestead.yaml
/backend/auth.json
/backend/npm-debug.log
/backend/yarn-error.log
/backend/.fleet
/backend/.idea
/backend/.vscode

# Laravel specific
/backend/bootstrap/cache/*.php
/backend/storage/app/*
!/backend/storage/app/.gitkeep
/backend/storage/framework/cache/*
!/backend/storage/framework/cache/.gitkeep
/backend/storage/framework/sessions/*
!/backend/storage/framework/sessions/.gitkeep
/backend/storage/framework/testing/*
!/backend/storage/framework/testing/.gitkeep
/backend/storage/framework/views/*
!/backend/storage/framework/views/.gitkeep
/backend/storage/logs/*
!/backend/storage/logs/.gitkeep

# Frontend Assets
/frontend/node_modules/
/frontend/dist/
/frontend/build/
/frontend/.cache/
/frontend/.parcel-cache/
/frontend/.next/
/frontend/out/

# Static Files (keep original HTML for reference but ignore compiled versions)
/compiled/
/dist/
/build/

# Environment files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
.env.backup

# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*
.pnpm-debug.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output

# Dependency directories
node_modules/
jspm_packages/

# Snowpack dependency directory (https://snowpack.dev/)
web_modules/

# TypeScript cache
*.tsbuildinfo

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Optional stylelint cache
.stylelintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variable files
.env.development.local
.env.test.local
.env.production.local
.env.local

# parcel-bundler cache (https://parceljs.org/)
.cache
.parcel-cache

# Next.js build output
.next

# Nuxt.js build / generate output
.nuxt
dist

# Gatsby files
.cache/
public

# Vuepress build output
.vuepress/dist

# Serverless directories
.serverless/

# FuseBox cache
.fusebox/

# DynamoDB Local files
.dynamodb/

# TernJS port file
.tern-port

# Stores VSCode versions used for testing VSCode extensions
.vscode-test

# yarn v2
.yarn/cache
.yarn/unplugged
.yarn/build-state.yml
.yarn/install-state.gz
.pnp.*

# IDE and Editor files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Windows
*.exe
*.msi
*.msm
*.msp

# Temporary files
*.tmp
*.temp
temp/
tmp/

# Database
*.sqlite
*.sqlite3
*.db

# Backup files
*.bak
*.backup

# Archive files
*.zip
*.tar.gz
*.rar

# PHP specific
composer.phar
/vendor/
.phpunit.result.cache

# Laravel Mix
/public/hot
/public/storage
/storage/*.key

# Telescope
/storage/telescope/

# Ignition
/storage/ignition/

# Documentation build
/docs/_build/

# Testing
/coverage/
.phpunit.cache

# Local development
/local/
/dev/

# Deployment
/deploy/
/releases/

# Custom uploads (but keep structure)
/uploads/*
!/uploads/.gitkeep

# Generated files
/generated/
/auto-generated/

# Cache files
*.cache
.cache/

# Lock files (uncomment if you want to ignore them)
# package-lock.json
# yarn.lock
# composer.lock

# Local configuration
local.config.js
local.config.php

# Sensitive data
/secrets/
/keys/
*.pem
*.key
*.crt

# Development tools
.phpcs.xml
.php_cs.cache
.php-cs-fixer.cache

# Vagrant
.vagrant/

# Docker
.docker/
docker-compose.override.yml

# Kubernetes
*.kubeconfig

# Terraform
*.tfstate
*.tfstate.*
.terraform/

# AWS
.aws/

# Google Cloud
.gcloud/

# Azure
.azure/
