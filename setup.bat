@echo off
setlocal enabledelayedexpansion

:: SantriMental Development Setup Script for Windows
echo 🚀 Setting up SantriMental Development Environment...
echo.

:: Check if we're in the right directory
if not exist "README.md" (
    echo [ERROR] Please run this script from the project root directory
    pause
    exit /b 1
)

if not exist "backend" (
    echo [ERROR] Backend directory not found
    pause
    exit /b 1
)

:: Check prerequisites
echo [INFO] Checking prerequisites...

:: Check PHP
php -v >nul 2>&1
if errorlevel 1 (
    echo [ERROR] PHP is not installed or not in PATH
    echo Please install PHP 8.1 or higher and add it to your PATH
    pause
    exit /b 1
)

echo [SUCCESS] PHP found

:: Check Composer
composer --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Composer is not installed or not in PATH
    echo Please install Composer and add it to your PATH
    pause
    exit /b 1
)

echo [SUCCESS] Composer found

:: Setup Backend
echo [INFO] Setting up Laravel backend...
cd backend

:: Install PHP dependencies
echo [INFO] Installing PHP dependencies...
composer install

if errorlevel 1 (
    echo [ERROR] Failed to install PHP dependencies
    pause
    exit /b 1
)

echo [SUCCESS] PHP dependencies installed

:: Copy environment file
if not exist ".env" (
    echo [INFO] Creating environment file...
    copy .env.example .env
    echo [SUCCESS] Environment file created
) else (
    echo [WARNING] Environment file already exists
)

:: Generate application key
echo [INFO] Generating application key...
php artisan key:generate

:: Database Configuration
echo.
echo [INFO] Database Configuration
echo Please enter your database details:
echo.

set /p DB_HOST="Enter database host (default: 127.0.0.1): "
if "%DB_HOST%"=="" set DB_HOST=127.0.0.1

set /p DB_PORT="Enter database port (default: 3306): "
if "%DB_PORT%"=="" set DB_PORT=3306

set /p DB_NAME="Enter database name (default: santrimental): "
if "%DB_NAME%"=="" set DB_NAME=santrimental

set /p DB_USER="Enter database username (default: root): "
if "%DB_USER%"=="" set DB_USER=root

set /p DB_PASS="Enter database password: "

:: Update .env file
echo [INFO] Updating environment configuration...
powershell -Command "(gc .env) -replace 'DB_HOST=.*', 'DB_HOST=%DB_HOST%' | Out-File -encoding ASCII .env"
powershell -Command "(gc .env) -replace 'DB_PORT=.*', 'DB_PORT=%DB_PORT%' | Out-File -encoding ASCII .env"
powershell -Command "(gc .env) -replace 'DB_DATABASE=.*', 'DB_DATABASE=%DB_NAME%' | Out-File -encoding ASCII .env"
powershell -Command "(gc .env) -replace 'DB_USERNAME=.*', 'DB_USERNAME=%DB_USER%' | Out-File -encoding ASCII .env"
powershell -Command "(gc .env) -replace 'DB_PASSWORD=.*', 'DB_PASSWORD=%DB_PASS%' | Out-File -encoding ASCII .env"

:: Test database connection
echo [INFO] Testing database connection...
php artisan migrate:status >nul 2>&1

if errorlevel 1 (
    echo [ERROR] Cannot connect to database. Please check your credentials.
    pause
    exit /b 1
)

echo [SUCCESS] Database connection successful

:: Run migrations
echo [INFO] Running database migrations...
php artisan migrate

if errorlevel 1 (
    echo [ERROR] Failed to run migrations
    pause
    exit /b 1
)

echo [SUCCESS] Database migrations completed

:: Run seeders
echo [INFO] Seeding database...
php artisan db:seed --class=UserSeeder
php artisan db:seed --class=FormTemplateSeeder

echo [SUCCESS] Database seeding completed

:: Create storage link
echo [INFO] Creating storage link...
php artisan storage:link

:: Install Node.js dependencies (if package.json exists)
if exist "package.json" (
    echo [INFO] Installing Node.js dependencies...
    
    npm --version >nul 2>&1
    if not errorlevel 1 (
        npm install
        npm run build
        echo [SUCCESS] Node.js dependencies installed and assets built
    ) else (
        echo [WARNING] npm not found. Skipping Node.js dependencies.
    )
)

:: Create demo user
echo [INFO] Creating demo user...
php artisan tinker --execute="$user = \App\Models\User::firstOrCreate(['email' => '<EMAIL>'], ['name' => 'Demo User', 'password' => bcrypt('demo123'), 'email_verified_at' => now()]); echo 'Demo user created: ' . $user->email . PHP_EOL;"

cd ..

:: Create necessary directories
if not exist "logs" mkdir logs
if not exist "uploads" mkdir uploads

echo.
echo [SUCCESS] Setup completed successfully!
echo.
echo 🎉 SantriMental is ready for development!
echo.
echo 📋 Next steps:
echo 1. Start the development server:
echo    cd backend ^&^& php artisan serve
echo.
echo 2. Open your browser and visit:
echo    http://127.0.0.1:8000
echo.
echo 3. Login with demo credentials:
echo    Email: <EMAIL>
echo    Password: demo123
echo.
echo 📚 Documentation:
echo    - API docs: http://127.0.0.1:8000/api/documentation
echo    - README: ./README.md
echo.
echo 🐛 Troubleshooting:
echo    - Check logs: ./backend/storage/logs/
echo    - Database issues: Check .env configuration
echo.
echo [SUCCESS] Happy coding! 🚀

pause
