<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Aplikasi Logika Fuzzy Pemilihan Gadget</title>
    <!-- Tailwind CSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
        body {
            font-family: 'Inter', sans-serif;
            background-color: #f3f4f6; /* Light gray background */
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }
        .card {
            background-color: #ffffff;
            border-radius: 0.75rem; /* rounded-xl */
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            padding: 1.5rem;
        }
        .input-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 500;
            color: #374151; /* Gray-700 */
        }
        .input-group input[type="number"] {
            width: 100%;
            padding: 0.75rem 1rem;
            border: 1px solid #d1d5db; /* Gray-300 */
            border-radius: 0.5rem; /* rounded-lg */
            font-size: 1rem;
            line-height: 1.5rem;
            transition: border-color 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
        }
        .input-group input[type="number"]:focus {
            outline: none;
            border-color: #2563eb; /* Blue-600 */
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.5); /* Blue-300 with opacity */
        }
        .btn {
            padding: 0.75rem 1.5rem;
            border-radius: 0.5rem;
            font-weight: 600;
            cursor: pointer;
            transition: background-color 0.2s ease-in-out, transform 0.1s ease-in-out;
            border: none;
        }
        .btn-primary {
            background-color: #2563eb; /* Blue-600 */
            color: #ffffff;
        }
        .btn-primary:hover {
            background-color: #1d4ed8; /* Blue-700 */
            transform: translateY(-1px);
        }
        .btn-primary:active {
            transform: translateY(0);
        }
        .tab-button {
            padding: 0.75rem 1.5rem;
            border-radius: 0.5rem;
            font-weight: 600;
            cursor: pointer;
            background-color: #e5e7eb; /* Gray-200 */
            color: #4b5563; /* Gray-600 */
            transition: background-color 0.2s ease-in-out, color 0.2s ease-in-out;
        }
        .tab-button.active {
            background-color: #2563eb; /* Blue-600 */
            color: #ffffff;
        }
        canvas {
            border: 1px solid #d1d5db;
            border-radius: 0.5rem;
            background-color: #f9fafb;
            margin-top: 1rem;
        }
        .output-box {
            background-color: #eff6ff; /* Blue-50 */
            border: 1px solid #bfdbfe; /* Blue-200 */
            border-radius: 0.5rem;
            padding: 1rem;
            font-weight: 600;
            color: #1e40af; /* Blue-800 */
            text-align: center;
        }
    </style>
</head>
<body class="bg-gray-100 text-gray-800 antialiased">
    <div class="container py-8">
        <h1 class="text-4xl font-bold text-center mb-8 text-gray-900">
            Logika Fuzzy untuk Pemilihan Gadget
        </h1>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <!-- Input Card -->
            <div class="card col-span-1 md:col-span-1 lg:col-span-1">
                <h2 class="text-2xl font-semibold mb-6 text-gray-800">Input Parameter Gadget</h2>
                <div class="space-y-6">
                    <div class="input-group">
                        <label for="harga">Harga (Juta IDR):</label>
                        <input type="number" id="harga" value="7.5" min="0" max="15" step="0.1"
                               class="focus:ring-blue-500 focus:border-blue-500">
                    </div>
                    <div class="input-group">
                        <label for="spesifikasi">Spesifikasi (Skala 0-100):</label>
                        <input type="number" id="spesifikasi" value="70" min="0" max="100"
                               class="focus:ring-blue-500 focus:border-blue-500">
                    </div>
                    <div class="input-group">
                        <label for="kamera">Kualitas Kamera (Skala 0-100):</label>
                        <input type="number" id="kamera" value="60" min="0" max="100"
                               class="focus:ring-blue-500 focus:border-blue-500">
                    </div>
                    <div class="input-group">
                        <label for="baterai">Daya Tahan Baterai (Skala 0-100):</label>
                        <input type="number" id="baterai" value="80" min="0" max="100"
                               class="focus:ring-blue-500 focus:border-blue-500">
                    </div>
                </div>
                <button id="calculateBtn" class="btn btn-primary w-full mt-8">
                    Hitung Rekomendasi
                </button>
            </div>

            <!-- Output and Method Selection Card -->
            <div class="card col-span-1 md:col-span-1 lg:col-span-2">
                <h2 class="text-2xl font-semibold mb-6 text-gray-800">Metode & Hasil Rekomendasi</h2>

                <div class="flex space-x-4 mb-6 justify-center">
                    <button id="tabMamdani" class="tab-button active">Mamdani</button>
                    <button id="tabSugeno" class="tab-button">Sugeno</button>
                    <button id="tabTsukamoto" class="tab-button">Tsukamoto</button>
                </div>

                <div class="output-box mb-6">
                    <p class="text-xl">Rekomendasi (Mamdani): <span id="resultMamdani" class="font-bold text-2xl">--</span></p>
                </div>
                <div class="output-box mb-6 hidden">
                    <p class="text-xl">Rekomendasi (Sugeno): <span id="resultSugeno" class="font-bold text-2xl">--</span></p>
                </div>
                <div class="output-box mb-6 hidden">
                    <p class="text-xl">Rekomendasi (Tsukamoto): <span id="resultTsukamoto" class="font-bold text-2xl">--</span></p>
                </div>
            </div>

            <!-- Fuzzy Functions Visualization Cards -->
            <div class="card col-span-1 md:col-span-2 lg:col-span-3">
                <h2 class="text-2xl font-semibold mb-6 text-gray-800">Visualisasi Fungsi Keanggotaan</h2>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <h3 class="text-lg font-medium text-gray-700 mb-2">Harga (Juta IDR)</h3>
                        <canvas id="canvasHarga" width="400" height="200"></canvas>
                    </div>
                    <div>
                        <h3 class="text-lg font-medium text-gray-700 mb-2">Spesifikasi (Skala 0-100)</h3>
                        <canvas id="canvasSpesifikasi" width="400" height="200"></canvas>
                    </div>
                    <div>
                        <h3 class="text-lg font-medium text-gray-700 mb-2">Kualitas Kamera (Skala 0-100)</h3>
                        <canvas id="canvasKamera" width="400" height="200"></canvas>
                    </div>
                    <div>
                        <h3 class="text-lg font-medium text-gray-700 mb-2">Daya Tahan Baterai (Skala 0-100)</h3>
                        <canvas id="canvasBaterai" width="400" height="200"></canvas>
                    </div>
                    <div class="md:col-span-2">
                        <h3 class="text-lg font-medium text-gray-700 mb-2">Rekomendasi (Skala 0-100 - Output Fuzzy)</h3>
                        <canvas id="canvasRekomendasi" width="800" height="200"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // --- Membership Functions ---
        // Triangular membership function
        function triangle(x, a, b, c) {
            if (x >= a && x <= b) {
                return (x - a) / (b - a);
            } else if (x > b && x <= c) {
                return (c - x) / (c - b);
            } else {
                return 0;
            }
        }

        // Trapezoidal membership function
        function trapezoid(x, a, b, c, d) {
            if (x >= a && x <= b) {
                return (x - a) / (b - a);
            } else if (x > b && x <= c) {
                return 1;
            } else if (x > c && x <= d) {
                return (d - x) / (d - c);
            } else {
                return 0;
            }
        }

        // Left-shoulder membership function (ramp up then plateau)
        function leftShoulder(x, a, b) {
            if (x <= a) {
                return 1;
            } else if (x > a && x < b) {
                return (b - x) / (b - a);
            } else {
                return 0;
            }
        }

        // Right-shoulder membership function (plateau then ramp down)
        function rightShoulder(x, a, b) {
            if (x >= b) {
                return 1;
            } else if (x > a && x < b) {
                return (x - a) / (b - a);
            } else {
                return 0;
            }
        }

        // --- Fuzzification ---
        function fuzzifyHarga(harga) {
            return {
                murah: leftShoulder(harga, 3, 7), // 0-7, 1 at 0-3, 0 at 7
                sedang: triangle(harga, 5, 9, 13), // 5-13, 1 at 9
                mahal: rightShoulder(harga, 10, 15) // 10-inf, 1 at 15-inf, 0 at 10
            };
        }

        function fuzzifySpesifikasi(spec) {
            return {
                rendah: leftShoulder(spec, 30, 60),
                cukup: triangle(spec, 40, 70, 90),
                tinggi: rightShoulder(spec, 75, 100)
            };
        }

        function fuzzifyKamera(kamera) {
            return {
                buruk: leftShoulder(kamera, 20, 50),
                cukup: triangle(kamera, 40, 70, 90),
                bagus: rightShoulder(kamera, 70, 100)
            };
        }

        function fuzzifyBaterai(baterai) {
            return {
                lemah: leftShoulder(baterai, 30, 60),
                sedang: triangle(baterai, 50, 75, 95),
                kuat: rightShoulder(baterai, 80, 100)
            };
        }

        // --- Fuzzy Rules (Simplified for demonstration) ---
        // Rules will return a an object with the degree of membership for each output fuzzy set
        // e.g., { sangatBuruk: 0, buruk: 0.2, cukup: 0.8, baik: 0, sangatBaik: 0 }
        function applyRules(fuzzyInputs) {
            const { harga, spesifikasi, kamera, baterai } = fuzzyInputs;
            const activation = {};

            // Rule 1: IF Harga Murah AND Spesifikasi Tinggi AND Kamera Bagus AND Baterai Kuat THEN Sangat Baik
            activation.sangatBaik1 = Math.min(harga.murah, spesifikasi.tinggi, kamera.bagus, baterai.kuat);

            // Rule 2: IF Harga Murah AND Spesifikasi Cukup AND Baterai Kuat THEN Baik
            activation.baik1 = Math.min(harga.murah, spesifikasi.cukup, baterai.kuat);

            // Rule 3: IF Harga Sedang AND Spesifikasi Tinggi AND Kamera Bagus THEN Baik
            activation.baik2 = Math.min(harga.sedang, spesifikasi.tinggi, kamera.bagus);

            // Rule 4: IF Harga Sedang AND Spesifikasi Cukup THEN Cukup
            activation.cukup1 = Math.min(harga.sedang, spesifikasi.cukup);

            // Rule 5: IF Harga Mahal AND Spesifikasi Rendah THEN Buruk
            activation.buruk1 = Math.min(harga.mahal, spesifikasi.rendah);

            // Rule 6: IF Harga Mahal AND Spesifikasi Cukup AND Kamera Buruk THEN Sangat Buruk
            activation.sangatBuruk1 = Math.min(harga.mahal, spesifikasi.cukup, kamera.buruk);

            // Rule 7: IF Spesifikasi Rendah AND Baterai Lemah THEN Sangat Buruk
            activation.sangatBuruk2 = Math.min(spesifikasi.rendah, baterai.lemah);

            // Rule 8: IF Harga Murah AND Spesifikasi Rendah THEN Cukup
            activation.cukup2 = Math.min(harga.murah, spesifikasi.rendah);

            // Rule 9: IF Kamera Buruk AND Baterai Lemah THEN Buruk
            activation.buruk2 = Math.min(kamera.buruk, baterai.lemah);

            // Aggregate activation for each output fuzzy set
            return {
                sangatBuruk: Math.max(activation.sangatBuruk1, activation.sangatBuruk2),
                buruk: Math.max(activation.buruk1, activation.buruk2),
                cukup: Math.max(activation.cukup1, activation.cukup2),
                baik: Math.max(activation.baik1, activation.baik2),
                sangatBaik: activation.sangatBaik1
            };
        }

        // --- Mamdani Method ---
        function mamdaniDefuzzification(aggregatedOutput) {
            const outputRange = {
                sangatBuruk: [0, 20],
                buruk: [10, 40],
                cukup: [30, 70],
                baik: [60, 90],
                sangatBaik: [80, 100]
            };

            const interval = 1; // Step for integration
            let numerator = 0;
            let denominator = 0;

            // Iterate through the whole output universe of discourse
            for (let x = 0; x <= 100; x += interval) {
                // Get membership degrees for each fuzzy set at point x
                const muSangatBuruk = triangle(x, 0, 10, 20); // Simplified for Mamdani aggregation
                const muBuruk = triangle(x, 10, 25, 40);
                const muCukup = triangle(x, 30, 50, 70);
                const muBaik = triangle(x, 60, 80, 90);
                const muSangatBaik = triangle(x, 80, 90, 100);

                // Clipped membership values for each output fuzzy set
                const clippedSangatBuruk = Math.min(aggregatedOutput.sangatBuruk, muSangatBuruk);
                const clippedBuruk = Math.min(aggregatedOutput.buruk, muBuruk);
                const clippedCukup = Math.min(aggregatedOutput.cukup, muCukup);
                const clippedBaik = Math.min(aggregatedOutput.baik, muBaik);
                const clippedSangatBaik = Math.min(aggregatedOutput.sangatBaik, muSangatBaik);

                // Take the maximum of all clipped memberships at this point x
                const overallMembership = Math.max(
                    clippedSangatBuruk, clippedBuruk, clippedCukup, clippedBaik, clippedSangatBaik
                );

                numerator += x * overallMembership;
                denominator += overallMembership;
            }

            return denominator === 0 ? 0 : numerator / denominator;
        }

        // --- Sugeno Method (Order 0) ---
        // Outputs are crisp values defined for each rule consequent
        function sugenoDefuzzification(fuzzyInputs) {
            const { harga, spesifikasi, kamera, baterai } = fuzzyInputs;
            let numerator = 0;
            let denominator = 0;

            // Consequents (crisp values for each rule)
            const z = {
                sangatBaik: 90, // for rule 1
                baik1: 75,     // for rule 2
                baik2: 70,     // for rule 3
                cukup1: 50,    // for rule 4
                buruk1: 25,    // for rule 5
                sangatBuruk1: 10, // for rule 6
                sangatBuruk2: 5,  // for rule 7
                cukup2: 45,    // for rule 8
                buruk2: 20     // for rule 9
            };

            // Calculate firing strength (alpha-cut) for each rule
            const alpha = {
                r1: Math.min(harga.murah, spesifikasi.tinggi, kamera.bagus, baterai.kuat), // Sangat Baik
                r2: Math.min(harga.murah, spesifikasi.cukup, baterai.kuat), // Baik
                r3: Math.min(harga.sedang, spesifikasi.tinggi, kamera.bagus), // Baik
                r4: Math.min(harga.sedang, spesifikasi.cukup), // Cukup
                r5: Math.min(harga.mahal, spesifikasi.rendah), // Buruk
                r6: Math.min(harga.mahal, spesifikasi.cukup, kamera.buruk), // Sangat Buruk
                r7: Math.min(spesifikasi.rendah, baterai.lemah), // Sangat Buruk
                r8: Math.min(harga.murah, spesifikasi.rendah), // Cukup
                r9: Math.min(kamera.buruk, baterai.lemah) // Buruk
            };

            // Sum of (alpha * z) and sum of alpha
            numerator += alpha.r1 * z.sangatBaik;
            numerator += alpha.r2 * z.baik1;
            numerator += alpha.r3 * z.baik2;
            numerator += alpha.r4 * z.cukup1;
            numerator += alpha.r5 * z.buruk1;
            numerator += alpha.r6 * z.sangatBuruk1;
            numerator += alpha.r7 * z.sangatBuruk2;
            numerator += alpha.r8 * z.cukup2;
            numerator += alpha.r9 * z.buruk2;

            denominator += alpha.r1;
            denominator += alpha.r2;
            denominator += alpha.r3;
            denominator += alpha.r4;
            denominator += alpha.r5;
            denominator += alpha.r6;
            denominator += alpha.r7;
            denominator += alpha.r8;
            denominator += alpha.r9;

            return denominator === 0 ? 0 : numerator / denominator;
        }

        // --- Tsukamoto Method ---
        // Requires monotonically decreasing/increasing output membership functions
        function inverseTriangle(y, a, b, c) {
            if (y >= 0 && y <= 1) {
                if (y >= (b - a) / (c - a) && y <= 1) {
                    // Right side of triangle
                    return c - y * (c - b);
                } else {
                    // Left side of triangle
                    return a + y * (b - a);
                }
            }
            return null; // y out of range [0, 1]
        }

        function inverseRightShoulder(y, a, b) {
            // Monotonically increasing from a to b
            // y = (x - a) / (b - a) => x = a + y * (b - a)
            if (y >= 0 && y <= 1) {
                return a + y * (b - a);
            }
            return null;
        }

        function inverseLeftShoulder(y, a, b) {
            // Monotonically decreasing from a to b
            // y = (b - x) / (b - a) => x = b - y * (b - a)
            if (y >= 0 && y <= 1) {
                return b - y * (b - a);
            }
            return null;
        }


        function tsukamotoDefuzzification(fuzzyInputs) {
            const { harga, spesifikasi, kamera, baterai } = fuzzyInputs;
            let numerator = 0;
            let denominator = 0;

            // Output fuzzy sets for Tsukamoto MUST be monotonic.
            // Let's define them as linear segments for simplicity.
            // Example:
            // Sangat Buruk: Starts high, ends low (e.g., from 20 down to 0 for membership 0 to 1) -> decreasing
            // Buruk: decreasing
            // Cukup: decreasing
            // Baik: increasing
            // Sangat Baik: increasing

            // We need the *inverse* membership functions (y to x) for Tsukamoto
            // Define Tsukamoto output membership functions to be monotonic
            // Let's assume for simplicity, Sangat Buruk, Buruk, Cukup are decreasing, Baik, Sangat Baik are increasing

            // Inverse functions to get crisp x from a given alpha (y)
            const getXFromAlpha = {
                sangatBuruk: (alpha) => inverseLeftShoulder(alpha, 0, 20), // Monotonically decreasing from 0 to 20
                buruk: (alpha) => inverseLeftShoulder(alpha, 20, 50),     // Monotonically decreasing from 20 to 50
                cukup: (alpha) => inverseLeftShoulder(alpha, 40, 70),     // Monotonically decreasing from 40 to 70
                baik: (alpha) => inverseRightShoulder(alpha, 60, 90),     // Monotonically increasing from 60 to 90
                sangatBaik: (alpha) => inverseRightShoulder(alpha, 80, 100) // Monotonically increasing from 80 to 100
            };

            // --- Rules for Tsukamoto (example, similar structure to Mamdani/Sugeno) ---
            const rules = [
                {
                    antecedent: [harga.murah, spesifikasi.tinggi, kamera.bagus, baterai.kuat],
                    consequent: 'sangatBaik'
                },
                {
                    antecedent: [harga.murah, spesifikasi.cukup, baterai.kuat],
                    consequent: 'baik'
                },
                {
                    antecedent: [harga.sedang, spesifikasi.tinggi, kamera.bagus],
                    consequent: 'baik'
                },
                {
                    antecedent: [harga.sedang, spesifikasi.cukup],
                    consequent: 'cukup'
                },
                {
                    antecedent: [harga.mahal, spesifikasi.rendah],
                    consequent: 'buruk'
                },
                {
                    antecedent: [harga.mahal, spesifikasi.cukup, kamera.buruk],
                    consequent: 'sangatBuruk'
                },
                {
                    antecedent: [spesifikasi.rendah, baterai.lemah],
                    consequent: 'sangatBuruk'
                },
                {
                    antecedent: [harga.murah, spesifikasi.rendah],
                    consequent: 'cukup'
                },
                {
                    antecedent: [kamera.buruk, baterai.lemah],
                    consequent: 'buruk'
                }
            ];

            for (const rule of rules) {
                const alpha = Math.min(...rule.antecedent); // Firing strength of the rule
                const consequentType = rule.consequent;

                if (alpha > 0) {
                    const z = getXFromAlpha[consequentType](alpha);
                    if (z !== null) {
                        numerator += alpha * z;
                        denominator += alpha;
                    }
                }
            }

            return denominator === 0 ? 0 : numerator / denominator;
        }

        // --- Recommendation Mapping ---
        function mapScoreToRecommendation(score) {
            if (score >= 85) return "Sangat Baik (Sangat Direkomendasikan)";
            if (score >= 70) return "Baik (Direkomendasikan)";
            if (score >= 40) return "Cukup (Pertimbangkan Kembali)";
            if (score >= 20) return "Buruk (Tidak Direkomendasikan)";
            return "Sangat Buruk (Hindari)";
        }

        // --- Canvas Drawing Functions ---
        function drawMembershipFunction(canvasId, values, labels, minVal, maxVal, inputVal = null) {
            const canvas = document.getElementById(canvasId);
            const ctx = canvas.getContext('2d');
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            const padding = 20;
            const width = canvas.width - 2 * padding;
            const height = canvas.height - 2 * padding;

            // Draw X and Y axes
            ctx.beginPath();
            ctx.moveTo(padding, padding + height);
            ctx.lineTo(padding + width, padding + height); // X-axis
            ctx.moveTo(padding, padding + height);
            ctx.lineTo(padding, padding); // Y-axis
            ctx.strokeStyle = '#6b7280'; // Gray-500
            ctx.stroke();

            // X-axis labels (min and max)
            ctx.font = '10px Inter';
            ctx.fillStyle = '#4b5563'; // Gray-600
            ctx.textAlign = 'center';
            ctx.fillText(minVal, padding, padding + height + 15);
            ctx.fillText(maxVal, padding + width, padding + height + 15);
            ctx.textAlign = 'left';
            ctx.fillText('0', padding - 10, padding + height + 5); // Origin
            ctx.textAlign = 'right';
            ctx.fillText('1', padding - 5, padding + 5); // Y-axis max

            const scaleX = width / (maxVal - minVal);
            const scaleY = height; // Membership degree is 0-1

            // Draw each membership function
            const colors = ['#ef4444', '#f97316', '#eab308', '#22c55e', '#3b82f6']; // Red, Orange, Yellow, Green, Blue
            let colorIndex = 0;

            for (const key in values) {
                const func = values[key];
                const label = labels[key];
                const color = colors[colorIndex % colors.length];
                colorIndex++;

                ctx.beginPath();
                ctx.strokeStyle = color;
                ctx.lineWidth = 2;

                let firstPoint = true;
                for (let x = minVal; x <= maxVal; x += (maxVal - minVal) / 100) { // Plot 100 points
                    const y = func(x);
                    const plotX = padding + (x - minVal) * scaleX;
                    const plotY = padding + height - y * scaleY;

                    if (firstPoint) {
                        ctx.moveTo(plotX, plotY);
                        firstPoint = false;
                    } else {
                        ctx.lineTo(plotX, plotY);
                    }
                }
                ctx.stroke();

                // Add label to the function (approximate center)
                // Find a point where membership is somewhat high for label placement
                let labelX = minVal;
                let maxMu = 0;
                for (let x = minVal; x <= maxVal; x += (maxVal - minVal) / 10) {
                    const mu = func(x);
                    if (mu > maxMu) {
                        maxMu = mu;
                        labelX = x;
                    }
                }
                ctx.fillStyle = color;
                ctx.font = '12px Inter';
                ctx.textAlign = 'center';
                ctx.fillText(label, padding + (labelX - minVal) * scaleX, padding + height - maxMu * scaleY - 5);
            }

            // Draw current input value marker
            if (inputVal !== null) {
                const markerX = padding + (inputVal - minVal) * scaleX;
                ctx.beginPath();
                ctx.moveTo(markerX, padding + height);
                ctx.lineTo(markerX, padding);
                ctx.strokeStyle = '#000000'; // Black
                ctx.lineWidth = 1.5;
                ctx.setLineDash([5, 5]); // Dashed line
                ctx.stroke();
                ctx.setLineDash([]); // Reset line dash

                ctx.fillStyle = '#000000';
                ctx.font = '12px Inter';
                ctx.textAlign = 'center';
                ctx.fillText(inputVal.toFixed(1), markerX, padding + height + 25);
            }
        }

        // --- Main Calculation and Display Logic ---
        function calculateAndDisplay() {
            const hargaVal = parseFloat(document.getElementById('harga').value);
            const spesifikasiVal = parseFloat(document.getElementById('spesifikasi').value);
            const kameraVal = parseFloat(document.getElementById('kamera').value);
            const bateraiVal = parseFloat(document.getElementById('baterai').value);

            // Input Validation
            if (isNaN(hargaVal) || isNaN(spesifikasiVal) || isNaN(kameraVal) || isNaN(bateraiVal)) {
                alert('Mohon masukkan semua nilai input yang valid.');
                return;
            }

            // Fuzzification
            const fuzzyInputs = {
                harga: fuzzifyHarga(hargaVal),
                spesifikasi: fuzzifySpesifikasi(spesifikasiVal),
                kamera: fuzzifyKamera(kameraVal),
                baterai: fuzzifyBaterai(bateraiVal)
            };

            // Mamdani
            const aggregatedMamdani = applyRules(fuzzyInputs);
            const mamdaniScore = mamdaniDefuzzification(aggregatedMamdani);
            document.getElementById('resultMamdani').textContent = mapScoreToRecommendation(mamdaniScore) + ` (${mamdaniScore.toFixed(2)})`;

            // Sugeno
            const sugenoScore = sugenoDefuzzification(fuzzyInputs);
            document.getElementById('resultSugeno').textContent = mapScoreToRecommendation(sugenoScore) + ` (${sugenoScore.toFixed(2)})`;

            // Tsukamoto
            const tsukamotoScore = tsukamotoDefuzzification(fuzzyInputs);
            document.getElementById('resultTsukamoto').textContent = mapScoreToRecommendation(tsukamotoScore) + ` (${tsukamotoScore.toFixed(2)})`;

            // Draw membership functions for inputs
            drawMembershipFunction(
                'canvasHarga',
                {
                    murah: (x) => leftShoulder(x, 3, 7),
                    sedang: (x) => triangle(x, 5, 9, 13),
                    mahal: (x) => rightShoulder(x, 10, 15)
                },
                { murah: 'Murah', sedang: 'Sedang', mahal: 'Mahal' },
                0, 15, hargaVal
            );

            drawMembershipFunction(
                'canvasSpesifikasi',
                {
                    rendah: (x) => leftShoulder(x, 30, 60),
                    cukup: (x) => triangle(x, 40, 70, 90),
                    tinggi: (x) => rightShoulder(x, 75, 100)
                },
                { rendah: 'Rendah', cukup: 'Cukup', tinggi: 'Tinggi' },
                0, 100, spesifikasiVal
            );

            drawMembershipFunction(
                'canvasKamera',
                {
                    buruk: (x) => leftShoulder(x, 20, 50),
                    cukup: (x) => triangle(x, 40, 70, 90),
                    bagus: (x) => rightShoulder(x, 70, 100)
                },
                { buruk: 'Buruk', cukup: 'Cukup', bagus: 'Bagus' },
                0, 100, kameraVal
            );

            drawMembershipFunction(
                'canvasBaterai',
                {
                    lemah: (x) => leftShoulder(x, 30, 60),
                    sedang: (x) => triangle(x, 50, 75, 95),
                    kuat: (x) => rightShoulder(x, 80, 100)
                },
                { lemah: 'Lemah', sedang: 'Sedang', kuat: 'Kuat' },
                0, 100, bateraiVal
            );

            // Draw output membership functions (Rekomendasi)
            drawMembershipFunction(
                'canvasRekomendasi',
                {
                    sangatBuruk: (x) => triangle(x, 0, 10, 20),
                    buruk: (x) => triangle(x, 10, 25, 40),
                    cukup: (x) => triangle(x, 30, 50, 70),
                    baik: (x) => triangle(x, 60, 80, 90),
                    sangatBaik: (x) => triangle(x, 80, 90, 100)
                },
                {
                    sangatBuruk: 'Sangat Buruk',
                    buruk: 'Buruk',
                    cukup: 'Cukup',
                    baik: 'Baik',
                    sangatBaik: 'Sangat Baik'
                },
                0, 100
            );
        }

        // --- Event Listeners ---
        document.addEventListener('DOMContentLoaded', () => {
            const calculateBtn = document.getElementById('calculateBtn');
            calculateBtn.addEventListener('click', calculateAndDisplay);

            const tabMamdani = document.getElementById('tabMamdani');
            const tabSugeno = document.getElementById('tabSugeno');
            const tabTsukamoto = document.getElementById('tabTsukamoto');

            const resultMamdaniDiv = document.querySelector('#resultMamdani').parentElement;
            const resultSugenoDiv = document.querySelector('#resultSugeno').parentElement;
            const resultTsukamotoDiv = document.querySelector('#resultTsukamoto').parentElement;

            tabMamdani.addEventListener('click', () => {
                tabMamdani.classList.add('active');
                tabSugeno.classList.remove('active');
                tabTsukamoto.classList.remove('active');
                resultMamdaniDiv.classList.remove('hidden');
                resultSugenoDiv.classList.add('hidden');
                resultTsukamotoDiv.classList.add('hidden');
            });

            tabSugeno.addEventListener('click', () => {
                tabMamdani.classList.remove('active');
                tabSugeno.classList.add('active');
                tabTsukamoto.classList.remove('active');
                resultMamdaniDiv.classList.add('hidden');
                resultSugenoDiv.classList.remove('hidden');
                resultTsukamotoDiv.classList.add('hidden');
            });

            tabTsukamoto.addEventListener('click', () => {
                tabMamdani.classList.remove('active');
                tabSugeno.classList.remove('active');
                tabTsukamoto.classList.add('active');
                resultMamdaniDiv.classList.add('hidden');
                resultSugenoDiv.classList.add('hidden');
                resultTsukamotoDiv.classList.remove('hidden');
            });

            // Initial calculation and display on page load
            calculateAndDisplay();
        });
    </script>
</body>
</html>
