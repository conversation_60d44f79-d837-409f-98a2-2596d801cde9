/**
 * Jest Test Setup
 * Global setup for all tests
 */

// Mock localStorage
const localStorageMock = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
};
global.localStorage = localStorageMock;

// Mock sessionStorage
const sessionStorageMock = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
};
global.sessionStorage = sessionStorageMock;

// Mock fetch
global.fetch = jest.fn();

// Mock window.location
delete window.location;
window.location = {
  href: 'http://localhost:3000',
  origin: 'http://localhost:3000',
  protocol: 'http:',
  host: 'localhost:3000',
  hostname: 'localhost',
  port: '3000',
  pathname: '/',
  search: '',
  hash: '',
  assign: jest.fn(),
  replace: jest.fn(),
  reload: jest.fn(),
};

// Mock console methods for cleaner test output
global.console = {
  ...console,
  log: jest.fn(),
  debug: jest.fn(),
  info: jest.fn(),
  warn: jest.fn(),
  error: jest.fn(),
};

// Mock DOM methods
Object.defineProperty(window, 'scrollTo', {
  value: jest.fn(),
  writable: true,
});

Object.defineProperty(window, 'alert', {
  value: jest.fn(),
  writable: true,
});

Object.defineProperty(window, 'confirm', {
  value: jest.fn(() => true),
  writable: true,
});

// Mock Chart.js
global.Chart = {
  register: jest.fn(),
  defaults: {
    global: {
      defaultFontFamily: 'Arial',
    },
  },
};

// Mock IntersectionObserver
global.IntersectionObserver = class IntersectionObserver {
  constructor() {}
  observe() {
    return null;
  }
  disconnect() {
    return null;
  }
  unobserve() {
    return null;
  }
};

// Mock ResizeObserver
global.ResizeObserver = class ResizeObserver {
  constructor() {}
  observe() {
    return null;
  }
  disconnect() {
    return null;
  }
  unobserve() {
    return null;
  }
};

// Setup DOM
document.body.innerHTML = `
  <div id="app"></div>
  <div id="toast"></div>
  <div id="modal"></div>
`;

// Global test utilities
global.testUtils = {
  // Create a mock API response
  mockApiResponse: (data, success = true) => ({
    success,
    data,
    message: success ? 'Success' : 'Error',
  }),

  // Create a mock user
  mockUser: (overrides = {}) => ({
    id: 1,
    name: 'Test User',
    email: '<EMAIL>',
    email_verified_at: '2023-01-01T00:00:00.000000Z',
    created_at: '2023-01-01T00:00:00.000000Z',
    updated_at: '2023-01-01T00:00:00.000000Z',
    ...overrides,
  }),

  // Create a mock form template
  mockFormTemplate: (overrides = {}) => ({
    id: 1,
    code: 'TEST',
    name: 'Test Form',
    description: 'Test form description',
    category: 'test',
    questions: ['Question 1', 'Question 2'],
    scoring_rules: {
      type: 'binary_sum',
      max_score: 2,
      questions: {
        1: { type: 'binary', yes_score: 1, no_score: 0 },
        2: { type: 'binary', yes_score: 1, no_score: 0 },
      },
    },
    interpretation_rules: [
      {
        min_score: 0,
        max_score: 1,
        status: 'normal',
        interpretation: 'Normal',
        recommendations: ['Keep it up'],
      },
      {
        min_score: 2,
        max_score: 2,
        status: 'concern',
        interpretation: 'Concern',
        recommendations: ['Seek help'],
      },
    ],
    time_limit: 10,
    is_active: true,
    version: 1,
    ...overrides,
  }),

  // Create a mock form response
  mockFormResponse: (overrides = {}) => ({
    id: 1,
    user_id: 1,
    form_template_id: 1,
    answers: { 1: 1, 2: 0 },
    total_score: 1,
    status: 'normal',
    interpretation: 'Normal',
    recommendations: ['Keep it up'],
    completion_time: 120,
    created_at: '2023-01-01T00:00:00.000000Z',
    updated_at: '2023-01-01T00:00:00.000000Z',
    ...overrides,
  }),

  // Wait for async operations
  waitFor: (ms = 0) => new Promise(resolve => setTimeout(resolve, ms)),

  // Trigger DOM events
  triggerEvent: (element, eventType, eventInit = {}) => {
    const event = new Event(eventType, { bubbles: true, ...eventInit });
    element.dispatchEvent(event);
  },

  // Create DOM element
  createElement: (tag, attributes = {}, textContent = '') => {
    const element = document.createElement(tag);
    Object.entries(attributes).forEach(([key, value]) => {
      element.setAttribute(key, value);
    });
    if (textContent) {
      element.textContent = textContent;
    }
    return element;
  },
};

// Reset mocks before each test
beforeEach(() => {
  // Clear all mocks
  jest.clearAllMocks();

  // Reset localStorage
  localStorageMock.getItem.mockReturnValue(null);
  localStorageMock.setItem.mockImplementation(() => {});
  localStorageMock.removeItem.mockImplementation(() => {});
  localStorageMock.clear.mockImplementation(() => {});

  // Reset sessionStorage
  sessionStorageMock.getItem.mockReturnValue(null);
  sessionStorageMock.setItem.mockImplementation(() => {});
  sessionStorageMock.removeItem.mockImplementation(() => {});
  sessionStorageMock.clear.mockImplementation(() => {});

  // Reset fetch
  fetch.mockResolvedValue({
    ok: true,
    status: 200,
    json: jest.fn().mockResolvedValue({}),
    text: jest.fn().mockResolvedValue(''),
  });

  // Reset DOM
  document.body.innerHTML = `
    <div id="app"></div>
    <div id="toast"></div>
    <div id="modal"></div>
  `;
});

// Global error handler for unhandled promise rejections
process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
});
