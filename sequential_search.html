<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Visualisasi Algoritma Sequential Search</title>
    
    <!-- Memuat Tailwind CSS untuk styling -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Memuat Font Inter dari Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;700&display=swap" rel="stylesheet">

    <style>
        /* Menggunakan font Inter sebagai default */
        body {
            font-family: 'Inter', sans-serif;
        }
        /* Kontainer array perlu posisi relatif untuk menampung pointer */
        #array-wrapper {
            position: relative;
            padding-top: 50px; /* <PERSON>uang untuk pointer di atas array */
        }
        /* Styling untuk pointer pencarian */
        #search-pointer {
            position: absolute;
            top: 0;
            left: 0;
            transition: transform 0.4s ease-in-out;
            will-change: transform;
            opacity: 0; /* Sembunyikan di awal */
        }
        /* Transisi halus untuk elemen array */
        .array-element {
            transition: background-color 0.3s ease, border-color 0.3s ease, transform 0.3s ease;
        }
        /* Animasi berdenyut (pulse) untuk elemen yang ditemukan */
        .found-animation {
            animation: pulse 1s infinite;
        }
        @keyframes pulse {
            0% { transform: scale(1.05); }
            50% { transform: scale(1.15); }
            100% { transform: scale(1.05); }
        }
    </style>
</head>
<body class="bg-gray-100 text-gray-800 flex items-center justify-center min-h-screen p-4">

    <div class="w-full max-w-4xl bg-white rounded-xl shadow-lg p-6 md:p-8">
        
        <!-- Judul Aplikasi -->
        <div class="text-center mb-6">
            <h1 class="text-2xl md:text-3xl font-bold text-gray-900">Visualisasi Algoritma Sequential Search</h1>
            <p class="text-gray-600 mt-1">Simulasi pencarian elemen dalam sebuah array secara berurutan.</p>
        </div>

        <!-- Wrapper untuk array dan pointer -->
        <div id="array-wrapper" class="mb-6 min-h-[120px]">
            <!-- Pointer pencarian akan dibuat oleh JavaScript di sini -->
            <div id="array-container" class="flex flex-wrap justify-center items-center gap-2">
                <!-- Elemen-elemen array akan dibuat oleh JavaScript di sini -->
            </div>
        </div>

        <!-- Panel Kontrol -->
        <div class="bg-gray-50 p-4 rounded-lg shadow-inner">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 items-center">
                <!-- Input untuk angka yang dicari -->
                <div class="flex flex-col">
                    <label for="search-input" class="text-sm font-medium text-gray-700 mb-1">Angka yang Dicari:</label>
                    <input type="number" id="search-input" placeholder="e.g., 42" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500">
                </div>
                
                <!-- Tombol Aksi -->
                <div class="md:col-span-2 flex flex-col sm:flex-row gap-3 mt-4 md:mt-0 md:items-end h-full">
                    <button id="search-button" class="w-full sm:w-auto flex-grow bg-indigo-600 text-white font-semibold py-2 px-4 rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition duration-150 ease-in-out">
                        Mulai Pencarian
                    </button>
                    <button id="reset-button" class="w-full sm:w-auto flex-grow bg-gray-600 text-white font-semibold py-2 px-4 rounded-md hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 transition duration-150 ease-in-out">
                        Buat Array Baru
                    </button>
                </div>
            </div>
        </div>

        <!-- Pesan Status -->
        <div id="status-message" class="text-center mt-6 font-medium text-lg text-gray-700 min-h-[28px]">
            <!-- Pesan status akan ditampilkan di sini -->
        </div>

    </div>

    <script>
        // --- Referensi Elemen DOM ---
        const arrayWrapper = document.getElementById('array-wrapper');
        const arrayContainer = document.getElementById('array-container');
        const searchInput = document.getElementById('search-input');
        const searchButton = document.getElementById('search-button');
        const resetButton = document.getElementById('reset-button');
        const statusMessage = document.getElementById('status-message');

        // --- Variabel State ---
        let dataArray = [];
        let searchPointer = null; // Variabel untuk menyimpan elemen pointer
        const ARRAY_SIZE = 12; 
        const ANIMATION_SPEED_MS = 500;

        // --- Fungsi Utama ---

        /**
         * Fungsi untuk menunda eksekusi (digunakan dalam animasi).
         * @param {number} ms - Waktu tunda dalam milidetik.
         */
        function sleep(ms) {
            return new Promise(resolve => setTimeout(resolve, ms));
        }

        /**
         * Membuat elemen pointer (panah) dan menambahkannya ke DOM.
         */
        function createSearchPointer() {
            // Hapus pointer lama jika ada
            if (searchPointer && searchPointer.parentNode) {
                searchPointer.parentNode.removeChild(searchPointer);
            }
            searchPointer = document.createElement('div');
            searchPointer.id = 'search-pointer';
            // SVG untuk ikon panah
            searchPointer.innerHTML = `
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" class="text-indigo-600">
                    <path d="M12 15.5L6 9.5L7.41 8.09L12 12.67L16.59 8.09L18 9.5L12 15.5Z" fill="currentColor"/>
                </svg>
            `;
            arrayWrapper.prepend(searchPointer);
        }

        /**
         * Menghasilkan array baru, merendernya, dan menyiapkan pointer.
         */
        function generateAndRenderArray() {
            dataArray = [];
            for (let i = 0; i < ARRAY_SIZE; i++) {
                dataArray.push(Math.floor(Math.random() * 100) + 1);
            }
            renderArray();
            createSearchPointer(); // Buat pointer setelah array dirender
            statusMessage.textContent = 'Array baru telah dibuat. Masukkan angka untuk dicari.';
            enableControls();
        }

        /**
         * Merender array saat ini ke dalam DOM.
         */
        function renderArray() {
            arrayContainer.innerHTML = ''; // Bersihkan kontainer
            dataArray.forEach((value, index) => {
                const elementDiv = document.createElement('div');
                elementDiv.id = `element-${index}`;
                elementDiv.className = 'array-element flex items-center justify-center w-12 h-12 md:w-14 md:h-14 bg-gray-200 border-2 border-gray-300 rounded-md text-lg font-bold';
                elementDiv.textContent = value;
                arrayContainer.appendChild(elementDiv);
            });
        }

        /**
         * Menjalankan algoritma Sequential Search dengan animasi pointer.
         */
        async function performSequentialSearch() {
            const targetValue = parseInt(searchInput.value, 10);

            if (isNaN(targetValue)) {
                statusMessage.textContent = 'Harap masukkan angka yang valid.';
                statusMessage.className = 'text-center mt-6 font-medium text-lg text-red-600';
                return;
            }

            disableControls();
            let found = false;
            searchPointer.style.opacity = 1; // Tampilkan pointer

            for (let i = 0; i < dataArray.length; i++) {
                const currentElement = document.getElementById(`element-${i}`);
                
                // Pindahkan pointer ke atas elemen saat ini
                const elementRect = currentElement.getBoundingClientRect();
                const containerRect = arrayWrapper.getBoundingClientRect();
                const pointerOffset = (elementRect.width / 2) - 12; // 12 adalah setengah lebar pointer (24px)
                const newX = elementRect.left - containerRect.left + pointerOffset;
                searchPointer.style.transform = `translateX(${newX}px)`;
                
                // Sorot elemen yang sedang diperiksa
                statusMessage.textContent = `Mengecek indeks ${i} (nilai: ${dataArray[i]})...`;
                statusMessage.className = 'text-center mt-6 font-medium text-lg text-blue-700';
                currentElement.classList.add('bg-blue-200', 'border-blue-500', 'transform', 'scale-110');
                
                await sleep(ANIMATION_SPEED_MS);

                // Bandingkan nilai
                if (dataArray[i] === targetValue) {
                    statusMessage.textContent = `Elemen ${targetValue} ditemukan di indeks ${i}!`;
                    statusMessage.className = 'text-center mt-6 font-medium text-lg text-green-600';
                    currentElement.classList.remove('bg-blue-200', 'border-blue-500');
                    currentElement.classList.add('bg-green-400', 'border-green-600', 'text-white', 'found-animation');
                    found = true;
                    break; // Hentikan pencarian
                } else {
                    // Kembalikan elemen ke state normal jika tidak cocok
                    currentElement.classList.remove('bg-blue-200', 'border-blue-500', 'scale-110');
                }
            }

            if (!found) {
                statusMessage.textContent = `Elemen ${targetValue} tidak ditemukan dalam array.`;
                statusMessage.className = 'text-center mt-6 font-medium text-lg text-red-600';
                searchPointer.style.opacity = 0; // Sembunyikan pointer jika tidak ditemukan
            }

            // Aktifkan kembali kontrol setelah pencarian selesai
            enableControls(false);
        }
        
        /**
         * Menonaktifkan tombol dan input selama animasi berjalan.
         */
        function disableControls() {
            searchInput.disabled = true;
            searchButton.disabled = true;
            resetButton.disabled = true;
            searchButton.classList.add('opacity-50', 'cursor-not-allowed');
            resetButton.classList.add('opacity-50', 'cursor-not-allowed');
        }
        
        /**
         * Mengaktifkan kembali tombol dan input.
         * @param {boolean} clearInput - Apakah akan membersihkan input field.
         */
        function enableControls(clearInput = true) {
            searchInput.disabled = false;
            searchButton.disabled = false;
            resetButton.disabled = false;
            searchButton.classList.remove('opacity-50', 'cursor-not-allowed');
            resetButton.classList.remove('opacity-50', 'cursor-not-allowed');
            if(clearInput) {
                 // Bersihkan state visual dari pencarian sebelumnya
                const allElements = document.querySelectorAll('.array-element');
                allElements.forEach(el => {
                    el.classList.remove('bg-green-400', 'border-green-600', 'text-white', 'found-animation', 'scale-110', 'bg-blue-200', 'border-blue-500');
                });
                searchInput.value = '';
            }
        }

        // --- Event Listeners ---
        searchButton.addEventListener('click', performSequentialSearch);
        resetButton.addEventListener('click', generateAndRenderArray);
        
        searchInput.addEventListener('keyup', function(event) {
            if (event.key === 'Enter') {
                searchButton.click();
            }
        });

        // Inisialisasi aplikasi saat halaman dimuat
        window.addEventListener('DOMContentLoaded', generateAndRenderArray);

    </script>
</body>
</html>
