<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Kuesioner <PERSON><PERSON><PERSON><PERSON> (MHKQ)</title>
    
    <!-- Tailwind CSS for styling -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Google Fonts: Inter for a modern look -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800&display=swap" rel="stylesheet">

    <style>
        /* Custom styles for the application */
        body {
            font-family: 'Inter', sans-serif;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        /* Custom gradient background */
        .gradient-bg {
            background-color: #f0f9ff;
            background-image: radial-gradient(at 47% 33%, hsl(162.00, 77%, 40%) 0, transparent 59%), 
                              radial-gradient(at 82% 65%, hsl(218.00, 89%, 43%) 0, transparent 55%);
        }

        /* Custom styling for radio buttons to make them more engaging */
        .radio-label {
            transition: all 0.3s ease-in-out;
        }

        /* Style the label when the hidden radio button is checked */
        .radio-input:checked + .radio-label {
            border-color: #0d9488; /* teal-600 */
            background-color: #ccfbf1; /* teal-100 */
            color: #134e4a; /* teal-900 */
            font-weight: 600;
            transform: translateY(-2px);
            box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
        }
        
        /* Custom modal for alerts */
        #customAlert {
            transition: opacity 0.3s ease-in-out;
        }
    </style>
</head>
<body class="gradient-bg">

    <main class="min-h-screen w-full flex items-center justify-center p-4 sm:p-6 lg:p-8">
        <div class="w-full max-w-4xl bg-white/80 backdrop-blur-sm rounded-2xl shadow-2xl p-6 sm:p-8 lg:p-12 transform transition-all duration-500">
            
            <!-- Header Section -->
            <header id="appHeader" class="text-center mb-8">
                <h1 class="text-3xl sm:text-4xl font-extrabold text-gray-800 tracking-tight">Kuesioner Pengetahuan Kesehatan Jiwa</h1>
                <p class="mt-2 text-lg text-teal-800 font-semibold">(MHKQ)</p>
                <p class="mt-4 text-gray-600 max-w-2xl mx-auto">Pilih salah satu jawaban yang paling sesuai untuk setiap pernyataan di bawah ini. Jawaban Anda akan membantu mengukur tingkat pengetahuan Anda tentang kesehatan jiwa.</p>
            </header>

            <!-- Questionnaire Form -->
            <form id="mhkqForm">
                <div id="questionsContainer" class="space-y-6">
                    <!-- Questions will be dynamically inserted here by JavaScript -->
                </div>
                <div class="mt-10">
                    <button type="submit" class="w-full bg-teal-600 hover:bg-teal-700 text-white font-bold text-lg py-4 px-4 rounded-xl shadow-lg hover:shadow-xl focus:outline-none focus:ring-4 focus:ring-teal-300 transition-all duration-300">
                        Lihat Hasil
                    </button>
                </div>
            </form>

            <!-- Results Container (Initially Hidden) -->
            <div id="hasilContainer" class="hidden text-center">
                <h2 class="text-3xl font-extrabold text-gray-800 mb-6">Hasil Pengetahuan Anda</h2>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Score Card -->
                    <div class="bg-blue-100 border-2 border-blue-300 rounded-xl p-6 shadow-md">
                        <h3 class="text-lg font-semibold text-blue-800 mb-2">Skor Anda</h3>
                        <p class="text-5xl font-bold text-blue-900">
                            <span id="skorValue">0</span> / 20
                        </p>
                    </div>

                    <!-- Category Card -->
                    <div id="kategoriCard" class="bg-green-100 border-2 border-green-300 rounded-xl p-6 shadow-md">
                        <h3 class="text-lg font-semibold text-green-800 mb-2">Kategori Pengetahuan</h3>
                        <p id="kategoriValue" class="text-4xl font-bold text-green-900">Tinggi</p>
                    </div>
                </div>

                <p id="pesanHasil" class="mt-6 text-gray-700 text-lg">Pengetahuan Anda sangat baik! Terus tingkatkan dan bagikan informasi yang benar tentang kesehatan jiwa.</p>

                <div class="mt-8">
                    <button id="restartBtn" class="bg-gray-700 hover:bg-gray-800 text-white font-bold py-3 px-8 rounded-xl shadow-lg hover:shadow-xl focus:outline-none focus:ring-4 focus:ring-gray-400 transition-all duration-300">
                        Ulangi Kuesioner
                    </button>
                </div>
            </div>

        </div>
    </main>
    
    <!-- Custom Alert Modal -->
    <div id="customAlert" class="hidden fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50 opacity-0">
        <div class="bg-white rounded-xl shadow-2xl p-8 max-w-sm w-full text-center transform scale-95 transition-transform duration-300">
            <h3 class="text-xl font-bold text-red-600">Perhatian!</h3>
            <p id="customAlertMessage" class="text-gray-700 my-4">Harap jawab semua pertanyaan sebelum melihat hasil.</p>
            <button id="customAlertClose" class="mt-4 bg-red-500 hover:bg-red-600 text-white font-bold py-2 px-6 rounded-lg transition-colors">Tutup</button>
        </div>
    </div>


    <script>
        document.addEventListener('DOMContentLoaded', () => {
            // --- DATA & CONFIGURATION ---

            // List of questions based on the PDF
            const questions = [
                { id: 1, text: "Kesehatan jiwa berarti kondisi seseorang dapat mengenali potensi dirinya." },
                { id: 2, text: "Orang dengan gangguan jiwa tidak bisa hidup mandiri." },
                { id: 3, text: "Gangguan jiwa seperti depresi bisa menyerang siapa saja." },
                { id: 4, text: "Semua orang dengan gangguan jiwa akan melakukan kekerasan." },
                { id: 5, text: "Faktor sosial dan ekonomi bisa mempengaruhi kesehatan jiwa." },
                { id: 6, text: "Gangguan jiwa tidak dapat disembuhkan." },
                { id: 7, text: "Kecemasan berlebihan termasuk bentuk gangguan jiwa." },
                { id: 8, text: "Masalah kejiwaan hanya dialami orang dengan latar belakang keluarga miskin." },
                { id: 9, text: "Kesehatan fisik yang buruk bisa berdampak pada kesehatan jiwa." },
                { id: 10, text: "Gangguan jiwa tidak bisa dicegah." },
                { id: 11, text: "Orang dengan gangguan jiwa sering mengalami diskriminasi." },
                { id: 12, text: "Konsumsi narkoba dapat memicu gangguan kesehatan jiwa." },
                { id: 13, text: "Ada bantuan profesional untuk masalah kesehatan jiwa di puskesmas." },
                { id: 14, text: "Mencari bantuan psikolog berarti seseorang pasti gila." },
                { id: 15, text: "Mendapatkan informasi tentang kesehatan jiwa bisa membantu pencegahan." },
                { id: 16, text: "Orang pintar (dukun) lebih ampuh menyembuhkan gangguan jiwa daripada dokter." },
                { id: 17, text: "Konseling adalah bagian dari promosi kesehatan jiwa." },
                { id: 18, text: "Sekolah dapat menjadi tempat edukasi tentang kesehatan jiwa." },
                { id: 19, text: "Film animasi bisa digunakan sebagai media edukasi kesehatan jiwa." },
                { id: 20, text: "Promosi kesehatan jiwa hanya penting bagi orang yang sudah sakit jiwa." }
            ];

            // Answer key (B=Benar, S=Salah). This is inferred as it wasn't in the PDF.
            const answerKey = {
                1: 'benar', 2: 'salah', 3: 'benar', 4: 'salah', 5: 'benar',
                6: 'salah', 7: 'benar', 8: 'salah', 9: 'benar', 10: 'salah',
                11: 'benar', 12: 'benar', 13: 'benar', 14: 'salah', 15: 'benar',
                16: 'salah', 17: 'benar', 18: 'benar', 19: 'benar', 20: 'salah'
            };

            // --- DOM ELEMENT REFERENCES ---
            const form = document.getElementById('mhkqForm');
            const questionsContainer = document.getElementById('questionsContainer');
            const hasilContainer = document.getElementById('hasilContainer');
            const skorValue = document.getElementById('skorValue');
            const kategoriValue = document.getElementById('kategoriValue');
            const kategoriCard = document.getElementById('kategoriCard');
            const pesanHasil = document.getElementById('pesanHasil');
            const restartBtn = document.getElementById('restartBtn');
            const appHeader = document.getElementById('appHeader');
            
            // Custom Alert Elements
            const customAlert = document.getElementById('customAlert');
            const customAlertMessage = document.getElementById('customAlertMessage');
            const customAlertClose = document.getElementById('customAlertClose');


            // --- FUNCTIONS ---

            /**
             * Renders all questions into the questionsContainer.
             * This makes the application dynamic and ready for API integration.
             */
            function renderQuestions() {
                let questionsHTML = '';
                questions.forEach(q => {
                    questionsHTML += `
                        <div class="bg-white/60 p-5 sm:p-6 rounded-xl shadow-md border border-gray-200 transition-all duration-300 hover:shadow-lg hover:border-teal-200">
                            <p class="text-lg font-medium text-gray-800 mb-4">
                                <span class="font-bold text-teal-700">${q.id}.</span> ${q.text}
                            </p>
                            <div class="grid grid-cols-1 sm:grid-cols-2 gap-3">
                                <div>
                                    <input type="radio" name="q${q.id}" id="q${q.id}-benar" value="benar" class="hidden radio-input" required>
                                    <label for="q${q.id}-benar" class="radio-label flex items-center justify-center cursor-pointer p-3 border-2 border-gray-300 rounded-lg text-gray-700">
                                        Benar
                                    </label>
                                </div>
                                <div>
                                    <input type="radio" name="q${q.id}" id="q${q.id}-salah" value="salah" class="hidden radio-input" required>
                                    <label for="q${q.id}-salah" class="radio-label flex items-center justify-center cursor-pointer p-3 border-2 border-gray-300 rounded-lg text-gray-700">
                                        Salah
                                    </label>
                                </div>
                            </div>
                        </div>
                    `;
                });
                questionsContainer.innerHTML = questionsHTML;
            }
            
            /**
             * Shows a custom alert modal.
             * @param {string} message - The message to display.
             */
            function showAlert(message) {
                customAlertMessage.textContent = message;
                customAlert.classList.remove('hidden');
                setTimeout(() => {
                    customAlert.classList.remove('opacity-0');
                    customAlert.querySelector('div').classList.remove('scale-95');
                }, 10);
            }

            /**
             * Hides the custom alert modal.
             */
            function hideAlert() {
                customAlert.classList.add('opacity-0');
                customAlert.querySelector('div').classList.add('scale-95');
                setTimeout(() => {
                    customAlert.classList.add('hidden');
                }, 300);
            }

            /**
             * Handles the form submission, calculates score, and displays results.
             * @param {Event} e - The form submission event.
             */
            function handleSubmit(e) {
                e.preventDefault();
                
                const formData = new FormData(form);
                const userAnswers = {};
                let score = 0;
                let answeredQuestions = 0;

                for (const [key, value] of formData.entries()) {
                    const questionId = key.replace('q', '');
                    userAnswers[questionId] = value;
                    if (answerKey[questionId] === value) {
                        score++;
                    }
                    answeredQuestions++;
                }
                
                // Validation: Check if all questions are answered
                if (answeredQuestions < questions.length) {
                    showAlert('Harap jawab semua pertanyaan sebelum melihat hasil.');
                    return;
                }

                // Determine category based on score
                let category = '';
                let categoryClass = '';
                let message = '';

                if (score >= 16) {
                    category = 'Tinggi';
                    categoryClass = 'bg-green-100 border-green-300 text-green-900';
                    message = 'Luar biasa! Pengetahuan Anda sangat baik. Teruslah menjadi agen perubahan dengan menyebarkan informasi yang akurat tentang kesehatan jiwa.';
                } else if (score >= 11) {
                    category = 'Cukup';
                    categoryClass = 'bg-yellow-100 border-yellow-300 text-yellow-900';
                    message = 'Bagus! Anda sudah memiliki dasar pengetahuan yang baik. Terus belajar untuk lebih memahami seluk-beluk kesehatan jiwa.';
                } else {
                    category = 'Rendah';
                    categoryClass = 'bg-red-100 border-red-300 text-red-900';
                    message = 'Tidak apa-apa, belajar adalah sebuah proses. Ini adalah kesempatan bagus untuk mulai memperdalam pengetahuan Anda tentang kesehatan jiwa.';
                }

                // --- PREPARE DATA FOR API (INTEGRATION POINT) ---
                const dataForAPI = {
                    userId: `user_${new Date().getTime()}`, // Example user ID
                    answers: userAnswers,
                    score: score,
                    category: category,
                    submittedAt: new Date().toISOString()
                };

                // In a real application, you would send this data to your backend.
                console.log("Data to be sent to API:", dataForAPI);
                // Example: sendToAPI(dataForAPI);

                // --- DISPLAY RESULTS ---
                skorValue.textContent = score;
                kategoriValue.textContent = category;
                pesanHasil.textContent = message;

                // Update category card styling
                kategoriCard.className = `rounded-xl p-6 shadow-md border-2 ${categoryClass}`;
                kategoriValue.className = `text-4xl font-bold ${categoryClass.split(' ')[2]}`;
                kategoriCard.querySelector('h3').className = `text-lg font-semibold ${categoryClass.split(' ')[2]}`;

                // Animate the transition
                form.classList.add('opacity-0', 'scale-95');
                appHeader.classList.add('opacity-0', 'scale-95');
                
                setTimeout(() => {
                    form.classList.add('hidden');
                    appHeader.classList.add('hidden');
                    hasilContainer.classList.remove('hidden');
                    hasilContainer.classList.add('opacity-0', 'scale-95');
                    setTimeout(() => {
                        hasilContainer.classList.remove('opacity-0', 'scale-95');
                    }, 50);
                }, 300);
            }
            
            /**
             * Resets the quiz to its initial state.
             */
            function restartQuiz() {
                hasilContainer.classList.add('opacity-0', 'scale-95');
                
                setTimeout(() => {
                    hasilContainer.classList.add('hidden');
                    form.classList.remove('hidden');
                    appHeader.classList.remove('hidden');
                    form.reset();
                    
                    // Scroll to top
                    window.scrollTo(0, 0);
                    
                    setTimeout(() => {
                        form.classList.remove('opacity-0', 'scale-95');
                        appHeader.classList.remove('opacity-0', 'scale-95');
                    }, 50);
                }, 300);
            }

            // --- EVENT LISTENERS ---
            form.addEventListener('submit', handleSubmit);
            restartBtn.addEventListener('click', restartQuiz);
            customAlertClose.addEventListener('click', hideAlert);
            customAlert.addEventListener('click', (e) => {
                if (e.target === customAlert) {
                    hideAlert();
                }
            });


            // --- INITIALIZATION ---
            renderQuestions();
        });

        // Example function for API submission
        /*
        async function sendToAPI(data) {
            try {
                const response = await fetch('https://api.example.com/mhkq-results', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        // 'Authorization': 'Bearer YOUR_API_TOKEN'
                    },
                    body: JSON.stringify(data)
                });

                if (!response.ok) {
                    throw new Error(`API Error: ${response.statusText}`);
                }

                const result = await response.json();
                console.log('API Response:', result);
                // You could show a "Successfully saved" message here
                
            } catch (error) {
                console.error('Failed to send data to API:', error);
                // You could show an error message to the user here
            }
        }
        */
    </script>
</body>
</html>
