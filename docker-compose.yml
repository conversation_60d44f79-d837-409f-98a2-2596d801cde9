version: '3.8'

services:
  # MySQL Database
  mysql:
    image: mysql:8.0
    container_name: santrimental_mysql
    restart: unless-stopped
    environment:
      MYSQL_DATABASE: santrimental
      MYSQL_ROOT_PASSWORD: root
      MYSQL_USER: santrimental
      MYSQL_PASSWORD: password
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./backend/database/init:/docker-entrypoint-initdb.d
    networks:
      - santrimental_network

  # PHP Laravel Application
  app:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: santrimental_app
    restart: unless-stopped
    working_dir: /var/www
    volumes:
      - ./backend:/var/www
      - ./backend/storage:/var/www/storage
    networks:
      - santrimental_network
    depends_on:
      - mysql
    environment:
      - DB_HOST=mysql
      - DB_DATABASE=santrimental
      - DB_USERNAME=santrimental
      - DB_PASSWORD=password

  # Nginx Web Server
  nginx:
    image: nginx:alpine
    container_name: santrimental_nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./backend:/var/www
      - ./docker/nginx:/etc/nginx/conf.d
      - ./docker/ssl:/etc/ssl/certs
    networks:
      - santrimental_network
    depends_on:
      - app

  # Redis for Caching and Sessions
  redis:
    image: redis:alpine
    container_name: santrimental_redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    networks:
      - santrimental_network

  # phpMyAdmin for Database Management
  phpmyadmin:
    image: phpmyadmin/phpmyadmin
    container_name: santrimental_phpmyadmin
    restart: unless-stopped
    environment:
      PMA_HOST: mysql
      PMA_USER: root
      PMA_PASSWORD: root
    ports:
      - "8080:80"
    networks:
      - santrimental_network
    depends_on:
      - mysql

volumes:
  mysql_data:
    driver: local

networks:
  santrimental_network:
    driver: bridge
