<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\User;
use Illuminate\Support\Facades\Hash;

class UserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create demo user
        User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'first_name' => 'Demo',
                'last_name' => 'User',
                'email' => '<EMAIL>',
                'password' => Hash::make('demo123'),
                'email_verified_at' => now(),
            ]
        );

        // Create test user
        User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'first_name' => 'Test',
                'last_name' => 'User',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
                'email_verified_at' => now(),
            ]
        );

        // Create admin user
        User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'first_name' => 'Admin',
                'last_name' => 'User',
                'email' => '<EMAIL>',
                'password' => Hash::make('admin123'),
                'email_verified_at' => now(),
            ]
        );
    }
}
