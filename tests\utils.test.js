/**
 * Utils Tests
 */

// Mock the Utils module
const mockUtils = {
  apiCall: jest.fn(),
  showNotification: jest.fn(),
  formatDate: jest.fn(),
  validateEmail: jest.fn(),
  validatePassword: jest.fn(),
  debounce: jest.fn(),
  throttle: jest.fn(),
  generateId: jest.fn(),
  sanitizeHtml: jest.fn(),
};

// Mock window.Utils
global.Utils = mockUtils;

describe('Utils', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('apiCall', () => {
    it('should make successful API call', async () => {
      const mockResponse = testUtils.mockApiResponse({ message: 'Success' });

      fetch.mockResolvedValueOnce({
        ok: true,
        status: 200,
        json: jest.fn().mockResolvedValue(mockResponse),
      });

      mockUtils.apiCall.mockResolvedValue(mockResponse);

      const result = await mockUtils.apiCall('/test-endpoint');

      expect(mockUtils.apiCall).toHaveBeenCalledWith('/test-endpoint');
      expect(result.success).toBe(true);
      expect(result.data.message).toBe('Success');
    });

    it('should handle API errors', async () => {
      const mockResponse = testUtils.mockApiResponse(null, false);
      mockResponse.message = 'Server error';

      fetch.mockResolvedValueOnce({
        ok: false,
        status: 500,
        json: jest.fn().mockResolvedValue(mockResponse),
      });

      mockUtils.apiCall.mockResolvedValue(mockResponse);

      const result = await mockUtils.apiCall('/test-endpoint');

      expect(result.success).toBe(false);
      expect(result.message).toBe('Server error');
    });

    it('should handle network errors', async () => {
      fetch.mockRejectedValueOnce(new Error('Network error'));
      mockUtils.apiCall.mockRejectedValue(new Error('Network error'));

      await expect(mockUtils.apiCall('/test-endpoint')).rejects.toThrow('Network error');
    });

    it('should send POST data correctly', async () => {
      const postData = { name: 'Test', email: '<EMAIL>' };
      const mockResponse = testUtils.mockApiResponse({ id: 1 });

      fetch.mockResolvedValueOnce({
        ok: true,
        status: 201,
        json: jest.fn().mockResolvedValue(mockResponse),
      });

      mockUtils.apiCall.mockResolvedValue(mockResponse);

      const result = await mockUtils.apiCall('/test-endpoint', {
        method: 'POST',
        body: JSON.stringify(postData),
      });

      expect(mockUtils.apiCall).toHaveBeenCalledWith('/test-endpoint', {
        method: 'POST',
        body: JSON.stringify(postData),
      });
      expect(result.success).toBe(true);
    });
  });

  describe('showNotification', () => {
    it('should show success notification', () => {
      mockUtils.showNotification.mockImplementation((message, type) => {
        expect(message).toBe('Success message');
        expect(type).toBe('success');
      });

      mockUtils.showNotification('Success message', 'success');

      expect(mockUtils.showNotification).toHaveBeenCalledWith('Success message', 'success');
    });

    it('should show error notification', () => {
      mockUtils.showNotification.mockImplementation((message, type) => {
        expect(message).toBe('Error message');
        expect(type).toBe('error');
      });

      mockUtils.showNotification('Error message', 'error');

      expect(mockUtils.showNotification).toHaveBeenCalledWith('Error message', 'error');
    });
  });

  describe('formatDate', () => {
    it('should format date correctly', () => {
      const date = new Date('2023-01-01T12:00:00Z');
      mockUtils.formatDate.mockReturnValue('1 Januari 2023');

      const result = mockUtils.formatDate(date);

      expect(result).toBe('1 Januari 2023');
      expect(mockUtils.formatDate).toHaveBeenCalledWith(date);
    });

    it('should handle invalid dates', () => {
      mockUtils.formatDate.mockReturnValue('Invalid Date');

      const result = mockUtils.formatDate('invalid-date');

      expect(result).toBe('Invalid Date');
    });
  });

  describe('validateEmail', () => {
    it('should validate correct email', () => {
      mockUtils.validateEmail.mockReturnValue(true);

      const result = mockUtils.validateEmail('<EMAIL>');

      expect(result).toBe(true);
      expect(mockUtils.validateEmail).toHaveBeenCalledWith('<EMAIL>');
    });

    it('should reject invalid email', () => {
      mockUtils.validateEmail.mockReturnValue(false);

      const result = mockUtils.validateEmail('invalid-email');

      expect(result).toBe(false);
      expect(mockUtils.validateEmail).toHaveBeenCalledWith('invalid-email');
    });

    it('should reject empty email', () => {
      mockUtils.validateEmail.mockReturnValue(false);

      const result = mockUtils.validateEmail('');

      expect(result).toBe(false);
    });
  });

  describe('validatePassword', () => {
    it('should validate strong password', () => {
      mockUtils.validatePassword.mockReturnValue(true);

      const result = mockUtils.validatePassword('StrongPassword123!');

      expect(result).toBe(true);
      expect(mockUtils.validatePassword).toHaveBeenCalledWith('StrongPassword123!');
    });

    it('should reject weak password', () => {
      mockUtils.validatePassword.mockReturnValue(false);

      const result = mockUtils.validatePassword('weak');

      expect(result).toBe(false);
      expect(mockUtils.validatePassword).toHaveBeenCalledWith('weak');
    });

    it('should reject empty password', () => {
      mockUtils.validatePassword.mockReturnValue(false);

      const result = mockUtils.validatePassword('');

      expect(result).toBe(false);
    });
  });

  describe('debounce', () => {
    it('should debounce function calls', (done) => {
      let callCount = 0;
      const mockFn = jest.fn(() => callCount++);
      
      mockUtils.debounce.mockImplementation((fn, delay) => {
        let timeoutId;
        return (...args) => {
          clearTimeout(timeoutId);
          timeoutId = setTimeout(() => fn.apply(this, args), delay);
        };
      });

      const debouncedFn = mockUtils.debounce(mockFn, 100);

      // Call multiple times quickly
      debouncedFn();
      debouncedFn();
      debouncedFn();

      // Should only be called once after delay
      setTimeout(() => {
        expect(mockUtils.debounce).toHaveBeenCalledWith(mockFn, 100);
        done();
      }, 150);
    });
  });

  describe('generateId', () => {
    it('should generate unique IDs', () => {
      mockUtils.generateId
        .mockReturnValueOnce('id-1')
        .mockReturnValueOnce('id-2')
        .mockReturnValueOnce('id-3');

      const id1 = mockUtils.generateId();
      const id2 = mockUtils.generateId();
      const id3 = mockUtils.generateId();

      expect(id1).toBe('id-1');
      expect(id2).toBe('id-2');
      expect(id3).toBe('id-3');
      expect(id1).not.toBe(id2);
      expect(id2).not.toBe(id3);
    });
  });

  describe('sanitizeHtml', () => {
    it('should sanitize HTML content', () => {
      const dirtyHtml = '<script>alert("xss")</script><p>Safe content</p>';
      const cleanHtml = '<p>Safe content</p>';

      mockUtils.sanitizeHtml.mockReturnValue(cleanHtml);

      const result = mockUtils.sanitizeHtml(dirtyHtml);

      expect(result).toBe(cleanHtml);
      expect(mockUtils.sanitizeHtml).toHaveBeenCalledWith(dirtyHtml);
    });

    it('should handle empty input', () => {
      mockUtils.sanitizeHtml.mockReturnValue('');

      const result = mockUtils.sanitizeHtml('');

      expect(result).toBe('');
    });
  });
});
