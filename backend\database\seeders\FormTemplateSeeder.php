<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\FormTemplate;

class FormTemplateSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // SRQ-20 (Self Report Questionnaire 20)
        FormTemplate::updateOrCreate(
            ['code' => 'SRQ20'],
            [
            'code' => 'SRQ20',
            'name' => 'Self Report Questionnaire 20',
            'description' => 'Kuesioner skrining kesehatan mental yang dikembangkan oleh WHO untuk mendeteksi gangguan mental umum dalam 30 hari terakhir.',
            'category' => 'mental_health_screening',
            'questions' => [
                'Apakah Anda sering merasa sakit kepala?',
                'Apakah nafsu makan Anda buruk?',
                'Apakah Anda tidur tidak nyenyak?',
                'Apakah Anda mudah merasa takut?',
                '<PERSON>pakah Anda merasa cemas, tegang, atau khawatir?',
                '<PERSON><PERSON>kah tangan Anda gemetar?',
                '<PERSON><PERSON><PERSON>h pencernaan Anda terganggu atau buruk?',
                'Apakah Anda sulit berpikir jernih?',
                'Apakah Anda merasa tidak bahagia?',
                'Apakah Anda lebih sering menangis?',
                'Apakah Anda merasa sulit untuk menikmati kegiatan sehari-hari?',
                'Apakah Anda mengalami kesulitan dalam mengambil keputusan?',
                'Apakah pekerjaan sehari-hari Anda terganggu?',
                'Apakah Anda tidak mampu melakukan hal-hal yang bermanfaat dalam hidup?',
                'Apakah Anda kehilangan minat pada berbagai hal?',
                'Apakah Anda merasa tidak berharga?',
                'Apakah Anda mempunyai pikiran untuk mengakhiri hidup Anda?',
                'Apakah Anda merasa lelah sepanjang waktu?',
                'Apakah Anda merasa tidak enak di perut?',
                'Apakah Anda mudah lelah?'
            ],
            'scoring_rules' => [
                'type' => 'binary_sum',
                'max_score' => 20,
                'questions' => array_fill(1, 20, [
                    'type' => 'binary',
                    'yes_score' => 1,
                    'no_score' => 0
                ])
            ],
            'interpretation_rules' => [
                [
                    'min_score' => 0,
                    'max_score' => 5,
                    'status' => 'normal',
                    'interpretation' => 'Kondisi Kesehatan Mental Anda Baik',
                    'recommendations' => [
                        'Pertahankan pola hidup sehat yang sudah baik',
                        'Lakukan aktivitas yang Anda nikmati secara rutin',
                        'Jaga hubungan sosial yang positif',
                        'Praktikkan mindfulness atau meditasi',
                        'Lakukan skrining berkala untuk monitoring'
                    ]
                ],
                [
                    'min_score' => 6,
                    'max_score' => 7,
                    'status' => 'concern',
                    'interpretation' => 'Terindikasi Mengalami Gangguan Emosional Ringan',
                    'recommendations' => [
                        'Pertimbangkan konsultasi dengan psikolog',
                        'Praktikkan teknik relaksasi dan manajemen stres',
                        'Jaga pola hidup sehat dan olahraga teratur',
                        'Cari dukungan dari keluarga dan teman terdekat',
                        'Monitor kondisi Anda dalam beberapa minggu ke depan'
                    ]
                ],
                [
                    'min_score' => 8,
                    'max_score' => 20,
                    'status' => 'high_risk',
                    'interpretation' => 'Terindikasi Mengalami Gangguan Mental yang Signifikan',
                    'recommendations' => [
                        'Segera konsultasi dengan psikolog atau psikiater',
                        'Pertimbangkan untuk mendapatkan dukungan dari keluarga dan teman',
                        'Hindari penggunaan alkohol atau zat terlarang',
                        'Jaga pola tidur dan makan yang teratur',
                        'Lakukan aktivitas fisik ringan secara rutin'
                    ]
                ]
            ],
            'time_limit' => 15,
            'is_active' => true,
            'version' => 1
            ]
        );

        // GSE (General Self-Efficacy Scale)
        FormTemplate::updateOrCreate(
            ['code' => 'GSE'],
            [
            'code' => 'GSE',
            'name' => 'General Self-Efficacy Scale',
            'description' => 'Skala untuk mengukur keyakinan diri seseorang dalam menghadapi berbagai situasi sulit.',
            'category' => 'self_efficacy',
            'questions' => [
                'Saya dapat menyelesaikan masalah sulit jika saya berusaha dengan cukup keras',
                'Jika seseorang menentang saya, saya dapat menemukan cara untuk mendapatkan apa yang saya inginkan',
                'Mudah bagi saya untuk berpegang pada tujuan saya dan mencapai target saya',
                'Saya yakin bahwa saya dapat menangani peristiwa tak terduga secara efektif',
                'Berkat kecerdikan saya, saya tahu bagaimana menangani situasi yang tidak terduga',
                'Saya dapat menyelesaikan sebagian besar masalah jika saya berusaha dengan cukup keras',
                'Saya dapat tetap tenang ketika menghadapi kesulitan karena saya dapat mengandalkan kemampuan mengatasi masalah saya',
                'Ketika saya dihadapkan dengan masalah, saya biasanya dapat menemukan beberapa solusi',
                'Jika saya dalam kesulitan, saya biasanya dapat memikirkan solusi',
                'Saya biasanya dapat menangani apa pun yang terjadi pada saya'
            ],
            'scoring_rules' => [
                'type' => 'likert_sum',
                'max_score' => 40,
                'questions' => array_fill(1, 10, [
                    'type' => 'scale',
                    'min' => 1,
                    'max' => 4,
                    'multiplier' => 1
                ])
            ],
            'interpretation_rules' => [
                [
                    'min_score' => 10,
                    'max_score' => 25,
                    'status' => 'low',
                    'interpretation' => 'Self-Efficacy Rendah',
                    'recommendations' => [
                        'Fokus pada pencapaian kecil yang dapat dicapai',
                        'Cari dukungan dari orang terdekat',
                        'Pelajari keterampilan baru secara bertahap',
                        'Praktikkan self-talk yang positif'
                    ]
                ],
                [
                    'min_score' => 26,
                    'max_score' => 32,
                    'status' => 'moderate',
                    'interpretation' => 'Self-Efficacy Sedang',
                    'recommendations' => [
                        'Terus kembangkan kepercayaan diri',
                        'Tantang diri dengan tujuan yang lebih besar',
                        'Refleksikan pencapaian yang sudah diraih',
                        'Belajar dari pengalaman orang lain'
                    ]
                ],
                [
                    'min_score' => 33,
                    'max_score' => 40,
                    'status' => 'high',
                    'interpretation' => 'Self-Efficacy Tinggi',
                    'recommendations' => [
                        'Pertahankan kepercayaan diri yang baik',
                        'Bantu orang lain mengembangkan kepercayaan diri',
                        'Ambil tantangan yang lebih kompleks',
                        'Jadilah mentor bagi orang lain'
                    ]
                ]
            ],
            'time_limit' => 10,
            'is_active' => true,
            'version' => 1
            ]
        );

        // MSCS (Multidimensional Scale of Perceived Social Support)
        FormTemplate::updateOrCreate(
            ['code' => 'MSCS'],
            [
            'code' => 'MSCS',
            'name' => 'Multidimensional Scale of Perceived Social Support',
            'description' => 'Skala untuk mengukur persepsi dukungan sosial dari keluarga, teman, dan orang penting lainnya.',
            'category' => 'social_support',
            'questions' => [
                'Ada orang khusus yang ada saat saya membutuhkan',
                'Ada orang khusus yang dapat saya bagi kegembiraan dan kesedihan',
                'Keluarga saya benar-benar mencoba membantu saya',
                'Saya mendapat dukungan emosional yang saya butuhkan dari keluarga',
                'Saya memiliki orang khusus yang merupakan sumber kenyamanan bagi saya',
                'Teman-teman saya benar-benar mencoba membantu saya',
                'Saya dapat mengandalkan teman-teman saya ketika ada masalah',
                'Saya dapat membicarakan masalah saya dengan keluarga',
                'Saya memiliki teman yang dapat saya bagi kegembiraan dan kesedihan',
                'Ada orang khusus dalam hidup saya yang peduli dengan perasaan saya',
                'Keluarga saya bersedia membantu saya membuat keputusan',
                'Saya dapat membicarakan masalah saya dengan teman-teman'
            ],
            'scoring_rules' => [
                'type' => 'likert_sum',
                'max_score' => 84,
                'questions' => array_fill(1, 12, [
                    'type' => 'scale',
                    'min' => 1,
                    'max' => 7,
                    'multiplier' => 1
                ])
            ],
            'interpretation_rules' => [
                [
                    'min_score' => 12,
                    'max_score' => 36,
                    'status' => 'low',
                    'interpretation' => 'Dukungan Sosial Rendah',
                    'recommendations' => [
                        'Coba untuk lebih terbuka dengan orang terdekat',
                        'Bergabung dengan komunitas atau kelompok sosial',
                        'Pertimbangkan konseling untuk meningkatkan keterampilan sosial',
                        'Mulai membangun hubungan yang lebih bermakna'
                    ]
                ],
                [
                    'min_score' => 37,
                    'max_score' => 60,
                    'status' => 'moderate',
                    'interpretation' => 'Dukungan Sosial Sedang',
                    'recommendations' => [
                        'Pertahankan hubungan yang sudah ada',
                        'Coba untuk lebih aktif dalam interaksi sosial',
                        'Ekspresikan kebutuhan dukungan dengan jelas',
                        'Berikan dukungan kepada orang lain juga'
                    ]
                ],
                [
                    'min_score' => 61,
                    'max_score' => 84,
                    'status' => 'high',
                    'interpretation' => 'Dukungan Sosial Tinggi',
                    'recommendations' => [
                        'Pertahankan jaringan dukungan yang kuat',
                        'Jadilah sumber dukungan bagi orang lain',
                        'Syukuri hubungan yang bermakna dalam hidup',
                        'Terus jaga komunikasi yang baik dengan orang terdekat'
                    ]
                ]
            ],
            'time_limit' => 8,
            'is_active' => true,
            'version' => 1
            ]
        );
    }
}
