<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\FormResponse;
use App\Models\FormTemplate;
use Illuminate\Support\Facades\Auth;

class RoleController extends Controller
{
    /**
     * Get user's role and permissions
     */
    public function getUserRole()
    {
        $user = Auth::user();
        $user->load('role');

        return response()->json([
            'success' => true,
            'data' => [
                'user' => $user,
                'role' => $user->role,
                'permissions' => $user->role->permissions ?? []
            ]
        ]);
    }

    /**
     * Get dashboard data based on user role
     */
    public function getDashboardData()
    {
        $user = Auth::user();
        $user->load('role');

        switch ($user->role->name) {
            case 'admin':
                return $this->getAdminDashboard();
            case 'guru':
                return $this->getGuruDashboard();
            case 'orangtua':
                return $this->getOrangtuaDashboard();
            case 'siswa':
                return $this->getSiswaDashboard();
            default:
                return response()->json(['success' => false, 'message' => 'Invalid role'], 403);
        }
    }

    private function getAdminDashboard()
    {
        $totalUsers = User::count();
        $totalSiswa = User::whereHas('role', function($q) { $q->where('name', 'siswa'); })->count();
        $totalGuru = User::whereHas('role', function($q) { $q->where('name', 'guru'); })->count();
        $totalOrangtua = User::whereHas('role', function($q) { $q->where('name', 'orangtua'); })->count();

        $recentAssessments = \App\Models\FormResponse::with(['user', 'formTemplate'])
            ->latest()
            ->take(10)
            ->get();

        return response()->json([
            'success' => true,
            'data' => [
                'stats' => [
                    'total_users' => $totalUsers,
                    'total_siswa' => $totalSiswa,
                    'total_guru' => $totalGuru,
                    'total_orangtua' => $totalOrangtua,
                    'total_assessments' => \App\Models\FormResponse::count()
                ],
                'recent_assessments' => $recentAssessments,
                'menu_items' => [
                    ['name' => 'Dashboard', 'route' => '/admin/dashboard', 'icon' => '🏠'],
                    ['name' => 'Kelola Pengguna', 'route' => '/admin/users', 'icon' => '👥'],
                    ['name' => 'Kelola Assessment', 'route' => '/admin/assessments', 'icon' => '📋'],
                    ['name' => 'Laporan', 'route' => '/admin/reports', 'icon' => '📊'],
                    ['name' => 'Pengaturan', 'route' => '/admin/settings', 'icon' => '⚙️']
                ]
            ]
        ]);
    }

    private function getGuruDashboard()
    {
        $user = Auth::user();

        // Get students under this teacher
        $students = User::where('role_id', 4) // siswa role
            ->where('is_active', true)
            ->get();

        $studentAssessments = \App\Models\FormResponse::whereIn('user_id', $students->pluck('id'))
            ->with(['user', 'formTemplate'])
            ->latest()
            ->take(20)
            ->get();

        return response()->json([
            'success' => true,
            'data' => [
                'stats' => [
                    'total_students' => $students->count(),
                    'assessments_this_month' => $studentAssessments->where('created_at', '>=', now()->startOfMonth())->count(),
                    'students_need_attention' => $studentAssessments->whereIn('status', ['concern', 'high_risk', 'severe'])->count()
                ],
                'students' => $students,
                'recent_assessments' => $studentAssessments,
                'menu_items' => [
                    ['name' => 'Dashboard', 'route' => '/guru/dashboard', 'icon' => '🏠'],
                    ['name' => 'Daftar Siswa', 'route' => '/guru/students', 'icon' => '👨‍🎓'],
                    ['name' => 'Hasil Assessment', 'route' => '/guru/assessments', 'icon' => '📋'],
                    ['name' => 'Laporan Kelas', 'route' => '/guru/reports', 'icon' => '📊']
                ]
            ]
        ]);
    }

    private function getOrangtuaDashboard()
    {
        $user = Auth::user();

        // Get children of this parent
        $children = $user->children;

        $childrenAssessments = \App\Models\FormResponse::whereIn('user_id', $children->pluck('id'))
            ->with(['user', 'formTemplate'])
            ->latest()
            ->get();

        return response()->json([
            'success' => true,
            'data' => [
                'stats' => [
                    'total_children' => $children->count(),
                    'assessments_this_month' => $childrenAssessments->where('created_at', '>=', now()->startOfMonth())->count(),
                    'latest_assessment' => $childrenAssessments->first()
                ],
                'children' => $children,
                'assessments' => $childrenAssessments,
                'menu_items' => [
                    ['name' => 'Dashboard', 'route' => '/orangtua/dashboard', 'icon' => '🏠'],
                    ['name' => 'Anak Saya', 'route' => '/orangtua/children', 'icon' => '👶'],
                    ['name' => 'Hasil Assessment', 'route' => '/orangtua/assessments', 'icon' => '📋'],
                    ['name' => 'Profil', 'route' => '/orangtua/profile', 'icon' => '👤']
                ]
            ]
        ]);
    }

    private function getSiswaDashboard()
    {
        $user = Auth::user();

        $userAssessments = $user->formResponses()
            ->with('formTemplate')
            ->latest()
            ->get();

        $availableForms = \App\Models\FormTemplate::where('is_active', true)->get();

        return response()->json([
            'success' => true,
            'data' => [
                'stats' => [
                    'total_assessments' => $userAssessments->count(),
                    'assessments_this_month' => $userAssessments->where('created_at', '>=', now()->startOfMonth())->count(),
                    'latest_assessment' => $userAssessments->first(),
                    'available_forms' => $availableForms->count()
                ],
                'assessments' => $userAssessments,
                'available_forms' => $availableForms,
                'menu_items' => [
                    ['name' => 'Dashboard', 'route' => '/siswa/dashboard', 'icon' => '🏠'],
                    ['name' => 'Assessment', 'route' => '/assessments', 'icon' => '📋'],
                    ['name' => 'Riwayat', 'route' => '/history', 'icon' => '📚'],
                    ['name' => 'Profil', 'route' => '/profile', 'icon' => '👤']
                ]
            ]
        ]);
    }
}
