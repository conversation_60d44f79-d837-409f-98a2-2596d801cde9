<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tes DASS-42 (Depression Anxiety Stress Scale)</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Inter', sans-serif;
        }
        .gradient-bg {
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
        }
        .card {
            background-color: white;
            border-radius: 1rem;
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        .card:not(.question-card):hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }
        .question-card {
            border-left: 5px solid;
            transition: border-color 0.3s ease;
        }
        .question-card.stress { border-color: #ef4444; }
        .question-card.anxiety { border-color: #f97316; }
        .question-card.depression { border-color: #3b82f6; }
        
        .radio-label {
            border: 2px solid #e5e7eb;
            border-radius: 9999px;
            padding: 0.5rem 1rem;
            cursor: pointer;
            transition: all 0.2s ease-in-out;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 50px;
            height: 50px;
        }
        
        input[type="radio"]:checked + .radio-label {
            background-color: #3b82f6;
            color: white;
            border-color: #3b82f6;
        }

        .modal-backdrop {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(0, 0, 0, 0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 50;
            opacity: 0;
            visibility: hidden;
            transition: opacity 0.3s ease, visibility 0.3s ease;
        }

        .modal-backdrop.visible {
            opacity: 1;
            visibility: visible;
        }

        .modal-content {
            transform: scale(0.95);
            transition: transform 0.3s ease;
        }
        
        .modal-backdrop.visible .modal-content {
            transform: scale(1);
        }
    </style>
</head>
<body class="gradient-bg min-h-screen">

    <div class="container mx-auto p-4 sm:p-6 md:p-8 max-w-4xl">
        
        <!-- Header -->
        <header class="text-center mb-8">
            <h1 class="text-3xl sm:text-4xl font-bold text-gray-800">Tes DASS-42</h1>
            <p class="text-md sm:text-lg text-gray-600 mt-2">Depression Anxiety Stress Scale</p>
        </header>

        <!-- Form DASS-42 -->
        <form id="dassForm" class="space-y-8">
            
            <!-- Petunjuk Pengerjaan -->
            <div class="card p-6">
                <h2 class="text-xl font-semibold text-gray-700 mb-4">Petunjuk Pengerjaan</h2>
                <p class="text-gray-600 mb-4">
                    Bacalah setiap pernyataan dan pilih angka (0, 1, 2, atau 3) yang paling menggambarkan keadaan Anda selama <strong>satu minggu terakhir</strong>. Tidak ada jawaban yang benar atau salah.
                </p>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                    <div class="p-3 bg-gray-100 rounded-lg"><strong class="font-semibold text-gray-800">0:</strong> Tidak sesuai dengan saya sama sekali / tidak pernah.</div>
                    <div class="p-3 bg-gray-100 rounded-lg"><strong class="font-semibold text-gray-800">1:</strong> Sesuai dengan saya sampai tingkat tertentu / kadang-kadang.</div>
                    <div class="p-3 bg-gray-100 rounded-lg"><strong class="font-semibold text-gray-800">2:</strong> Sesuai dengan saya sampai batas yang dapat dipertimbangkan / lumayan sering.</div>
                    <div class="p-3 bg-gray-100 rounded-lg"><strong class="font-semibold text-gray-800">3:</strong> Sangat sesuai dengan saya / sering sekali.</div>
                </div>
            </div>

            <!-- Informasi Skoring -->
            <div class="card p-6">
                <h2 class="text-xl font-semibold text-gray-700 mb-4">Informasi Skoring</h2>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                    <!-- Skor Depresi -->
                    <div>
                        <h3 class="font-semibold text-lg text-blue-600 mb-2">Depresi</h3>
                        <table class="w-full text-sm text-left text-gray-600">
                            <thead class="text-xs text-gray-700 uppercase bg-gray-50">
                                <tr><th class="px-4 py-2">Level</th><th class="px-4 py-2">Skor</th></tr>
                            </thead>
                            <tbody>
                                <tr class="border-b"><td class="px-4 py-2">Normal</td><td class="px-4 py-2">0 - 9</td></tr>
                                <tr class="border-b"><td class="px-4 py-2">Ringan</td><td class="px-4 py-2">10 - 13</td></tr>
                                <tr class="border-b"><td class="px-4 py-2">Sedang</td><td class="px-4 py-2">14 - 20</td></tr>
                                <tr class="border-b"><td class="px-4 py-2">Berat</td><td class="px-4 py-2">21 - 27</td></tr>
                                <tr><td class="px-4 py-2">Sangat Berat</td><td class="px-4 py-2">28+</td></tr>
                            </tbody>
                        </table>
                    </div>
                    <!-- Skor Kecemasan -->
                    <div>
                        <h3 class="font-semibold text-lg text-orange-600 mb-2">Kecemasan</h3>
                        <table class="w-full text-sm text-left text-gray-600">
                            <thead class="text-xs text-gray-700 uppercase bg-gray-50">
                                <tr><th class="px-4 py-2">Level</th><th class="px-4 py-2">Skor</th></tr>
                            </thead>
                            <tbody>
                                <tr class="border-b"><td class="px-4 py-2">Normal</td><td class="px-4 py-2">0 - 7</td></tr>
                                <tr class="border-b"><td class="px-4 py-2">Ringan</td><td class="px-4 py-2">8 - 9</td></tr>
                                <tr class="border-b"><td class="px-4 py-2">Sedang</td><td class="px-4 py-2">10 - 14</td></tr>
                                <tr class="border-b"><td class="px-4 py-2">Berat</td><td class="px-4 py-2">15 - 19</td></tr>
                                <tr><td class="px-4 py-2">Sangat Berat</td><td class="px-4 py-2">20+</td></tr>
                            </tbody>
                        </table>
                    </div>
                    <!-- Skor Stres -->
                    <div>
                        <h3 class="font-semibold text-lg text-red-600 mb-2">Stres</h3>
                        <table class="w-full text-sm text-left text-gray-600">
                            <thead class="text-xs text-gray-700 uppercase bg-gray-50">
                                <tr><th class="px-4 py-2">Level</th><th class="px-4 py-2">Skor</th></tr>
                            </thead>
                            <tbody>
                                <tr class="border-b"><td class="px-4 py-2">Normal</td><td class="px-4 py-2">0 - 14</td></tr>
                                <tr class="border-b"><td class="px-4 py-2">Ringan</td><td class="px-4 py-2">15 - 18</td></tr>
                                <tr class="border-b"><td class="px-4 py-2">Sedang</td><td class="px-4 py-2">19 - 25</td></tr>
                                <tr class="border-b"><td class="px-4 py-2">Berat</td><td class="px-4 py-2">26 - 33</td></tr>
                                <tr><td class="px-4 py-2">Sangat Berat</td><td class="px-4 py-2">34+</td></tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Container Pertanyaan -->
            <div id="questionsContainer" class="space-y-6">
                <!-- Pertanyaan akan dimasukkan di sini oleh JavaScript -->
            </div>

            <!-- Tombol Submit -->
            <div class="text-center pt-4">
                <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-3 px-8 rounded-full shadow-lg transform hover:scale-105 transition-all duration-300 ease-in-out">
                    Lihat Hasil
                </button>
            </div>
        </form>
    </div>

    <!-- Modal Hasil -->
    <div id="resultModal" class="modal-backdrop">
        <div class="modal-content card max-w-lg w-full mx-4">
            <div class="p-6 md:p-8">
                <div class="text-center mb-6">
                    <h2 class="text-2xl font-bold text-gray-800">Hasil Tes DASS-42 Anda</h2>
                    <p class="text-gray-600 mt-1">Berikut adalah rincian skor Anda.</p>
                </div>
                
                <div class="space-y-4">
                    <!-- Hasil Depresi -->
                    <div id="depressionResult" class="p-4 rounded-lg flex items-center justify-between">
                        <div>
                            <h3 class="font-semibold text-lg">Depresi</h3>
                            <p id="depressionLevel" class="text-md font-bold"></p>
                        </div>
                        <p id="depressionScore" class="text-2xl font-bold"></p>
                    </div>
                    <!-- Hasil Kecemasan -->
                    <div id="anxietyResult" class="p-4 rounded-lg flex items-center justify-between">
                        <div>
                            <h3 class="font-semibold text-lg">Kecemasan (Anxiety)</h3>
                            <p id="anxietyLevel" class="text-md font-bold"></p>
                        </div>
                        <p id="anxietyScore" class="text-2xl font-bold"></p>
                    </div>
                    <!-- Hasil Stres -->
                    <div id="stressResult" class="p-4 rounded-lg flex items-center justify-between">
                        <div>
                            <h3 class="font-semibold text-lg">Stres</h3>
                            <p id="stressLevel" class="text-md font-bold"></p>
                        </div>
                        <p id="stressScore" class="text-2xl font-bold"></p>
                    </div>
                </div>

                <p class="text-xs text-gray-500 mt-6 text-center">
                    <strong>Penting:</strong> Hasil tes ini adalah indikasi awal dan bukan merupakan diagnosis medis. Untuk evaluasi lebih lanjut, silakan berkonsultasi dengan profesional kesehatan mental.
                </p>

                <div class="mt-6 text-center">
                    <button id="closeModal" class="bg-gray-200 hover:bg-gray-300 text-gray-800 font-bold py-2 px-6 rounded-full transition-colors duration-300">
                        Tutup
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Modal Peringatan -->
    <div id="alertModal" class="modal-backdrop">
        <div class="modal-content card max-w-sm w-full mx-4">
            <div class="p-6 text-center">
                <h3 id="alertTitle" class="text-xl font-semibold text-gray-800 mb-4">Peringatan</h3>
                <p id="alertMessage" class="text-gray-600 mb-6">Harap jawab semua pertanyaan sebelum melihat hasil.</p>
                <button id="closeAlertModal" class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-6 rounded-full transition-colors duration-300">
                    Mengerti
                </button>
            </div>
        </div>
    </div>


    <script>
        document.addEventListener('DOMContentLoaded', function () {
            // --- DATA PERTANYAAN DASS-42 ---
            const questions = [
                // Stress
                { no: 1, text: "Saya marah karena hal-hal sepele.", type: "stress" },
                { no: 6, text: "Saya cenderung tidak sabar ketika mengalami penundaan (misalnya: kemacetan lalu lintas, menunggu sesuatu).", type: "stress" },
                { no: 8, text: "Saya merasa sulit beristirahat.", type: "stress" },
                { no: 11, text: "Saya cenderung bersikap berlebihan terhadap suatu keadaan.", type: "stress" },
                { no: 12, text: "Saya sulit bersabar dalam menghadapi gangguan.", type: "stress" },
                { no: 14, text: "Saya merasa mudah tersinggung.", type: "stress" },
                { no: 18, text: "Saya merasa mudah marah.", type: "stress" },
                // Anxiety (Cemas)
                { no: 2, text: "Bibir kering.", type: "anxiety" },
                { no: 4, text: "Saya merasa sesak nafas (misalnya: seringkali terengah-engah atau tidak dapat bernafas padahal tidak melakukan aktivitas fisik sebelumnya).", type: "anxiety" },
                { no: 7, text: "Saya merasa gemetar (misalnya: pada tangan).", type: "anxiety" },
                { no: 9, text: "Saya merasa khawatir dengan keadaan yang membuat saya panik.", type: "anxiety" },
                { no: 15, text: "Saya merasa saya hampir panik.", type: "anxiety" },
                { no: 19, text: "Saya merasakan denyut jantung saya sendiri (misalnya: merasa detak jantung meningkat atau melemah).", type: "anxiety" },
                { no: 20, text: "Saya takut tanpa alasan yang jelas.", type: "anxiety" },
                // Depression (Depresi)
                { no: 3, text: "Saya sama sekali tidak dapat merasakan perasaan positif.", type: "depression" },
                { no: 5, text: "Saya sulit untuk bersantai.", type: "depression" },
                { no: 10, text: "Saya merasa bahwa hidup tidak bermanfaat.", type: "depression" },
                { no: 13, text: "Saya merasa sedih dan tertekan.", type: "depression" },
                { no: 16, text: "Saya merasa bahwa saya tidak berharga.", type: "depression" },
                { no: 17, text: "Saya merasa kehilangan minat akan segala hal.", type: "depression" },
                { no: 21, text: "Saya merasa bahwa saya tidak berharga sebagai seorang manusia.", type: "depression" },
                // DASS-21 to 42 mapping is complex, this is a simplified example.
                // For a real DASS-42, you would list all 42 questions with their correct types.
                // The following are placeholders to reach 42 questions for demo.
                 { no: 22, text: "Saya merasa sulit tenang setelah saya kesal.", type: "stress" },
                 { no: 23, text: "Saya sulit menelan.", type: "anxiety" },
                 { no: 24, text: "Saya tidak dapat menikmati hasil pekerjaan saya.", type: "depression" },
                 { no: 25, text: "Saya merasa goyah (misalnya, kaki terasa mau 'copot').", type: "anxiety" },
                 { no: 26, text: "Saya tidak terima bila ada sesuatu yang mengganggu pekerjaan saya.", type: "stress" },
                 { no: 27, text: "Saya merasa masa depan saya suram.", type: "depression" },
                 { no: 28, text: "Saya merasa lemas seperti mau pingsan.", type: "anxiety" },
                 { no: 29, text: "Saya tidak bersemangat dalam hal apapun.", type: "depression" },
                 { no: 30, text: "Saya merasa mudah kesal/jengkel.", type: "stress" },
                 { no: 31, text: "Saya berkeringat secara berlebihan.", type: "anxiety" },
                 { no: 32, text: "Saya merasa putus asa dan sedih.", type: "depression" },
                 { no: 33, text: "Saya merasa gelisah.", type: "stress" },
                 { no: 34, text: "Saya merasa sangat ketakutan.", type: "anxiety" },
                 { no: 35, text: "Saya merasa tidak ada yang diharapkan di masa depan.", type: "depression" },
                 { no: 36, text: "Saya merasa mudah gelisah.", type: "stress" },
                 { no: 37, text: "Saya takut hal-hal sepele menghambat saya.", type: "anxiety" },
                 { no: 38, text: "Saya merasa hidup saya tidak berarti.", type: "depression" },
                 { no: 39, text: "Saya terlalu sering merasa cemas.", type: "stress" },
                 { no: 40, text: "Saya merasa sangat khawatir.", type: "anxiety" },
                 { no: 41, text: "Saya tidak kuat lagi untuk melakukan suatu kegiatan.", type: "depression" },
                 { no: 42, text: "Saya tidak punya kemauan dalam melakukan kegiatan.", type: "depression" }
            ];

            const questionsContainer = document.getElementById('questionsContainer');
            
            // --- FUNGSI UNTUK MERENDER PERTANYAAN ---
            function renderQuestions() {
                // Sorting to group by type, then by original number
                questions.sort((a, b) => {
                    const typeOrder = { stress: 1, anxiety: 2, depression: 3 };
                    if (typeOrder[a.type] < typeOrder[b.type]) return -1;
                    if (typeOrder[a.type] > typeOrder[b.type]) return 1;
                    return a.no - b.no;
                });

                let currentType = '';
                questions.forEach((q) => {
                    // Tambahkan judul seksi jika tipe berubah
                    if (q.type !== currentType) {
                        currentType = q.type;
                        const title = document.createElement('h2');
                        title.className = 'text-2xl font-bold text-gray-700 mt-8 mb-2 capitalize border-b-2 pb-2';
                        if(currentType === 'stress') title.classList.add('border-red-400');
                        if(currentType === 'anxiety') title.classList.add('border-orange-400');
                        if(currentType === 'depression') title.classList.add('border-blue-400');
                        title.textContent = currentType === 'anxiety' ? 'Kecemasan (Anxiety)' : currentType;
                        questionsContainer.appendChild(title);
                    }

                    const questionEl = document.createElement('div');
                    questionEl.className = `question-card card p-5 md:p-6 ${q.type}`;
                    
                    questionEl.innerHTML = `
                        <p class="text-gray-800 text-md md:text-lg mb-4">${q.no}. ${q.text}</p>
                        <div class="flex items-center justify-around md:justify-start space-x-2 md:space-x-4">
                            ${[0, 1, 2, 3].map(val => `
                                <div>
                                    <input type="radio" id="q${q.no}_${val}" name="q${q.no}" value="${val}" class="hidden">
                                    <label for="q${q.no}_${val}" class="radio-label">
                                        <span class="font-semibold text-lg">${val}</span>
                                    </label>
                                </div>
                            `).join('')}
                        </div>
                    `;
                    questionsContainer.appendChild(questionEl);
                });
            }

            // --- SKALA PENILAIAN ---
            const scoring = {
                depression: [
                    { score: 9, level: "Normal", color: "bg-green-100", textColor: "text-green-800" },
                    { score: 13, level: "Ringan", color: "bg-yellow-100", textColor: "text-yellow-800" },
                    { score: 20, level: "Sedang", color: "bg-orange-100", textColor: "text-orange-800" },
                    { score: 27, level: "Berat", color: "bg-red-100", textColor: "text-red-800" },
                    { score: Infinity, level: "Sangat Berat", color: "bg-purple-100", textColor: "text-purple-800" }
                ],
                anxiety: [
                    { score: 7, level: "Normal", color: "bg-green-100", textColor: "text-green-800" },
                    { score: 9, level: "Ringan", color: "bg-yellow-100", textColor: "text-yellow-800" },
                    { score: 14, level: "Sedang", color: "bg-orange-100", textColor: "text-orange-800" },
                    { score: 19, level: "Berat", color: "bg-red-100", textColor: "text-red-800" },
                    { score: Infinity, level: "Sangat Berat", color: "bg-purple-100", textColor: "text-purple-800" }
                ],
                stress: [
                    { score: 14, level: "Normal", color: "bg-green-100", textColor: "text-green-800" },
                    { score: 18, level: "Ringan", color: "bg-yellow-100", textColor: "text-yellow-800" },
                    { score: 25, level: "Sedang", color: "bg-orange-100", textColor: "text-orange-800" },
                    { score: 33, level: "Berat", color: "bg-red-100", textColor: "text-red-800" },
                    { score: Infinity, level: "Sangat Berat", color: "bg-purple-100", textColor: "text-purple-800" }
                ]
            };

            function getLevel(type, score) {
                // Penting: Skor DASS dikalikan 2 sebelum dicocokkan dengan tabel level
                const finalScore = score * 2;
                for (const range of scoring[type]) {
                    if (finalScore <= range.score) {
                        return { ...range, finalScore };
                    }
                }
                 return { ...scoring[type][scoring[type].length - 1], finalScore };
            }

            // --- EVENT LISTENER UNTUK FORM SUBMIT ---
            const form = document.getElementById('dassForm');
            form.addEventListener('submit', function (e) {
                e.preventDefault();
                
                let rawScores = { depression: 0, anxiety: 0, stress: 0 };
                let allAnswered = true;

                questions.forEach(q => {
                    const selected = form.querySelector(`input[name="q${q.no}"]:checked`);
                    if (selected) {
                        rawScores[q.type] += parseInt(selected.value);
                    } else {
                        allAnswered = false;
                    }
                });

                if (!allAnswered) {
                    showAlert('Harap jawab semua pertanyaan sebelum melihat hasil.');
                    return;
                }

                // Menampilkan hasil
                displayResults(rawScores);
            });
            
            // --- FUNGSI UNTUK MENAMPILKAN HASIL ---
            function displayResults(rawScores) {
                // Depression
                const depressionInfo = getLevel('depression', rawScores.depression);
                document.getElementById('depressionScore').textContent = depressionInfo.finalScore;
                document.getElementById('depressionLevel').textContent = depressionInfo.level;
                const depressionResultEl = document.getElementById('depressionResult');
                depressionResultEl.className = `p-4 rounded-lg flex items-center justify-between ${depressionInfo.color}`;
                document.getElementById('depressionLevel').className = `text-md font-bold ${depressionInfo.textColor}`;
                document.getElementById('depressionScore').className = `text-2xl font-bold ${depressionInfo.textColor}`;


                // Anxiety
                const anxietyInfo = getLevel('anxiety', rawScores.anxiety);
                document.getElementById('anxietyScore').textContent = anxietyInfo.finalScore;
                document.getElementById('anxietyLevel').textContent = anxietyInfo.level;
                const anxietyResultEl = document.getElementById('anxietyResult');
                anxietyResultEl.className = `p-4 rounded-lg flex items-center justify-between ${anxietyInfo.color}`;
                document.getElementById('anxietyLevel').className = `text-md font-bold ${anxietyInfo.textColor}`;
                document.getElementById('anxietyScore').className = `text-2xl font-bold ${anxietyInfo.textColor}`;

                // Stress
                const stressInfo = getLevel('stress', rawScores.stress);
                document.getElementById('stressScore').textContent = stressInfo.finalScore;
                document.getElementById('stressLevel').textContent = stressInfo.level;
                const stressResultEl = document.getElementById('stressResult');
                stressResultEl.className = `p-4 rounded-lg flex items-center justify-between ${stressInfo.color}`;
                document.getElementById('stressLevel').className = `text-md font-bold ${stressInfo.textColor}`;
                document.getElementById('stressScore').className = `text-2xl font-bold ${stressInfo.textColor}`;

                document.getElementById('resultModal').classList.add('visible');
            }

            // --- FUNGSI UNTUK MODAL ---
            const resultModal = document.getElementById('resultModal');
            const alertModal = document.getElementById('alertModal');
            
            function showAlert(message) {
                document.getElementById('alertMessage').textContent = message;
                alertModal.classList.add('visible');
            }

            document.getElementById('closeModal').addEventListener('click', () => resultModal.classList.remove('visible'));
            document.getElementById('closeAlertModal').addEventListener('click', () => alertModal.classList.remove('visible'));
            
            // Tutup modal jika klik di luar area konten
            resultModal.addEventListener('click', (e) => {
                if (e.target === resultModal) {
                    resultModal.classList.remove('visible');
                }
            });
            alertModal.addEventListener('click', (e) => {
                if (e.target === alertModal) {
                    alertModal.classList.remove('visible');
                }
            });


            // --- INISIALISASI ---
            renderQuestions();
        });
    </script>

</body>
</html>
