/**
 * Dashboard Page Logic with API integration
 */
document.addEventListener('DOMContentLoaded', () => {
    const auth = window.auth;
    const Utils = window.Utils;

    if (!auth.isAuthenticated()) {
        window.location.href = 'index.html';
        return;
    }

    // Elements
    const userNameEl = document.getElementById('user-name');
    const userInitialsEl = document.getElementById('user-initials');
    const currentTimeEl = document.getElementById('current-time');
    const logoutBtn = document.getElementById('logout-btn');
    const sidebarLinks = document.querySelectorAll('.sidebar-link');
    const pageTitle = document.getElementById('page-title');

    const pages = {
        dashboard: document.getElementById('dashboard-page'),
        assessment: document.getElementById('assessment-page'),
        history: document.getElementById('history-page'),
        analytics: document.getElementById('analytics-page'),
        profile: document.getElementById('profile-page')
    };

    // Show user info
    const user = auth.getCurrentUser();
    if (user) {
        userNameEl.textContent = `${user.first_name} ${user.last_name}`;
        userInitialsEl.textContent = Utils.getInitials(`${user.first_name} ${user.last_name}`);
    } else {
        userNameEl.textContent = 'Pengguna';
        userInitialsEl.textContent = '?';
    }

    // Update current time every minute
    function updateTime() {
        currentTimeEl.textContent = Utils.formatDate(new Date(), { hour: '2-digit', minute: '2-digit' });
    }
    updateTime();
    setInterval(updateTime, 60000);

    // Sidebar navigation
    sidebarLinks.forEach(link => {
        link.addEventListener('click', (e) => {
            e.preventDefault();
            const page = link.getAttribute('data-page');
            if (!page || !pages[page]) return;

            // Update active link
            sidebarLinks.forEach(l => l.classList.remove('sidebar-active'));
            link.classList.add('sidebar-active');

            // Update page title
            pageTitle.textContent = link.textContent.trim();

            // Show selected page, hide others
            Object.entries(pages).forEach(([key, el]) => {
                if (key === page) {
                    el.classList.remove('hidden');
                } else {
                    el.classList.add('hidden');
                }
            });
        });
    });

    // Logout button
    logoutBtn.addEventListener('click', () => {
        auth.logout();
    });

    // Mobile menu toggle
    const mobileMenuBtn = document.getElementById('mobile-menu-btn');
    const sidebar = document.getElementById('sidebar');
    const mobileOverlay = document.getElementById('mobile-overlay');

    mobileMenuBtn.addEventListener('click', () => {
        sidebar.classList.toggle('mobile-menu-open');
        sidebar.classList.toggle('mobile-menu-closed');
        mobileOverlay.classList.toggle('hidden');
    });

    mobileOverlay.addEventListener('click', () => {
        sidebar.classList.add('mobile-menu-closed');
        sidebar.classList.remove('mobile-menu-open');
        mobileOverlay.classList.add('hidden');
    });

    // Start assessment button
    const startAssessmentBtn = document.getElementById('start-assessment-btn');
    if (startAssessmentBtn) {
        startAssessmentBtn.addEventListener('click', () => {
            // Navigate to assessment page
            const assessmentLink = Array.from(sidebarLinks).find(l => l.getAttribute('data-page') === 'assessment');
            if (assessmentLink) {
                assessmentLink.click();
            }
        });
    }

    // Load dashboard data from API
    async function loadDashboardData() {
        try {
            const response = await fetch('http://localhost:8000/api/assessments/dashboard-stats', {
                headers: {
                    'Authorization': `Bearer ${localStorage.getItem('authToken')}`,
                    'Accept': 'application/json'
                }
            });
            if (!response.ok) throw new Error('Failed to fetch dashboard data');
            const data = await response.json();

            // Display total assessments
            const totalAssessmentsEl = document.getElementById('total-assessments');
            if (totalAssessmentsEl) {
                totalAssessmentsEl.textContent = data.total_assessments;
            }

            // Display latest assessment score and status
            const latestScoreEl = document.getElementById('latest-score');
            const latestStatusEl = document.getElementById('latest-status');
            if (data.latest_assessment) {
                if (latestScoreEl) latestScoreEl.textContent = data.latest_assessment.total_score;
                if (latestStatusEl) latestStatusEl.textContent = data.latest_assessment.status === 'concern' ? 'Terindikasi Gangguan' : 'Normal';
            }

            // TODO: Render charts for trend and distribution using Chart.js or similar

        } catch (error) {
            console.error('Error loading dashboard data:', error);
        }
    }

    loadDashboardData();
});
