/**
 * Authentication Tests
 */

// Mock the auth module
const mockAuth = {
  login: jest.fn(),
  logout: jest.fn(),
  register: jest.fn(),
  isAuthenticated: jest.fn(),
  getToken: jest.fn(),
  getUser: jest.fn(),
  setToken: jest.fn(),
  setUser: jest.fn(),
};

// Mock window.auth
global.auth = mockAuth;

describe('Authentication', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    localStorage.clear();
    sessionStorage.clear();
  });

  describe('login', () => {
    it('should login successfully with valid credentials', async () => {
      const mockResponse = testUtils.mockApiResponse({
        token: 'mock-token',
        user: testUtils.mockUser(),
      });

      fetch.mockResolvedValueOnce({
        ok: true,
        status: 200,
        json: jest.fn().mockResolvedValue(mockResponse),
      });

      mockAuth.login.mockResolvedValue(mockResponse);

      const result = await mockAuth.login('<EMAIL>', 'password');

      expect(mockAuth.login).toHaveBeenCalledWith('<EMAIL>', 'password');
      expect(result.success).toBe(true);
      expect(result.data.token).toBe('mock-token');
      expect(result.data.user.email).toBe('<EMAIL>');
    });

    it('should fail login with invalid credentials', async () => {
      const mockResponse = testUtils.mockApiResponse(null, false);
      mockResponse.message = 'Invalid credentials';

      fetch.mockResolvedValueOnce({
        ok: false,
        status: 401,
        json: jest.fn().mockResolvedValue(mockResponse),
      });

      mockAuth.login.mockResolvedValue(mockResponse);

      const result = await mockAuth.login('<EMAIL>', 'wrong-password');

      expect(mockAuth.login).toHaveBeenCalledWith('<EMAIL>', 'wrong-password');
      expect(result.success).toBe(false);
      expect(result.message).toBe('Invalid credentials');
    });

    it('should handle network errors', async () => {
      fetch.mockRejectedValueOnce(new Error('Network error'));
      mockAuth.login.mockRejectedValue(new Error('Network error'));

      await expect(mockAuth.login('<EMAIL>', 'password')).rejects.toThrow('Network error');
    });
  });

  describe('logout', () => {
    it('should logout successfully', async () => {
      localStorage.setItem('auth_token', 'mock-token');
      localStorage.setItem('user_data', JSON.stringify(testUtils.mockUser()));

      mockAuth.logout.mockResolvedValue({ success: true });

      const result = await mockAuth.logout();

      expect(mockAuth.logout).toHaveBeenCalled();
      expect(result.success).toBe(true);
    });
  });

  describe('register', () => {
    it('should register successfully with valid data', async () => {
      const userData = {
        name: 'Test User',
        email: '<EMAIL>',
        password: 'password123',
        password_confirmation: 'password123',
      };

      const mockResponse = testUtils.mockApiResponse({
        token: 'mock-token',
        user: testUtils.mockUser(userData),
      });

      fetch.mockResolvedValueOnce({
        ok: true,
        status: 201,
        json: jest.fn().mockResolvedValue(mockResponse),
      });

      mockAuth.register.mockResolvedValue(mockResponse);

      const result = await mockAuth.register(userData);

      expect(mockAuth.register).toHaveBeenCalledWith(userData);
      expect(result.success).toBe(true);
      expect(result.data.user.name).toBe('Test User');
    });

    it('should fail registration with invalid data', async () => {
      const userData = {
        name: '',
        email: 'invalid-email',
        password: '123',
        password_confirmation: '456',
      };

      const mockResponse = testUtils.mockApiResponse(null, false);
      mockResponse.errors = {
        name: ['The name field is required.'],
        email: ['The email must be a valid email address.'],
        password: ['The password must be at least 8 characters.'],
      };

      fetch.mockResolvedValueOnce({
        ok: false,
        status: 422,
        json: jest.fn().mockResolvedValue(mockResponse),
      });

      mockAuth.register.mockResolvedValue(mockResponse);

      const result = await mockAuth.register(userData);

      expect(result.success).toBe(false);
      expect(result.errors).toBeDefined();
      expect(result.errors.name).toContain('The name field is required.');
    });
  });

  describe('isAuthenticated', () => {
    it('should return true when user is authenticated', () => {
      localStorage.setItem('auth_token', 'mock-token');
      mockAuth.isAuthenticated.mockReturnValue(true);

      const result = mockAuth.isAuthenticated();

      expect(result).toBe(true);
    });

    it('should return false when user is not authenticated', () => {
      localStorage.removeItem('auth_token');
      mockAuth.isAuthenticated.mockReturnValue(false);

      const result = mockAuth.isAuthenticated();

      expect(result).toBe(false);
    });
  });

  describe('token management', () => {
    it('should get token from localStorage', () => {
      const token = 'mock-token';
      localStorage.setItem('auth_token', token);
      mockAuth.getToken.mockReturnValue(token);

      const result = mockAuth.getToken();

      expect(result).toBe(token);
    });

    it('should set token to localStorage', () => {
      const token = 'new-mock-token';
      mockAuth.setToken.mockImplementation((t) => {
        localStorage.setItem('auth_token', t);
      });

      mockAuth.setToken(token);

      expect(mockAuth.setToken).toHaveBeenCalledWith(token);
    });
  });

  describe('user management', () => {
    it('should get user from localStorage', () => {
      const user = testUtils.mockUser();
      localStorage.setItem('user_data', JSON.stringify(user));
      mockAuth.getUser.mockReturnValue(user);

      const result = mockAuth.getUser();

      expect(result).toEqual(user);
    });

    it('should set user to localStorage', () => {
      const user = testUtils.mockUser();
      mockAuth.setUser.mockImplementation((u) => {
        localStorage.setItem('user_data', JSON.stringify(u));
      });

      mockAuth.setUser(user);

      expect(mockAuth.setUser).toHaveBeenCalledWith(user);
    });
  });
});
