<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>@yield('title', 'SantriMental - Platform Kesehatan Mental')</title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    
    <!-- Additional Scripts -->
    @stack('scripts-head')
    
    <style>
        body {
            font-family: 'Inter', sans-serif;
        }
        
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        
        .glass-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(25px);
            -webkit-backdrop-filter: blur(25px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        @yield('styles')
    </style>
</head>
<body class="@yield('body-class', 'gradient-bg min-h-screen')">
    <!-- Loading Overlay -->
    <div id="loading-overlay" class="hidden fixed inset-0 bg-black/50 flex items-center justify-center z-50">
        <div class="glass-card p-6 rounded-2xl text-center">
            <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-white mx-auto mb-4"></div>
            <p class="text-white">Memproses...</p>
        </div>
    </div>

    <!-- Toast Notification -->
    <div id="toast" class="hidden fixed top-4 right-4 glass-card p-4 rounded-lg shadow-lg z-50 max-w-sm">
        <div class="flex items-center">
            <div id="toast-icon" class="mr-3"></div>
            <div>
                <p id="toast-title" class="font-semibold text-white"></p>
                <p id="toast-message" class="text-purple-200 text-sm"></p>
            </div>
        </div>
    </div>

    @yield('content')

    <!-- Base Scripts -->
    <script src="{{ asset('js/utils.js') }}"></script>
    @stack('scripts')
</body>
</html>
