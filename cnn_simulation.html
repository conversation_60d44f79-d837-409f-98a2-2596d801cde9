<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simulasi Interaktif CNN dengan Animasi & Ekspor</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- jsPDF and html2canvas for PDF export -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js" xintegrity="sha512-BNaRQnYJYiPSqHHDb58B0yaPfCu+Wgds8Gp/gU33kqBtgNS4tSPHuGibyoVBL5gI9kDXDCe9WSwszG/lcQCaw==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Inter', sans-serif;
        }
        .grid-cell {
            width: 35px;
            height: 35px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 500;
            border: 1px solid #e2e8f0;
            transition: all 0.2s ease-in-out;
        }
        .grid-cell.active {
            background-color: #3b82f6;
            color: white;
        }
        .grid-cell.kernel {
            background-color: #f97316;
            color: white;
        }
        .grid-cell.output {
            background-color: #16a34a;
            color: white;
        }
        .grid-cell.highlight-input {
            background-color: #a5b4fc; /* Light indigo */
            border-color: #4f46e5;
            transform: scale(1.05);
        }
        .grid-cell.highlight-output {
            background-color: #6ee7b7; /* Light green */
            border-color: #059669;
            transform: scale(1.1);
        }
        .math-explanation {
            background-color: #f8fafc;
            border-left: 4px solid #3b82f6;
            padding: 1rem;
            margin-top: 1rem;
            font-family: 'Courier New', Courier, monospace;
            white-space: pre-wrap;
            word-wrap: break-word;
            min-height: 100px;
        }
        .step-card {
            background-color: white;
            border-radius: 0.75rem;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
        }
        .bar-chart {
            display: flex;
            align-items: flex-end;
            height: 150px;
            gap: 1rem;
        }
        .bar {
            flex-grow: 1;
            background-color: #3b82f6;
            text-align: center;
            color: white;
            transition: height 0.5s ease-out;
            border-radius: 0.25rem 0.25rem 0 0;
            padding: 4px;
        }
        .export-btn {
             background-color: #64748b; color: white; font-weight: 500; padding: 0.5rem 1rem; border-radius: 0.375rem; transition: background-color 0.2s;
        }
        .export-btn:hover {
            background-color: #475569;
        }

        @media print {
            body {
                -webkit-print-color-adjust: exact !important;
                print-color-adjust: exact !important;
            }
            .no-print {
                display: none !important;
            }
            .step-card, .container {
                box-shadow: none !important;
                border: 1px solid #e2e8f0;
                padding: 1rem;
                margin: 0;
            }
            .container {
                width: 100%;
                max-width: 100%;
                padding: 0;
            }
            body, .container {
                background-color: white !important;
            }
        }
    </style>
</head>
<body class="bg-slate-50 text-slate-800">

    <div class="container mx-auto p-4 md:p-8">
        <header class="text-center mb-8 no-print">
            <h1 class="text-3xl md:text-4xl font-bold text-slate-900">Simulasi Interaktif: Cara Kerja CNN</h1>
            <p class="mt-2 text-lg text-slate-600">Pelajari fundamental Convolutional Neural Network langkah demi langkah.</p>
        </header>

        <div class="grid grid-cols-1 lg:grid-cols-12 gap-8">
            <!-- Kolom Kontrol -->
            <aside class="lg:col-span-4 no-print">
                <div class="bg-white p-6 rounded-xl shadow-md sticky top-8">
                    <h2 class="text-2xl font-bold mb-4">Parameter Simulasi</h2>
                    
                    <div class="mb-6">
                        <label for="input-select" class="block text-sm font-medium text-slate-700 mb-2">1. Pilih Input (Matriks 5x5)</label>
                        <select id="input-select" class="w-full p-2 border border-slate-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500">
                            <option value="vertical-line">Garis Vertikal</option>
                            <option value="horizontal-line">Garis Horizontal</option>
                            <option value="cross">Tanda Silang</option>
                            <option value="diagonal">Garis Diagonal</option>
                        </select>
                    </div>

                    <div class="mb-6">
                        <label for="kernel-select" class="block text-sm font-medium text-slate-700 mb-2">2. Pilih Kernel/Filter (Matriks 3x3)</label>
                        <select id="kernel-select" class="w-full p-2 border border-slate-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500">
                            <option value="detect-vertical">Deteksi Tepi Vertikal</option>
                            <option value="detect-horizontal">Deteksi Tepi Horizontal</option>
                            <option value="sharpen">Sharpen</option>
                        </select>
                    </div>
                    
                    <div class="mb-6">
                        <label for="stride-input" class="block text-sm font-medium text-slate-700 mb-2">3. Atur Stride (Langkah Pergeseran)</label>
                        <select id="stride-input" class="w-full p-2 border border-slate-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500">
                            <option value="1">1</option>
                            <option value="2">2</option>
                        </select>
                    </div>

                    <button id="run-btn" class="w-full bg-blue-600 text-white font-bold py-3 px-4 rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-all duration-200">
                        Jalankan Simulasi
                    </button>
                    
                    <div class="mt-8 border-t pt-6">
                        <h3 class="text-xl font-bold mb-3">Ekspor Hasil</h3>
                        <div class="flex space-x-2">
                             <button id="print-btn" class="export-btn flex-1">Cetak</button>
                             <button id="pdf-btn" class="export-btn flex-1">PDF</button>
                             <button id="md-btn" class="export-btn flex-1">Markdown</button>
                        </div>
                    </div>
                </div>
            </aside>

            <!-- Kolom Hasil -->
            <main id="export-content" class="lg:col-span-8">
                <div id="results-container">
                     <div class="step-card text-center">
                        <h2 class="text-xl font-semibold text-slate-700">Selamat Datang!</h2>
                        <p class="mt-2 text-slate-500">Atur parameter di sebelah kiri dan klik "Jalankan Simulasi" untuk memulai.</p>
                        <p class="mt-2 text-slate-500">Aplikasi ini akan memvisualisasikan setiap langkah proses CNN, mulai dari konvolusi hingga klasifikasi, lengkap dengan perhitungan manualnya.</p>
                         <img src="https://storage.googleapis.com/gweb-uniblog-publish-prod/original_images/image5_h933q9h.png" alt="[Gambar alur kerja CNN]" class="mt-4 mx-auto rounded-lg shadow-md object-contain h-48">
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script>
        // Data (tidak berubah)
        const inputs = {
            'vertical-line': [[0,1,0,0,0],[0,1,0,0,0],[0,1,0,0,0],[0,1,0,0,0],[0,1,0,0,0]],
            'horizontal-line': [[0,0,0,0,0],[0,0,0,0,0],[1,1,1,1,1],[0,0,0,0,0],[0,0,0,0,0]],
            'cross': [[0,0,1,0,0],[0,0,1,0,0],[1,1,1,1,1],[0,0,1,0,0],[0,0,1,0,0]],
            'diagonal': [[1,0,0,0,0],[0,1,0,0,0],[0,0,1,0,0],[0,0,0,1,0],[0,0,0,0,1]]
        };
        const kernels = {
            'detect-vertical': [[1,0,-1],[1,0,-1],[1,0,-1]],
            'detect-horizontal': [[1,1,1],[0,0,0],[-1,-1,-1]],
            'sharpen': [[0,-1,0],[-1,5,-1],[0,-1,0]]
        };
        
        // Elemen DOM
        const runBtn = document.getElementById('run-btn');
        const resultsContainer = document.getElementById('results-container');
        const inputSelect = document.getElementById('input-select');
        const kernelSelect = document.getElementById('kernel-select');
        const strideInput = document.getElementById('stride-input');
        const printBtn = document.getElementById('print-btn');
        const pdfBtn = document.getElementById('pdf-btn');
        const mdBtn = document.getElementById('md-btn');

        // Fungsi utilitas
        const sleep = ms => new Promise(res => setTimeout(res, ms));

        function createGridHtml(matrix, type = '', title = '', id = '') {
            let html = `<div class="mb-2"><p class="font-medium text-center">${title}</p>`;
            html += `<div id="${id}" class="inline-grid grid-cols-${matrix[0].length} gap-1 p-2 bg-slate-100 rounded-lg">`;
            for (let i = 0; i < matrix.length; i++) {
                for (let j = 0; j < matrix[i].length; j++) {
                    const value = matrix[i][j];
                    let cellClass = 'grid-cell';
                    if (type === 'input' && value === 1) cellClass += ' active';
                    if (type === 'kernel') cellClass += ' kernel';
                    if (type === 'output') cellClass += ' output';
                    html += `<div class="${cellClass}" data-row="${i}" data-col="${j}">${value !== null ? value : ''}</div>`;
                }
            }
            html += `</div></div>`;
            return html;
        }
        
        // Event Listeners
        runBtn.addEventListener('click', runFullSimulation);
        printBtn.addEventListener('click', () => window.print());
        pdfBtn.addEventListener('click', generatePdf);
        mdBtn.addEventListener('click', generateMarkdown);

        async function runFullSimulation() {
            runBtn.disabled = true;
            runBtn.innerHTML = '<div class="animate-spin rounded-full h-5 w-5 border-b-2 border-white mx-auto"></div>';

            const selectedInputKey = inputSelect.value;
            const selectedKernelKey = kernelSelect.value;
            const stride = parseInt(strideInput.value);
            const inputMatrix = inputs[selectedInputKey];
            const kernelMatrix = kernels[selectedKernelKey];

            resultsContainer.innerHTML = ''; // Clear previous results

            // Langkah 1: Tampilkan Input & Kernel
            resultsContainer.innerHTML += `
                <div class="step-card">
                    <h3 class="text-xl font-bold mb-4">Langkah 1: Input & Kernel</h3>
                    <p class="mb-4 text-slate-600">Proses dimulai dengan gambar input (direpresentasikan sebagai matriks angka) dan sebuah kernel/filter. Kernel akan 'bergeser' di atas input untuk mendeteksi fitur tertentu.</p>
                    <div class="flex flex-wrap justify-center gap-8 items-center">
                        ${createGridHtml(inputMatrix, 'input', 'Input Image (5x5)', 'vis-input')}
                        <div class="text-2xl font-bold text-slate-400">+</div>
                        ${createGridHtml(kernelMatrix, 'kernel', 'Kernel/Filter (3x3)', 'vis-kernel')}
                    </div>
                </div>`;

            // Langkah 2: Animasi Konvolusi
            await animateConvolution(inputMatrix, kernelMatrix, stride);

            // Hitung hasil setelah konvolusi
            const convResult = applyConvolution(inputMatrix, kernelMatrix, stride);
            const reluResult = applyReLU(convResult.featureMap);
            const poolResult = applyMaxPooling(reluResult.activatedMap);
            const flattenedArray = poolResult.pooledMap.flat();
            const finalResult = fullyConnected(flattenedArray);
            
            // Tampilkan sisa langkah
            displayRemainingSteps(reluResult, poolResult, flattenedArray, finalResult);
            
            runBtn.disabled = false;
            runBtn.innerHTML = 'Jalankan Simulasi';
        }

        async function animateConvolution(input, kernel, stride) {
            const inputHeight = input.length;
            const inputWidth = input[0].length;
            const kernelHeight = kernel.length;
            const kernelWidth = kernel[0].length;
            
            const outputHeight = Math.floor((inputHeight - kernelHeight) / stride) + 1;
            const outputWidth = Math.floor((inputWidth - kernelWidth) / stride) + 1;
            
            const emptyOutput = Array(outputHeight).fill(0).map(() => Array(outputWidth).fill(null));

            // Setup HTML for animation
            const step2Container = document.createElement('div');
            step2Container.className = 'step-card';
            step2Container.innerHTML = `
                <h3 class="text-xl font-bold mb-4">Langkah 2: Operasi Konvolusi (Animasi)</h3>
                <p class="mb-4 text-slate-600">Kernel digeser di atas input. Di setiap posisi, dilakukan perkalian elemen-demi-elemen, lalu dijumlahkan untuk menghasilkan satu nilai di 'Feature Map'.</p>
                <div class="flex flex-wrap justify-center gap-8 items-center">
                    <div id="convolution-animation">
                        ${createGridHtml(emptyOutput, 'output', 'Feature Map', 'vis-output')}
                    </div>
                </div>
                <div id="conv-math" class="math-explanation">
                    <h4 class="font-bold mb-2">Perhitungan Manual:</h4>
                    <span class="text-slate-500">Animasi akan menampilkan perhitungan di sini...</span>
                </div>`;
            resultsContainer.appendChild(step2Container);

            const inputCells = document.querySelectorAll('#vis-input .grid-cell');
            const outputGrid = document.getElementById('vis-output');
            const mathDiv = document.getElementById('conv-math');

            for (let y = 0; y < outputHeight; y++) {
                for (let x = 0; x < outputWidth; x++) {
                    // Reset highlights
                    inputCells.forEach(c => c.classList.remove('highlight-input'));
                    
                    let sum = 0;
                    let calculationStr = '';
                    
                    for (let ky = 0; ky < kernelHeight; ky++) {
                        for (let kx = 0; kx < kernelWidth; kx++) {
                            const inputY = y * stride + ky;
                            const inputX = x * stride + kx;
                            
                            // Highlight input cell
                            const inputCell = document.querySelector(`#vis-input .grid-cell[data-row='${inputY}'][data-col='${inputX}']`);
                            inputCell.classList.add('highlight-input');

                            const inputValue = input[inputY][inputX];
                            const kernelValue = kernel[ky][kx];
                            sum += inputValue * kernelValue;
                             calculationStr += `(${inputValue}*${kernelValue}) + `;
                        }
                    }
                    calculationStr = calculationStr.slice(0, -3);

                    // Update math explanation
                    mathDiv.innerHTML = `<h4 class="font-bold mb-2">Perhitungan Manual:</h4><b>Posisi Output [${y},${x}]:</b><br>${calculationStr} = <b>${sum}</b>`;
                    
                    // Highlight output cell and update its value
                    const outputCell = outputGrid.querySelector(`.grid-cell[data-row='${y}'][data-col='${x}']`);
                    outputCell.classList.add('highlight-output');
                    await sleep(400); // Wait for highlight
                    outputCell.textContent = sum;
                    await sleep(600); // Wait for user to read
                    outputCell.classList.remove('highlight-output');
                }
            }
            // Final cleanup
             inputCells.forEach(c => c.classList.remove('highlight-input'));
             mathDiv.innerHTML = `<h4 class="font-bold mb-2">Perhitungan Manual:</h4>Animasi selesai. Ini adalah Feature Map final.`;
        }
        
        function displayRemainingSteps(reluResult, poolResult, flattenedArray, finalResult) {
            // LANGKAH 3: FUNGSI AKTIVASI (ReLU)
            resultsContainer.innerHTML += `
                <div class="step-card">
                    <h3 class="text-xl font-bold mb-4">Langkah 3: Fungsi Aktivasi (ReLU)</h3>
                    <p class="mb-4 text-slate-600">Feature Map kemudian dilewatkan ke fungsi aktivasi ReLU (Rectified Linear Unit). Fungsi ini mengubah semua nilai negatif menjadi nol.</p>
                    <div class="flex flex-wrap justify-center gap-8 items-center">
                         ${createGridHtml(reluResult.activatedMap, 'output', 'Activated Map (Post-ReLU)')}
                    </div>
                    <div class="math-explanation">
                        <h4 class="font-bold mb-2">Perhitungan Manual (ReLU(x) = max(0, x)):</h4>
                        ${reluResult.mathExplanation}
                    </div>
                </div>`;
             
            // LANGKAH 4: POOLING LAYER
            resultsContainer.innerHTML += `
                <div class="step-card">
                    <h3 class="text-xl font-bold mb-4">Langkah 4: Max Pooling</h3>
                    <p class="mb-4 text-slate-600">Pooling mengurangi dimensi feature map (downsampling). Kami mengambil nilai maksimum dari setiap area 2x2.</p>
                    <div class="flex flex-wrap justify-center gap-8 items-center">
                         ${createGridHtml(poolResult.pooledMap, 'output', 'Pooled Map')}
                    </div>
                     <div class="math-explanation">
                        <h4 class="font-bold mb-2">Perhitungan Manual (Max dari area 2x2):</h4>
                        ${poolResult.mathExplanation}
                    </div>
                </div>`;

            // LANGKAH 5: FLATTENING
            resultsContainer.innerHTML += `
                <div class="step-card">
                    <h3 class="text-xl font-bold mb-4">Langkah 5: Flattening</h3>
                    <p class="mb-4 text-slate-600">Matriks 2D hasil pooling diubah menjadi vektor 1D agar dapat dimasukkan ke dalam Fully Connected Layer.</p>
                    <div class="p-4 bg-slate-100 rounded-lg">
                        <p class="font-mono text-center text-lg">[${flattenedArray.join(', ')}]</p>
                    </div>
                </div>`;
            
            // LANGKAH 6: FULLY CONNECTED LAYER & KLASIFIKASI
            resultsContainer.innerHTML += `
                <div class="step-card">
                    <h3 class="text-xl font-bold mb-4">Langkah 6: Fully Connected Layer & Klasifikasi</h3>
                    <p class="mb-4 text-slate-600">Vektor yang sudah 'flat' dimasukkan ke Fully Connected Layer untuk melakukan klasifikasi akhir. Hasilnya adalah probabilitas untuk setiap kelas.</p>
                    <div class="math-explanation">
                        <h4 class="font-bold mb-2">Perhitungan Manual (Simulasi):</h4>
                        ${finalResult.mathExplanation}
                    </div>
                    <h4 class="font-bold mt-6 mb-2 text-center">Hasil Akhir Prediksi</h4>
                    <div class="bar-chart mt-4">
                        <div class="bar" style="height: ${finalResult.probabilities[0]*100}%">
                             <span class="font-bold">Vertikal</span><br>${(finalResult.probabilities[0]*100).toFixed(1)}%
                        </div>
                        <div class="bar bg-orange-500" style="height: ${finalResult.probabilities[1]*100}%">
                             <span class="font-bold">Horizontal</span><br>${(finalResult.probabilities[1]*100).toFixed(1)}%
                        </div>
                    </div>
                </div>`;
        }

        // --- Logika CNN (Fungsi tidak dianimasikan) ---
        function applyConvolution(input, kernel, stride) {
            const inputHeight = input.length, inputWidth = input[0].length;
            const kernelHeight = kernel.length, kernelWidth = kernel[0].length;
            const outputHeight = Math.floor((inputHeight - kernelHeight) / stride) + 1;
            const outputWidth = Math.floor((inputWidth - kernelWidth) / stride) + 1;
            const featureMap = Array(outputHeight).fill(0).map(() => Array(outputWidth).fill(0));
            
            for (let y = 0; y < outputHeight; y++) {
                for (let x = 0; x < outputWidth; x++) {
                    let sum = 0;
                    for (let ky = 0; ky < kernelHeight; ky++) {
                        for (let kx = 0; kx < kernelWidth; kx++) {
                            sum += input[y * stride + ky][x * stride + kx] * kernel[ky][kx];
                        }
                    }
                    featureMap[y][x] = sum;
                }
            }
            return { featureMap };
        }
        function applyReLU(featureMap) { /* ... sama seperti sebelumnya ... */ 
            let mathExplanation = '';
            const activatedMap = featureMap.map((row, i) => 
                row.map((value, j) => {
                    const result = Math.max(0, value);
                    mathExplanation += `ReLU(${value}) di [${i},${j}] -> max(0, ${value}) = <b>${result}</b><br>`;
                    return result;
                })
            );
            return { activatedMap, mathExplanation };
        }
        function applyMaxPooling(activatedMap) { /* ... sama seperti sebelumnya ... */ 
             const poolSize = 2, poolStride = 2;
            const outputHeight = Math.floor((activatedMap.length - poolSize) / poolStride) + 1;
            const outputWidth = activatedMap[0] ? Math.floor((activatedMap[0].length - poolSize) / poolStride) + 1 : 0;
            if (outputHeight <= 0 || outputWidth <= 0) {
                 return { pooledMap: [[0]], mathExplanation: "Ukuran feature map terlalu kecil untuk di-pool. Hasilnya adalah matriks 1x1 berisi 0." };
            }
            const pooledMap = Array(outputHeight).fill(0).map(() => Array(outputWidth).fill(0));
            let mathExplanation = '';
            for (let y = 0; y < outputHeight; y++) {
                for (let x = 0; x < outputWidth; x++) {
                    let maxVal = -Infinity, areaValues = [];
                    for (let py = 0; py < poolSize; py++) {
                        for (let px = 0; px < poolSize; px++) {
                             const currentVal = activatedMap[y * poolStride + py][x * poolStride + px];
                            areaValues.push(currentVal);
                            if (currentVal > maxVal) maxVal = currentVal;
                        }
                    }
                    pooledMap[y][x] = maxVal;
                    mathExplanation += `Max dari area [${areaValues.join(', ')}] di [${y},${x}] = <b>${maxVal}</b><br>`;
                }
            }
            return { pooledMap, mathExplanation };
        }
        function fullyConnected(flattened) { /* ... sama seperti sebelumnya ... */ 
            const weights = { 'vertical': [10, 5, -5, -10], 'horizontal': [10, -10, 5, -5] };
            const bias = { 'vertical': 1, 'horizontal': 1 };
            function calculateScore(input, w, b) {
                let score = b, explanation = `Skor = bias(${b})`;
                for (let i = 0; i < input.length; i++) {
                    const weight = w[i] || 0;
                    score += input[i] * weight;
                    explanation += ` + (input[${i}]*w[${i}])`;
                }
                explanation += `<br>= ${b}`;
                for (let i = 0; i < input.length; i++) {
                    const weight = w[i] || 0;
                    explanation += ` + (${input[i]}*${weight})`;
                }
                explanation += ` = <b>${score.toFixed(2)}</b>`;
                return { score, explanation };
            }
            const vRes = calculateScore(flattened, weights.vertical, bias.vertical);
            const hRes = calculateScore(flattened, weights.horizontal, bias.horizontal);
            let mathExplanation = `<b>Kelas 'Vertikal':</b><br>${vRes.explanation}<br><br><b>Kelas 'Horizontal':</b><br>${hRes.explanation}<br><br>`;
            const expV = Math.exp(vRes.score), expH = Math.exp(hRes.score), sumExp = expV + expH;
            const probV = expV / sumExp, probH = expH / sumExp;
            mathExplanation += `<b>Fungsi Softmax:</b><br>Prob(V) = e<sup>${vRes.score.toFixed(2)}</sup>/(e<sup>${vRes.score.toFixed(2)}</sup>+e<sup>${hRes.score.toFixed(2)}</sup>)=<b>${probV.toFixed(3)}</b><br>Prob(H) = e<sup>${hRes.score.toFixed(2)}</sup>/(e<sup>${vRes.score.toFixed(2)}</sup>+e<sup>${hRes.score.toFixed(2)}</sup>)=<b>${probH.toFixed(3)}</b>`;
            return { probabilities: [probV, probH], mathExplanation };
        }

        // --- Fungsi Ekspor ---
        async function generatePdf() {
            const { jsPDF } = window.jspdf;
            const content = document.getElementById('export-content');
            pdfBtn.innerText = 'Membuat...';
            
            const canvas = await html2canvas(content, { scale: 2 });
            const imgData = canvas.toDataURL('image/png');
            
            const pdf = new jsPDF('p', 'mm', 'a4');
            const pdfWidth = pdf.internal.pageSize.getWidth();
            const pdfHeight = pdf.internal.pageSize.getHeight();
            const canvasWidth = canvas.width;
            const canvasHeight = canvas.height;
            const ratio = canvasWidth / canvasHeight;
            const imgWidth = pdfWidth - 20; // with margin
            const imgHeight = imgWidth / ratio;
            
            let heightLeft = imgHeight;
            let position = 10;
            
            pdf.addImage(imgData, 'PNG', 10, position, imgWidth, imgHeight);
            heightLeft -= (pdfHeight - 20);

            while (heightLeft > 0) {
                position -= (pdfHeight - 20);
                pdf.addPage();
                pdf.addImage(imgData, 'PNG', 10, position, imgWidth, imgHeight);
                heightLeft -= (pdfHeight - 20);
            }
            
            pdf.save('simulasi-cnn.pdf');
            pdfBtn.innerText = 'PDF';
        }

        function generateMarkdown() {
            let md = '# Laporan Simulasi CNN\n\n';
            const stepCards = document.querySelectorAll('#results-container .step-card');
            
            stepCards.forEach(card => {
                const title = card.querySelector('h3').textContent;
                const description = card.querySelector('p').textContent;
                md += `## ${title}\n\n`;
                md += `${description}\n\n`;
                
                // Grids
                const grids = card.querySelectorAll('.inline-grid');
                grids.forEach(grid => {
                    const gridTitle = grid.parentElement.querySelector('p').textContent;
                    md += `**${gridTitle}**\n`;
                    md += '```\n';
                    let rows = [];
                    const cells = grid.querySelectorAll('.grid-cell');
                    const numCols = grid.className.match(/grid-cols-(\d+)/)[1];
                    let currentRow = '';
                    cells.forEach((cell, i) => {
                        currentRow += cell.textContent.padStart(4, ' ');
                        if ((i + 1) % numCols === 0) {
                            rows.push(currentRow);
                            currentRow = '';
                        }
                    });
                    md += rows.join('\n') + '\n';
                    md += '```\n\n';
                });

                // Math explanation
                const math = card.querySelector('.math-explanation');
                if (math) {
                    md += `**Perhitungan Manual**\n`;
                    md += '```\n';
                    md += math.innerText.replace(/<b>|<\/b>/g, '') + '\n';
                    md += '```\n\n';
                }

                 // Flattening
                const flat = card.querySelector('.font-mono');
                 if(flat) {
                     md += `**Vektor Hasil Flattening**\n\`${flat.textContent}\`\n\n`;
                 }

                // Bar chart
                const chart = card.querySelector('.bar-chart');
                if (chart) {
                    const bars = chart.querySelectorAll('.bar');
                    md += '**Hasil Akhir Prediksi**\n\n';
                    bars.forEach(bar => {
                        md += `- ${bar.innerText.replace('\n', ': ')}\n`;
                    });
                }

                md += '---\n\n';
            });
            
            // Download
            const blob = new Blob([md], { type: 'text/markdown' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'simulasi-cnn.md';
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
        }

        // Jalankan saat pertama kali dimuat
        document.addEventListener('DOMContentLoaded', runFullSimulation);
    </script>
</body>
</html>
