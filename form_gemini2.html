<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON><PERSON><PERSON> (SRQ-29, PHQ-9, SDQ)</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <!-- Libraries for PDF Export -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>
    <style>
        body {
            font-family: 'Inter', sans-serif;
        }
        .tab-content { display: none; }
        .tab-content.active { display: block; }
        .tab-btn.active { 
            border-color: #4f46e5; 
            background-color: #eef2ff;
            color: #4f46e5;
            font-weight: 600;
        }
        .question-group-title {
            border-bottom: 2px solid #e2e8f0;
            padding-bottom: 0.5rem;
            margin-top: 2rem;
            margin-bottom: 1rem;
            font-size: 1.25rem;
            font-weight: 600;
        }
        .result-card {
            display: none;
            opacity: 0;
            transition: opacity 0.5s ease-in-out;
        }
        .result-card.show {
            display: block;
            opacity: 1;
        }
        /* Print-specific styles */
        @media print {
            body * {
                visibility: hidden;
            }
            .printable-area, .printable-area * {
                visibility: visible;
            }
            .printable-area {
                position: absolute;
                left: 0;
                top: 0;
                width: 100%;
            }
            .no-print {
                display: none;
            }
        }
    </style>
</head>
<body class="bg-gray-50 text-gray-800">

    <div class="container mx-auto max-w-4xl p-4 sm:p-6 md:p-8">
        <div id="main-content" class="bg-white p-6 sm:p-8 rounded-2xl shadow-lg">
            <div class="text-center mb-6 no-print">
                <h1 class="text-3xl font-bold text-gray-900">Penilaian Mandiri Kesehatan Jiwa</h1>
                <p class="mt-2 text-gray-600">Termasuk SRQ-29, PHQ-9, dan SDQ</p>
            </div>

            <!-- Tabs Navigation -->
            <div class="border-b border-gray-200 mb-6 no-print">
                <nav class="-mb-px flex space-x-4" aria-label="Tabs">
                    <button class="tab-btn active" data-tab="srq29">SRQ-29</button>
                    <button class="tab-btn" data-tab="phq9">PHQ-9</button>
                    <button class="tab-btn" data-tab="sdq">SDQ</button>
                </nav>
            </div>
            
            <!-- SRQ-29 Content -->
            <div id="srq29" class="tab-content active">
                <div class="bg-blue-50 border-l-4 border-blue-500 text-blue-800 p-4 rounded-md my-6 no-print" role="alert">
                    <p class="font-bold">SRQ-29: Skrining Gangguan Mental</p>
                    <p>Jawab pertanyaan berdasarkan apa yang Anda rasakan selama **30 hari terakhir**.</p>
                </div>
                <form id="srqForm" class="no-print">
                    <h2 class="question-group-title text-gray-700">Kesejahteraan Umum & Kesehatan Emosional</h2>
                    <div id="srq-questions-1-20" class="space-y-4"></div>
                    <h2 class="question-group-title text-gray-700">Penggunaan Zat</h2>
                    <div id="srq-questions-21" class="space-y-4"></div>
                    <h2 class="question-group-title text-gray-700">Pengalaman Persepsi & Pikiran</h2>
                    <div id="srq-questions-22-24" class="space-y-4"></div>
                    <h2 class="question-group-title text-gray-700">Gejala Stres Pasca-Trauma</h2>
                    <div id="srq-questions-25-29" class="space-y-4"></div>
                    <div class="mt-8 text-center">
                        <button type="button" data-form="srq" class="calculateBtn bg-indigo-600 text-white font-bold py-3 px-8 rounded-lg">Tampilkan Hasil SRQ-29</button>
                    </div>
                </form>
                <div id="srq-results" class="result-card"></div>
            </div>

            <!-- PHQ-9 Content -->
            <div id="phq9" class="tab-content">
                <div class="bg-blue-50 border-l-4 border-blue-500 text-blue-800 p-4 rounded-md my-6 no-print" role="alert">
                    <p class="font-bold">PHQ-9: Kuesioner Kesehatan Pasien (Depresi)</p>
                    <p>Selama **2 minggu terakhir**, seberapa sering Anda terganggu oleh masalah-masalah berikut?</p>
                </div>
                 <form id="phqForm" class="no-print">
                    <div id="phq-questions" class="space-y-4"></div>
                     <div class="mt-8 text-center">
                        <button type="button" data-form="phq" class="calculateBtn bg-indigo-600 text-white font-bold py-3 px-8 rounded-lg">Tampilkan Hasil PHQ-9</button>
                    </div>
                </form>
                <div id="phq-results" class="result-card"></div>
            </div>
            
            <!-- SDQ Content -->
            <div id="sdq" class="tab-content">
                 <div class="bg-blue-50 border-l-4 border-blue-500 text-blue-800 p-4 rounded-md my-6 no-print" role="alert">
                    <p class="font-bold">SDQ: Kuesioner Kekuatan dan Kesulitan</p>
                    <p>Untuk setiap pernyataan, tandai jawaban yang paling sesuai dengan perasaan Anda selama **enam bulan terakhir**.</p>
                </div>
                 <form id="sdqForm" class="no-print">
                    <div id="sdq-questions" class="space-y-4"></div>
                     <div class="mt-8 text-center">
                        <button type="button" data-form="sdq" class="calculateBtn bg-indigo-600 text-white font-bold py-3 px-8 rounded-lg">Tampilkan Hasil SDQ</button>
                    </div>
                </form>
                <div id="sdq-results" class="result-card"></div>
            </div>

            <!-- Universal Export/Print Section -->
            <div id="export-buttons" class="mt-8 pt-6 border-t border-gray-200 text-center space-y-2 sm:space-y-0 sm:space-x-2 no-print" style="display: none;">
                <button id="printBtn" class="bg-gray-700 text-white font-semibold py-2 px-4 rounded-lg hover:bg-gray-800 transition-colors">Cetak Hasil</button>
                <button id="pdfBtn" class="bg-red-600 text-white font-semibold py-2 px-4 rounded-lg hover:bg-red-700 transition-colors">Ekspor ke PDF</button>
                <button id="excelBtn" class="bg-green-600 text-white font-semibold py-2 px-4 rounded-lg hover:bg-green-700 transition-colors">Ekspor ke Excel</button>
                <button id="csvBtn" class="bg-blue-600 text-white font-semibold py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors">Ekspor ke CSV</button>
            </div>
        </div>
    </div>

    <script>
        const { jsPDF } = window.jspdf;
        let activeTab = 'srq29';
        
        // --- DATA ---
        const srqQuestions = [
            { "id": 1, "text": "Apakah Anda sering menderita sakit kepala?" }, { "id": 2, "text": "Apakah Anda kehilangan nafsu makan?" }, { "id": 3, "text": "Apakah tidur Anda tidak lelap?" }, { "id": 4, "text": "Apakah Anda mudah menjadi takut?" }, { "id": 5, "text": "Apakah tangan Anda gemetar?" }, { "id": 6, "text": "Apakah Anda merasa cemas, tegang dan khawatir?" }, { "id": 7, "text": "Apakah Anda mengalami gangguan pencernaan?" }, { "id": 8, "text": "Apakah Anda merasa sulit berpikir jernih?" }, { "id": 9, "text": "Apakah Anda merasa tidak bahagia?" }, { "id": 10, "text": "Apakah Anda lebih sering menangis?" }, { "id": 11, "text": "Apakah Anda merasa sulit untuk menikmati aktivitas sehari-hari?" }, { "id": 12, "text": "Apakah Anda mengalami kesulitan untuk mengambil keputusan?" }, { "id": 13, "text": "Apakah aktivitas/tugas sehari-hari Anda terbengkalai?" }, { "id": 14, "text": "Apakah Anda merasa tidak mampu berperan dalam kehidupan ini?" }, { "id": 15, "text": "Apakah Anda kehilangan minat terhadap banyak hal?" }, { "id": 16, "text": "Apakah Anda merasa tidak berharga?" }, { "id": 17, "text": "Apakah Anda mempunyai pikiran untuk mengakhiri hidup Anda?" }, { "id": 18, "text": "Apakah Anda merasa lelah sepanjang waktu?" }, { "id": 19, "text": "Apakah Anda merasa tidak enak di perut?" }, { "id": 20, "text": "Apakah Anda mudah lelah?" },
            { "id": 21, "text": "Apakah Anda minum alkohol lebih banyak dari biasanya atau apakah Anda menggunakan narkoba?" },
            { "id": 22, "text": "Apakah Anda yakin bahwa seseorang mencoba mencelakai Anda dengan cara tertentu?" }, { "id": 23, "text": "Apakah ada yang mengganggu atau hal yang tidak biasa dalam pikiran Anda?" }, { "id": 24, "text": "Apakah Anda pernah mendengar suara tanpa tahu sumbernya atau yang orang lain tidak dapat mendengar?" },
            { "id": 25, "text": "Apakah Anda mengalami mimpi yang mengganggu tentang suatu bencana/musibah?" }, { "id": 26, "text": "Apakah Anda menghindari kegiatan, tempat, atau pikiran yang mengingatkan Anda akan bencana tersebut?" }, { "id": 27, "text": "Apakah minat Anda terhadap teman dan kegiatan yang biasa Anda lakukan berkurang?" }, { "id": 28, "text": "Apakah Anda merasa sangat terganggu jika berada dalam situasi yang mengingatkan Anda akan bencana?" }, { "id": 29, "text": "Apakah Anda kesulitan memahami atau mengekspresikan perasaan Anda?" },
        ];
        const phqQuestions = [
            {"id": 1, "text": "Kurang berminat atau bergairah dalam melakukan apapun"}, {"id": 2, "text": "Merasa murung, sedih, atau putus asa"}, {"id": 3, "text": "Sulit tidur/mudah terbangun, atau terlalu banyak tidur"}, {"id": 4, "text": "Merasa lelah atau kurang bertenaga"}, {"id": 5, "text": "Nafsu makan berkurang atau berlebihan"}, {"id": 6, "text": "Merasa buruk tentang diri sendiri - atau merasa bahwa Anda adalah orang yang gagal atau telah mengecewakan diri sendiri atau keluarga"}, {"id": 7, "text": "Sulit berkonsentrasi pada sesuatu, misalnya membaca koran atau menonton televisi"}, {"id": 8, "text": "Bergerak atau berbicara sangat lambat sehingga orang lain memperhatikannya. Atau sebaliknya - merasa resah atau gelisah sehingga Anda lebih sering bergerak dari biasanya"}, {"id": 9, "text": "Pikiran bahwa Anda lebih baik mati atau ingin melukai diri sendiri dengan cara apapun"},
        ];
        const sdqQuestions = [
            {id:1, text:"Saya mencoba bersikap baik kepada orang lain. Saya peduli dengan perasaan mereka.", scale:"prosocial", reverse:false},
            {id:2, text:"Saya gelisah, saya tidak bisa diam untuk waktu yang lama.", scale:"hyperactivity", reverse:false},
            {id:3, text:"Saya sering sakit kepala, sakit perut atau jenis sakit lainnya.", scale:"emotional", reverse:false},
            {id:4, text:"Saya biasanya berbagi dengan orang lain (misalnya makanan, permainan, pulpen).", scale:"prosocial", reverse:true},
            {id:5, text:"Saya menjadi sangat marah dan sering kehilangan kendali.", scale:"conduct", reverse:false},
            {id:6, text:"Saya lebih suka sendirian daripada bersama orang-orang seusia saya.", scale:"peer", reverse:false},
            {id:7, text:"Saya biasanya melakukan apa yang dikatakan orang lain.", scale:"conduct", reverse:true},
            {id:8, text:"Saya banyak khawatir.", scale:"emotional", reverse:false},
            {id:9, text:"Saya selalu siap membantu jika seseorang terluka, kesal atau merasa sakit.", scale:"prosocial", reverse:true},
            {id:10, text:"Saya terus-menerus gelisah atau bergerak-gerak.", scale:"hyperactivity", reverse:false},
            {id:11, text:"Saya punya satu teman baik atau lebih.", scale:"peer", reverse:true},
            {id:12, text:"Saya sering berkelahi. Saya bisa membuat orang lain melakukan apa yang saya inginkan.", scale:"conduct", reverse:false},
            {id:13, text:"Saya sering merasa tidak bahagia, sedih atau menangis.", scale:"emotional", reverse:false},
            {id:14, text:"Secara umum, saya disukai oleh orang-orang seusia saya.", scale:"peer", reverse:true},
            {id:15, text:"Saya mudah teralihkan, saya merasa sulit untuk berkonsentrasi.", scale:"hyperactivity", reverse:false},
            {id:16, text:"Saya gugup dalam situasi baru, saya mudah kehilangan kepercayaan diri.", scale:"emotional", reverse:false},
            {id:17, text:"Saya baik kepada anak-anak yang lebih kecil.", scale:"prosocial", reverse:true},
            {id:18, text:"Saya sering dituduh berbohong atau berbuat curang.", scale:"conduct", reverse:false},
            {id:19, text:"Orang lain seusia saya mengganggu atau mengucilkan saya.", scale:"peer", reverse:false},
            {id:20, text:"Saya sering menawarkan diri untuk membantu orang lain (orang tua, guru, anak-anak).", scale:"prosocial", reverse:true},
            {id:21, text:"Saya menyelesaikan pekerjaan yang saya mulai.", scale:"hyperactivity", reverse:true},
            {id:22, text:"Saya cenderung mengambil barang-barang yang bukan milik saya (dari rumah, sekolah atau tempat lain).", scale:"conduct", reverse:false},
            {id:23, text:"Saya lebih mudah bergaul dengan orang dewasa daripada dengan orang-orang seusia saya.", scale:"peer", reverse:false},
            {id:24, text:"Saya banyak merasa takut, saya mudah takut.", scale:"emotional", reverse:false},
            {id:25, text:"Saya berpikir sebelum melakukan sesuatu.", scale:"hyperactivity", reverse:true},
        ];

        // --- UI Initialization ---
        function init() {
            // SRQ-29
            const q1_20 = document.getElementById('srq-questions-1-20');
            const q21 = document.getElementById('srq-questions-21');
            const q22_24 = document.getElementById('srq-questions-22-24');
            const q25_29 = document.getElementById('srq-questions-25-29');
            srqQuestions.forEach(q => {
                let html = `<div class="bg-gray-100 p-4 rounded-lg flex items-center justify-between"><label for="srq_q${q.id}" class="flex-1 text-gray-700">${q.id}. ${q.text}</label><div class="flex space-x-2"><input type="radio" id="srq_q${q.id}_yes" name="srq_q${q.id}" value="1" class="hidden peer/yes"><label for="srq_q${q.id}_yes" class="cursor-pointer px-4 py-2 rounded-md text-sm font-medium border border-gray-300 peer-checked/yes:bg-red-500 peer-checked/yes:text-white">Ya</label><input type="radio" id="srq_q${q.id}_no" name="srq_q${q.id}" value="0" class="hidden peer/no" checked><label for="srq_q${q.id}_no" class="cursor-pointer px-4 py-2 rounded-md text-sm font-medium border border-gray-300 peer-checked/no:bg-green-500 peer-checked/no:text-white">Tidak</label></div></div>`;
                if(q.id <= 20) q1_20.innerHTML += html;
                else if(q.id === 21) q21.innerHTML += html;
                else if(q.id <= 24) q22_24.innerHTML += html;
                else q25_29.innerHTML += html;
            });
            
            // PHQ-9
            const phqContainer = document.getElementById('phq-questions');
            const phqOptions = ['Tidak sama sekali (0)', 'Beberapa hari (1)', 'Lebih dari separuh hari (2)', 'Hampir setiap hari (3)'];
            phqQuestions.forEach(q => {
                let optionsHtml = phqOptions.map((opt, i) => `<div class="flex items-center"><input type="radio" id="phq_q${q.id}_${i}" name="phq_q${q.id}" value="${i}" class="h-4 w-4 text-indigo-600 border-gray-300 focus:ring-indigo-500"><label for="phq_q${q.id}_${i}" class="ml-3 block text-sm text-gray-700">${opt}</label></div>`).join('');
                phqContainer.innerHTML += `<div class="bg-gray-100 p-4 rounded-lg"><p class="font-medium text-gray-800">${q.id}. ${q.text}</p><div class="mt-2 space-y-2">${optionsHtml}</div></div>`;
            });

            // SDQ
            const sdqContainer = document.getElementById('sdq-questions');
            const sdqOptions = ['Tidak Benar (0)', 'Agak Benar (1)', 'Pasti Benar (2)'];
             sdqQuestions.forEach(q => {
                let optionsHtml = sdqOptions.map((opt, i) => `<div class="flex items-center"><input type="radio" id="sdq_q${q.id}_${i}" name="sdq_q${q.id}" value="${i}" class="h-4 w-4 text-indigo-600 border-gray-300 focus:ring-indigo-500"><label for="sdq_q${q.id}_${i}" class="ml-3 block text-sm text-gray-700">${opt}</label></div>`).join('');
                sdqContainer.innerHTML += `<div class="bg-gray-100 p-4 rounded-lg"><p class="font-medium text-gray-800">${q.id}. ${q.text}</p><div class="mt-2 space-y-2">${optionsHtml}</div></div>`;
            });
        }

        // --- Calculation & Display ---
        function calculateAndShow(formType) {
            let resultsHtml = '';
            
            if (formType === 'srq') {
                const form = document.getElementById('srqForm');
                const formData = new FormData(form);
                let scores = { neurotic: 0, substance: 0, psychotic: 0, ptsd: 0 };
                for (let i = 1; i <= 29; i++) {
                    const answer = parseInt(formData.get(`srq_q${i}`), 10) || 0;
                    if (i <= 20) scores.neurotic += answer;
                    else if (i === 21) scores.substance += answer;
                    else if (i <= 24) scores.psychotic += answer;
                    else scores.ptsd += answer;
                }
                // Build SRQ results HTML...
                 resultsHtml = `<div class="printable-area p-2"><h2 class="text-2xl font-bold text-center mb-6 text-gray-900">Hasil Penilaian SRQ-29</h2>`;
                 resultsHtml += (scores.neurotic >= 5) ? `<div class="p-4 rounded-lg bg-red-100"><h3 class="font-bold text-red-800">Gejala Emosional & Kecemasan</h3><p>Skor Anda ${scores.neurotic}/20 menunjukkan kemungkinan gejala kecemasan/depresi.</p></div>` : `<div class="p-4 rounded-lg bg-green-100"><h3 class="font-bold text-green-800">Gejala Emosional & Kecemasan</h3><p>Skor Anda ${scores.neurotic}/20 dalam rentang tipikal.</p></div>`;
                 resultsHtml += `<div class="p-4 mt-2 rounded-lg ${scores.substance >= 1 ? 'bg-red-100' : 'bg-green-100'}"><h3 class="font-bold">Penggunaan Zat</h3><p>${scores.substance >= 1 ? 'Terindikasi peningkatan penggunaan.' : 'Tidak ada indikasi peningkatan.'}</p></div>`;
                 resultsHtml += `<div class="p-4 mt-2 rounded-lg ${scores.psychotic >= 1 ? 'bg-red-100' : 'bg-green-100'}"><h3 class="font-bold">Pengalaman Persepsi & Pikiran</h3><p>${scores.psychotic >= 1 ? 'Terindikasi adanya gejala. Sangat disarankan konsultasi.' : 'Tidak ada indikasi gejala.'}</p></div>`;
                 resultsHtml += `<div class="p-4 mt-2 rounded-lg ${scores.ptsd >= 1 ? 'bg-red-100' : 'bg-green-100'}"><h3 class="font-bold">Gejala Stres Pasca-Trauma</h3><p>${scores.ptsd >= 1 ? 'Terindikasi adanya gejala.' : 'Tidak ada indikasi gejala.'}</p></div>`;
                 resultsHtml += `</div>`;
                 document.getElementById('srq-results').innerHTML = resultsHtml;
                 document.getElementById('srq-results').classList.add('show');
                 document.getElementById('srq-results').scrollIntoView({ behavior: 'smooth' });

            } else if (formType === 'phq') {
                const form = document.getElementById('phqForm');
                let totalScore = 0;
                for (let i = 1; i <= 9; i++) {
                    totalScore += parseInt(document.querySelector(`input[name="phq_q${i}"]:checked`)?.value || '0', 10);
                }
                let interpretation = '';
                if(totalScore <= 4) interpretation = 'Minimal atau tanpa depresi.';
                else if (totalScore <= 9) interpretation = 'Depresi ringan.';
                else if (totalScore <= 14) interpretation = 'Depresi sedang.';
                else if (totalScore <= 19) interpretation = 'Depresi sedang hingga berat.';
                else interpretation = 'Depresi berat.';
                
                resultsHtml = `<div class="printable-area p-2"><h2 class="text-2xl font-bold text-center mb-6 text-gray-900">Hasil Penilaian PHQ-9</h2><div class="p-5 rounded-lg bg-blue-100 text-center"><p class="text-lg">Total Skor Anda:</p><p class="text-4xl font-bold my-2">${totalScore}</p><p class="text-lg font-semibold">${interpretation}</p></div></div>`;
                document.getElementById('phq-results').innerHTML = resultsHtml;
                document.getElementById('phq-results').classList.add('show');
                document.getElementById('phq-results').scrollIntoView({ behavior: 'smooth' });

            } else if (formType === 'sdq') {
                let scores = {emotional: 0, conduct: 0, hyperactivity: 0, peer: 0, prosocial: 0};
                sdqQuestions.forEach(q => {
                    let val = parseInt(document.querySelector(`input[name="sdq_q${q.id}"]:checked`)?.value || '0', 10);
                    if (q.reverse) {
                        if (val === 0) val = 2; else if (val === 2) val = 0;
                    }
                    scores[q.scale] += val;
                });
                
                const getInterp = (score, scale) => {
                    const thresholds = {
                        emotional: [0, 5, 6, 10], conduct: [0, 3, 4, 10], hyperactivity: [0, 5, 6, 10], peer: [0, 3, 4, 10],
                        prosocial: [0, 5, 6, 10] // For prosocial, higher is better
                    };
                    const t = thresholds[scale];
                    if (scale === 'prosocial') {
                        if (score <= t[1]) return {text: 'Perlu perhatian', class: 'bg-red-100'};
                        if (score <= t[2]) return {text: 'Batas', class: 'bg-yellow-100'};
                        return {text: 'Normal', class: 'bg-green-100'};
                    } else {
                        if (score <= t[1]) return {text: 'Normal', class: 'bg-green-100'};
                        if (score <= t[2]) return {text: 'Batas', class: 'bg-yellow-100'};
                        return {text: 'Perlu perhatian', class: 'bg-red-100'};
                    }
                };

                resultsHtml = `<div class="printable-area p-2"><h2 class="text-2xl font-bold text-center mb-6 text-gray-900">Hasil Penilaian SDQ</h2><div class="grid grid-cols-1 md:grid-cols-2 gap-4">`;
                const scaleNames = {"emotional": "Gejala Emosional", "conduct": "Masalah Perilaku", "hyperactivity": "Hiperaktivitas", "peer": "Masalah Teman Sebaya", "prosocial": "Perilaku Prososial"};
                for(const scale in scores) {
                    const interp = getInterp(scores[scale], scale);
                    resultsHtml += `<div class="p-4 rounded-lg ${interp.class}"><h3 class="font-bold">${scaleNames[scale]}</h3><p>Skor: ${scores[scale]}, Interpretasi: ${interp.text}</p></div>`;
                }
                resultsHtml += `</div></div>`;
                document.getElementById('sdq-results').innerHTML = resultsHtml;
                document.getElementById('sdq-results').classList.add('show');
                document.getElementById('sdq-results').scrollIntoView({ behavior: 'smooth' });
            }

            document.getElementById('export-buttons').style.display = 'block';
        }

        // --- Event Listeners ---
        // Tab switching
        document.querySelectorAll('.tab-btn').forEach(button => {
            button.addEventListener('click', () => {
                const tabId = button.dataset.tab;
                activeTab = tabId;
                document.querySelectorAll('.tab-btn').forEach(btn => btn.classList.remove('active'));
                button.classList.add('active');
                document.querySelectorAll('.tab-content').forEach(content => content.classList.remove('active'));
                document.getElementById(tabId).classList.add('active');
                // Hide results and export buttons when switching tabs
                document.querySelectorAll('.result-card').forEach(rc => { rc.classList.remove('show'); rc.innerHTML = ''; });
                document.getElementById('export-buttons').style.display = 'none';
            });
            button.style.border = '2px solid transparent';
            button.style.padding = '0.5rem 1rem';
            button.style.borderRadius = '0.5rem';
            button.style.transition = 'all 0.2s';
        });

        // Calculate buttons
        document.querySelectorAll('.calculateBtn').forEach(button => {
            button.addEventListener('click', (e) => {
                calculateAndShow(e.target.dataset.form);
            });
        });

        // Export/Print buttons
        document.getElementById('printBtn').addEventListener('click', () => window.print());
        document.getElementById('pdfBtn').addEventListener('click', () => {
            const content = document.querySelector(`#${activeTab} .printable-area`);
            if (!content) return;
            html2canvas(content, { scale: 2 }).then(canvas => {
                const imgData = canvas.toDataURL('image/png');
                const pdf = new jsPDF('p', 'mm', 'a4');
                const pdfWidth = pdf.internal.pageSize.getWidth();
                const pdfHeight = (canvas.height * pdfWidth) / canvas.width;
                pdf.addImage(imgData, 'PNG', 0, 0, pdfWidth, pdfHeight);
                pdf.save(`Hasil_Penilaian_${activeTab.toUpperCase()}.pdf`);
            });
        });
        
        function getExportData() {
            let data = "";
            let form, questionsList;
            if(activeTab === 'srq29') {
                form = document.getElementById('srqForm');
                questionsList = srqQuestions;
                data = "No,Pertanyaan,Jawaban\n";
                 questionsList.forEach(q => {
                    const answerValue = new FormData(form).get(`srq_q${q.id}`);
                    data += `${q.id},"${q.text.replace(/"/g, '""')}",${answerValue === '1' ? 'Ya' : 'Tidak'}\n`;
                });
            } else if (activeTab === 'phq9') {
                form = document.getElementById('phqForm');
                questionsList = phqQuestions;
                data = "No,Pertanyaan,Jawaban (Skor)\n";
                questionsList.forEach(q => {
                    const answerValue = document.querySelector(`input[name="phq_q${q.id}"]:checked`)?.value || 'N/A';
                    data += `${q.id},"${q.text.replace(/"/g, '""')}",${answerValue}\n`;
                });
            } else if (activeTab === 'sdq') {
                form = document.getElementById('sdqForm');
                questionsList = sdqQuestions;
                data = "No,Pertanyaan,Jawaban (Skor)\n";
                 questionsList.forEach(q => {
                    const answerValue = document.querySelector(`input[name="sdq_q${q.id}"]:checked`)?.value || 'N/A';
                    data += `${q.id},"${q.text.replace(/"/g, '""')}",${answerValue}\n`;
                });
            }
            return data;
        }

        const downloadFile = (data, filename, type) => {
             const blob = new Blob([data], { type: `${type};charset=utf-8;` });
             const link = document.createElement('a');
             link.href = URL.createObjectURL(blob);
             link.download = filename;
             link.click();
             URL.revokeObjectURL(link.href);
        };

        document.getElementById('excelBtn').addEventListener('click', () => {
            const data = getExportData();
            downloadFile(data, `Hasil_${activeTab.toUpperCase()}.xls`, 'application/vnd.ms-excel');
        });

        document.getElementById('csvBtn').addEventListener('click', () => {
             const data = getExportData();
             downloadFile(data, `Hasil_${activeTab.toUpperCase()}.csv`, 'text/csv');
        });

        // Initialize the forms
        init();
    </script>
</body>
</html>
