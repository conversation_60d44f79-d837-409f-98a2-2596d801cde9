<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Ases<PERSON></title>
    <!-- Tailwind CSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- Google Fonts - Inter -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Inter', sans-serif;
            background-color: #f1f5f9; /* Slate 100 */
            color: #334155; /* Slate 700 */
        }
        /* Custom styles for print and PDF */
        @media print {
            body {
                background-color: #fff;
            }
            #app-container {
                box-shadow: none;
                margin: 0;
                border-radius: 0;
                padding: 0;
                width: 100%;
                max-width: none;
            }
            .no-print {
                display: none !important;
            }
            .print-only {
                display: block !important;
            }
            h1, h2, h3, h4 {
                color: #1e3a8a !important; /* Dark blue for headings */
            }
            table {
                width: 100%;
                border-collapse: collapse;
                margin-top: 10px;
            }
            th, td {
                border: 1px solid #ccc;
                padding: 8px;
                text-align: left;
            }
            th {
                background-color: #f1f5f9;
            }
        }
        .print-only {
            display: none; /* Hidden by default, only shown in print */
        }
    </style>
</head>
<body class="min-h-screen flex items-center justify-center p-4">
    <div id="app-container" class="bg-white rounded-lg shadow-xl p-6 md:p-8 w-full max-w-4xl border border-gray-200">
        <h1 class="text-3xl md:text-4xl font-bold text-center text-blue-800 mb-6">Asesmen Mandiri Kesehatan Jiwa</h1>
        <p class="text-center text-gray-600 mb-8">Lengkapi formulir ini untuk mendapatkan gambaran awal mengenai kesehatan mental Anda. <br class="md:hidden">Hasil asesmen ini bukan diagnosis profesional.</p>

        <!-- Custom Alert/Message Box -->
        <div id="custom-alert" class="hidden fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center z-50">
            <div class="bg-white p-6 rounded-lg shadow-xl max-w-sm w-full text-center">
                <p id="alert-message" class="text-lg font-medium text-gray-800 mb-4"></p>
                <button onclick="hideAlert()" class="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors duration-200">Oke</button>
            </div>
        </div>

        <!-- Navigation Buttons -->
        <div id="navigation-buttons" class="no-print grid grid-cols-2 md:grid-cols-4 gap-4 mb-8">
            <button onclick="goToTest('PHQ9')" class="bg-blue-500 hover:bg-blue-600 text-white font-semibold py-3 px-4 rounded-lg shadow-md transition duration-200">PHQ-9</button>
            <button onclick="goToTest('BDI')" class="bg-blue-500 hover:bg-blue-600 text-white font-semibold py-3 px-4 rounded-lg shadow-md transition duration-200">BDI</button>
            <button onclick="goToTest('Y_BOCS')" class="bg-blue-500 hover:bg-blue-600 text-white font-semibold py-3 px-4 rounded-lg shadow-md transition duration-200">Y-BOCS</button>
            <button onclick="goToTest('STEPI')" class="bg-blue-500 hover:bg-blue-600 text-white font-semibold py-3 px-4 rounded-lg shadow-md transition duration-200">STEPI</button>
        </div>

        <!-- Assessment Sections -->
        <div id="assessment-sections">
            <!-- Each assessment section will be dynamically added/hidden here -->
        </div>

        <!-- Results Summary Section -->
        <div id="results-summary" class="hidden p-6 bg-blue-50 rounded-lg border border-blue-200 relative mb-8">
            <h2 class="text-2xl font-semibold text-blue-700 mb-4">Ringkasan Hasil Asesmen</h2>
            <p class="text-gray-700 mb-4">Berikut adalah ringkasan skor Anda untuk setiap asesmen. Untuk interpretasi lengkap, silakan merujuk pada bagian detail di bawah ini atau cetak/ekspor laporan.</p>

            <div id="summary-content" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                <!-- Summary cards will be populated here by JavaScript -->
            </div>

            <div class="disclaimer mt-8 p-4 bg-yellow-100 border border-yellow-300 text-yellow-800 rounded-md text-sm italic">
                <p><strong>Penting:</strong> Asesmen ini bukan diagnosis. Selalu konsultasikan dengan profesional kesehatan jiwa (psikolog, psikiater) untuk diagnosis dan perawatan yang akurat. Hasil ini dirancang sebagai alat skrining awal dan bukan pengganti evaluasi klinis yang komprehensif.</p>
            </div>

            <div class="no-print mt-6 flex flex-col md:flex-row justify-center gap-4">
                <button onclick="handlePrint()" class="bg-green-600 hover:bg-green-700 text-white font-semibold py-3 px-6 rounded-md shadow-lg transition duration-200 flex items-center justify-center">
                    <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20"><path d="M5 4H15C15.5523 4 16 4.44772 16 5V15C16 15.5523 15.5523 16 15 16H5C4.44772 16 4 15.5523 4 15V5C4 4.44772 4.44772 4 5 4ZM5 2C3.34315 2 2 3.34315 2 5V15C2 16.6569 3.34315 18 5 18H15C16.6569 18 18 16.6569 18 15V5C18 3.34315 16.6569 2 15 2H5ZM8 10H12V14H8V10Z" clip-rule="evenodd" fill-rule="evenodd"></path></svg>
                    Cetak Hasil
                </button>
                <button onclick="handleExportXLSX()" class="bg-purple-600 hover:bg-purple-700 text-white font-semibold py-3 px-6 rounded-md shadow-lg transition duration-200 flex items-center justify-center">
                    <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20"><path d="M11 3a1 1 0 100 2h3a1 1 0 100-2h-3zM11 7a1 1 0 100 2h3a1 1 0 100-2h-3zM11 11a1 1 0 100 2h3a1 1 0 100-2h-3zM9 1a1 1 0 011 1v1h1a1 1 0 110 2h-1v1a1 1 0 11-2 0v-1H7a1 1 0 110-2h1V2a1 1 0 011-1z" clip-rule="evenodd" fill-rule="evenodd"></path></svg>
                    Ekspor XLSX
                </button>
                <button onclick="handleExportPDF()" class="bg-red-600 hover:bg-red-700 text-white font-semibold py-3 px-6 rounded-md shadow-lg transition duration-200 flex items-center justify-center">
                    <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20"><path d="M9 2a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2V4a2 2 0 00-2-2H9zM7 4a1 1 0 011-1h4a1 1 0 011 1v6a1 1 0 01-1 1H8a1 1 0 01-1-1V4z"></path><path d="M4 11H3a1 1 0 00-1 1v4a2 2 0 002 2h10a2 2 0 002-2v-4a1 1 0 00-1-1h-1v2H4v-2z"></path></svg>
                    Ekspor PDF
                </button>
            </div>
            <button onclick="resetAssessment()" class="no-print mt-4 w-full bg-gray-500 hover:bg-gray-600 text-white font-semibold py-3 px-6 rounded-md shadow-md transition duration-200">Mulai Ulang Asesmen</button>
        </div>

        <!-- Footer -->
        <p class="no-print text-center text-gray-500 text-xs mt-8">Asesmen ini bukan diagnosis. Selalu konsultasikan dengan profesional kesehatan jiwa.</p>
    </div>

    <!-- CDN for XLSX library -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
    <!-- CDN for jsPDF and html2canvas -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>

    <script>
        // Global variables to store answers and scores
        let answers = {};
        let scores = {};
        let activeTest = null; // Stores the currently active test ID

        // Function to show custom alert message
        function showAlert(message) {
            document.getElementById('alert-message').innerText = message;
            document.getElementById('custom-alert').classList.remove('hidden');
        }

        // Function to hide custom alert message
        function hideAlert() {
            document.getElementById('custom-alert').classList.add('hidden');
        }

        // Define the structure and questions for each assessment test
        const tests = [
            {
                id: 'PHQ9',
                name: 'PHQ-9 (Patient Health Questionnaire-9)',
                description: 'Instrumen skrining depresi yang ringkas untuk mengukur tingkat keparahan depresi selama 2 minggu terakhir.',
                questions: [
                    { id: 'q1', text: 'Sedikit minat atau kesenangan dalam melakukan sesuatu.' },
                    { id: 'q2', text: 'Merasa sedih, murung, atau putus asa.' },
                    { id: 'q3', text: 'Sulit tidur atau tidur terlalu banyak.' },
                    { id: 'q4', text: 'Merasa lelah atau kurang energi.' },
                    { id: 'q5', text: 'Nafsu makan buruk atau makan terlalu banyak.' },
                    { id: 'q6', text: 'Merasa buruk tentang diri sendiri — atau merasa bahwa Anda adalah kegagalan atau telah mengecewakan diri sendiri atau keluarga Anda.' },
                    { id: 'q7', text: 'Sulit berkonsentrasi pada hal-hal, seperti membaca koran atau menonton TV.' },
                    { id: 'q8', text: 'Bergerak atau berbicara begitu lambat sehingga orang lain mungkin memperhatikannya. Atau sebaliknya — sangat gelisah atau resah sehingga Anda bergerak jauh lebih dari biasanya.' },
                    { id: 'q9', text: 'Pikiran bahwa Anda lebih baik mati atau melukai diri sendiri dengan cara tertentu.' },
                ],
                options: [
                    { value: 0, text: 'Tidak sama sekali' },
                    { value: 1, text: 'Beberapa hari' },
                    { value: 2, text: 'Lebih dari separuh hari' },
                    { value: 3, text: 'Hampir setiap hari' },
                ],
                // Function to calculate PHQ-9 score and interpretation
                calculateScore: (testAnswers) => {
                    const score = Object.values(testAnswers).reduce((sum, val) => sum + (val || 0), 0);
                    let interpretation = '';
                    if (score >= 0 && score <= 4) interpretation = 'Depresi Minimal';
                    else if (score >= 5 && score <= 9) interpretation = 'Depresi Ringan';
                    else if (score >= 10 && score <= 14) interpretation = 'Depresi Sedang';
                    else if (score >= 15 && score <= 19) interpretation = 'Depresi Sedang-Berat';
                    else if (score >= 20 && score <= 27) interpretation = 'Depresi Berat';
                    return { score, interpretation };
                }
            },
            {
                id: 'BDI',
                name: 'Beck Depression Inventory (BDI)',
                description: 'Instrumen 21 item untuk mengukur keparahan gejala depresi.',
                questions: [
                    { id: 'q1', text: 'Kesedihan:' },
                    { id: 'q2', text: 'Pesimisme:' },
                    { id: 'q3', text: 'Perasaan Kegagalan:' },
                    { id: 'q4', text: 'Kehilangan Kesenangan:' },
                    { id: 'q5', text: 'Perasaan Bersalah:' },
                    { id: 'q6', text: 'Perasaan Hukuman:' },
                    { id: 'q7', text: 'Ketidakpuasan Diri:' },
                    { id: 'q8', text: 'Kritik Diri:' },
                    { id: 'q9', text: 'Pikiran atau Keinginan Bunuh Diri:' },
                    { id: 'q10', text: 'Menangis:' },
                    { id: 'q11', text: 'Agitasi:' },
                    { id: 'q12', text: 'Kehilangan Minat:' },
                    { id: 'q13', text: 'Ketidaktegasan:' },
                    { id: 'q14', text: 'Hilangnya Energi:' },
                    { id: 'q15', text: 'Perubahan Pola Tidur:' },
                    { id: 'q16', text: 'Iritabilitas:' },
                    { id: 'q17', text: 'Perubahan Nafsu Makan:' },
                    { id: 'q18', text: 'Sulit Konsentrasi:' },
                    { id: 'q19', text: 'Kelelahan atau Kelemahan:' },
                    { id: 'q20', text: 'Kehilangan Minat pada Seks:' },
                    { id: 'q21', text: 'Kesehatan yang Mengkhawatirkan:' }
                ],
                // Options for BDI, each question has 4 options with increasing severity (0-3 points)
                options: [ // These options are general for BDI; in a full BDI, each question has specific options. Here, we simplify.
                    { value: 0, text: 'Saya tidak merasakan ini.' },
                    { value: 1, text: 'Saya sedikit merasakan ini.' },
                    { value: 2, text: 'Saya merasakan ini secara sedang.' },
                    { value: 3, text: 'Saya sangat merasakan ini.' }
                ],
                // Function to calculate BDI score and interpretation
                calculateScore: (testAnswers) => {
                    const score = Object.values(testAnswers).reduce((sum, val) => sum + (val || 0), 0);
                    let interpretation = '';
                    if (score >= 0 && score <= 13) interpretation = 'Depresi Minimal';
                    else if (score >= 14 && score <= 19) interpretation = 'Depresi Ringan';
                    else if (score >= 20 && score <= 28) interpretation = 'Depresi Sedang';
                    else if (score >= 29 && score <= 63) interpretation = 'Depresi Berat';
                    return { score, interpretation };
                }
            },
            {
                id: 'Y_BOCS',
                name: 'Yale-Brown Obsessive Compulsive Scale (Y-BOCS - Adaptasi Mandiri)',
                description: 'Adaptasi dari Y-BOCS untuk laporan mandiri, mengukur tingkat keparahan gejala obsesif-kompulsif. Ini adalah versi yang disederhanakan dan bukan alat diagnostik klinis.',
                questions: [
                    { id: 'q1', text: 'Berapa banyak waktu yang Anda habiskan untuk pikiran obsesif (pikiran yang tidak diinginkan dan berulang)?' },
                    { id: 'q2', text: 'Seberapa besar pikiran obsesif Anda mengganggu kehidupan sehari-hari (pekerjaan, studi, hubungan)?' },
                    { id: 'q3', text: 'Seberapa besar penderitaan yang disebabkan oleh pikiran obsesif Anda?' },
                    { id: 'q4', text: 'Seberapa besar Anda berusaha menolak atau mengabaikan pikiran obsesif Anda?' },
                    { id: 'q5', text: 'Seberapa besar Anda merasa mengendalikan pikiran obsesif Anda?' },
                    { id: 'q6', text: 'Berapa banyak waktu yang Anda habiskan untuk melakukan ritual atau tindakan kompulsif (tindakan berulang untuk meredakan kecemasan)?' },
                    { id: 'q7', text: 'Seberapa besar ritual Anda mengganggu kehidupan sehari-hari (pekerjaan, studi, hubungan)?' },
                    { id: 'q8', text: 'Seberapa besar penderitaan yang disebabkan oleh ritual Anda?' },
                    { id: 'q9', text: 'Seberapa besar Anda berusaha menolak atau menahan ritual Anda?' },
                    { id: 'q10', text: 'Seberapa besar Anda merasa mengendalikan ritual Anda?' },
                ],
                options: [
                    { value: 0, text: 'Tidak ada' },
                    { value: 1, text: 'Ringan (sesekali)' },
                    { value: 2, text: 'Sedang (cukup sering)' },
                    { value: 3, text: 'Parah (sering dan mengganggu)' },
                    { value: 4, text: 'Sangat Parah (hampir setiap saat dan sangat mengganggu)' },
                ],
                // Function to calculate Y-BOCS score and interpretation
                calculateScore: (testAnswers) => {
                    const score = Object.values(testAnswers).reduce((sum, val) => sum + (val || 0), 0);
                    let interpretation = '';
                    if (score >= 0 && score <= 7) interpretation = 'Subklinis / Tanpa Gejala Signifikan';
                    else if (score >= 8 && score <= 15) interpretation = 'OCD Ringan';
                    else if (score >= 16 && score <= 23) interpretation = 'OCD Sedang';
                    else if (score >= 24 && score <= 31) interpretation = 'OCD Berat';
                    else if (score >= 32 && score <= 40) interpretation = 'OCD Sangat Berat';
                    return { score, interpretation };
                }
            },
            {
                id: 'STEPI',
                name: 'STEPI (Schizophrenia Test and Early Psychosis Indicator - Ilustratif)',
                description: 'Indikator awal psikosis. Ini adalah versi ilustratif dan bukan alat diagnostik klinis yang divalidasi.',
                questions: [
                    { id: 'q1', text: 'Apakah Anda pernah merasa bahwa pikiran Anda bukan milik Anda, atau bahwa orang lain memasukkan atau menarik pikiran dari kepala Anda?' },
                    { id: 'q2', text: 'Apakah Anda kadang-kadang mendengar suara atau melihat hal-hal yang tidak didengar atau dilihat orang lain?' },
                    { id: 'q3', text: 'Apakah Anda merasa bahwa ada kekuatan tak terlihat yang mengendalikan tindakan atau pikiran Anda?' },
                    { id: 'q4', text: 'Apakah Anda merasa sangat curiga atau paranoid terhadap orang lain, bahkan teman dan keluarga?' },
                    { id: 'q5', text: 'Apakah Anda merasa sulit untuk memahami apa yang orang lain katakan, atau merasa percakapan tidak masuk akal atau membingungkan?' },
                    { id: 'q6', text: 'Apakah Anda tiba-tiba kehilangan minat pada aktivitas yang dulu Anda nikmati, atau merasa sangat sulit untuk merasakan kesenangan (anhedonia)?' },
                    { id: 'q7', text: 'Apakah Anda merasa menarik diri secara sosial dari teman dan keluarga, atau mengalami kesulitan besar dalam berinteraksi sosial?' },
                    { id: 'q8', text: 'Apakah orang lain (teman, keluarga, rekan kerja) mengomentari perubahan signifikan dalam perilaku, cara berbicara, atau penampilan Anda?' },
                ],
                options: [
                    { value: 0, text: 'Tidak Pernah' },
                    { value: 1, text: 'Jarang (satu atau dua kali)' },
                    { value: 2, text: 'Kadang-kadang (beberapa kali, tidak sering)' },
                    { value: 3, text: 'Sering (terjadi secara teratur)' },
                    { value: 4, text: 'Sangat Sering (hampir setiap hari/sepanjang waktu)' },
                ],
                // Function to calculate STEPI score and interpretation
                calculateScore: (testAnswers) => {
                    const score = Object.values(testAnswers).reduce((sum, val) => sum + (val || 0), 0);
                    let interpretation = '';
                    if (score >= 0 && score <= 5) interpretation = 'Risiko Rendah';
                    else if (score >= 6 && score <= 12) interpretation = 'Risiko Sedang, Perlu Perhatian Lebih Lanjut';
                    else if (score >= 13) interpretation = 'Risiko Tinggi, Sangat Disarankan Konsultasi Profesional Segera';
                    return { score, interpretation };
                }
            }
        ];

        // Function to render a specific test
        function renderTest(testId) {
            const assessmentSectionsDiv = document.getElementById('assessment-sections');
            assessmentSectionsDiv.innerHTML = ''; // Clear previous content

            const test = tests.find(t => t.id === testId);
            if (!test) {
                assessmentSectionsDiv.innerHTML = '<p class="text-red-500 text-center">Tes tidak ditemukan.</p>';
                return;
            }

            activeTest = testId; // Set the active test
            document.getElementById('results-summary').classList.add('hidden'); // Hide summary if active

            const testDiv = document.createElement('div');
            testDiv.id = `test-${test.id}`;
            testDiv.className = 'p-6 bg-blue-50 rounded-lg border border-blue-200';

            testDiv.innerHTML = `
                <h2 class="text-2xl font-semibold text-blue-700 mb-4">${test.name}</h2>
                <p class="text-gray-700 mb-6">${test.description}</p>
                <form id="form-${test.id}" class="space-y-6">
                    ${test.questions.map(q => `
                        <div class="bg-white p-5 rounded-lg shadow-sm border border-gray-200">
                            <p class="font-medium text-gray-800 mb-3">${q.text}</p>
                            <div class="space-y-2">
                                ${test.options.map(option => `
                                    <label class="flex items-center text-gray-700 cursor-pointer">
                                        <input type="radio" name="${q.id}" value="${option.value}"
                                            class="form-radio h-4 w-4 text-blue-600 focus:ring-blue-500 mr-2"
                                            ${(answers[test.id] && answers[test.id][q.id] === option.value) ? 'checked' : ''}
                                            onchange="handleAnswerChange('${test.id}', '${q.id}', parseInt(this.value))">
                                        <span>${option.text}</span>
                                    </label>
                                `).join('')}
                            </div>
                        </div>
                    `).join('')}
                    <div class="flex justify-between mt-8">
                        <button type="button" onclick="resetAnswers('${test.id}')" class="px-6 py-3 bg-red-500 text-white rounded-md hover:bg-red-600 transition-colors duration-200 shadow-lg">Reset Jawaban</button>
                        <button type="button" onclick="finishAssessment()" class="px-6 py-3 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors duration-200 shadow-lg">Selesai Asesmen & Lihat Hasil</button>
                    </div>
                </form>
            `;
            assessmentSectionsDiv.appendChild(testDiv);
        }

        // Handle answer changes
        function handleAnswerChange(testId, questionId, value) {
            if (!answers[testId]) {
                answers[testId] = {};
            }
            answers[testId][questionId] = value;
            // console.log(`Answer for ${testId}-${questionId}: ${value}`); // For debugging
        }

        // Reset answers for a specific test
        function resetAnswers(testId) {
            answers[testId] = {};
            renderTest(testId); // Re-render the test to clear selections
            showAlert('Jawaban untuk tes ini telah direset.');
        }

        // Navigate to a specific test
        function goToTest(testId) {
            renderTest(testId);
            document.getElementById('navigation-buttons').classList.remove('hidden');
            document.getElementById('results-summary').classList.add('hidden');
        }

        // Calculate scores for all tests and show summary
        function finishAssessment() {
            calculateAllScores();
            displayResultsSummary();
            document.getElementById('assessment-sections').innerHTML = ''; // Clear test view
            activeTest = null;
        }

        // Calculate scores for all tests
        function calculateAllScores() {
            tests.forEach(test => {
                const testAnswers = answers[test.id] || {};
                scores[test.id] = test.calculateScore(testAnswers);
            });
        }

        // Display results summary
        function displayResultsSummary() {
            const summaryContentDiv = document.getElementById('summary-content');
            summaryContentDiv.innerHTML = ''; // Clear previous summary

            tests.forEach(test => {
                const scoreData = scores[test.id];
                const card = document.createElement('div');
                card.className = 'bg-white p-5 rounded-lg shadow border border-gray-200';
                card.innerHTML = `
                    <h3 class="text-xl font-semibold text-blue-600 mb-2">${test.name}</h3>
                    <p class="text-gray-700 mb-1">Skor Anda: <span class="font-bold text-lg text-blue-800">${scoreData?.score !== undefined ? scoreData.score : 'N/A'}</span></p>
                    <p class="text-gray-700">Interpretasi: <span class="font-semibold text-blue-700">${scoreData?.interpretation || 'Belum diisi'}</span></p>
                    <button onclick="goToTest('${test.id}')" class="mt-4 px-4 py-2 bg-blue-100 text-blue-700 rounded-md hover:bg-blue-200 transition-colors duration-200 text-sm">Lihat Detail Tes</button>
                `;
                summaryContentDiv.appendChild(card);
            });

            document.getElementById('results-summary').classList.remove('hidden');
        }

        // Reset the entire assessment
        function resetAssessment() {
            answers = {};
            scores = {};
            activeTest = null;
            document.getElementById('results-summary').classList.add('hidden');
            document.getElementById('assessment-sections').innerHTML = '<p class="text-gray-600 text-center text-lg">Pilih asesmen di atas untuk memulai.</p>';
            showAlert('Asesmen telah direset. Silakan pilih tes untuk memulai kembali.');
        }

        // Handle printing the results
        function handlePrint() {
            const printContent = document.getElementById('results-summary').cloneNode(true);
            // Remove non-printable elements like buttons
            printContent.querySelectorAll('.no-print').forEach(el => el.remove());
            // Add custom header for printing
            const printHeader = document.createElement('h1');
            printHeader.className = 'text-3xl font-bold text-center text-blue-800 mb-6 print-only';
            printHeader.innerText = 'Laporan Asesmen Mandiri Kesehatan Jiwa';
            const printDate = document.createElement('p');
            printDate.className = 'text-center text-gray-600 mb-8 text-sm print-only';
            printDate.innerText = `Tanggal: ${new Date().toLocaleDateString('id-ID', { year: 'numeric', month: 'long', day: 'numeric' })}`;

            // Create a new window for printing
            const printWindow = window.open('', '_blank');
            printWindow.document.write('<html><head><title>Laporan Asesmen Kesehatan Jiwa</title>');
            printWindow.document.write('<link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">');
            printWindow.document.write('<style>');
            printWindow.document.write(`
                body { font-family: 'Inter', sans-serif; margin: 20px; color: #333; }
                h1 { font-size: 2em; margin-bottom: 1em; }
                h2 { font-size: 1.5em; margin-top: 1.5em; margin-bottom: 0.8em; color: #1e3a8a; }
                h3 { font-size: 1.2em; margin-top: 1em; margin-bottom: 0.5em; color: #1e3a8a; }
                .result-section { margin-bottom: 20px; padding: 15px; border: 1px solid #e2e8f0; border-radius: 8px; background-color: #f8fafc; }
                .disclaimer { font-size: 0.9em; color: #555; margin-top: 30px; padding: 10px; border: 1px dashed #ccc; border-radius: 5px; background-color: #fff; }
                table { width: 100%; border-collapse: collapse; margin-top: 10px; }
                th, td { border: 1px solid #ccc; padding: 8px; text-align: left; font-size: 0.9em; }
                th { background-color: #e2e8f0; font-weight: 600; }
                p { margin-bottom: 0.5em; }
                .score-value { font-weight: bold; color: #1e3a8a; }
                .interpretation-value { font-weight: 600; color: #065f46; } /* Green for interpretation */
                .summary-card {
                    background-color: #fff;
                    padding: 15px;
                    border-radius: 8px;
                    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
                    margin-bottom: 15px;
                }
            `);
            printWindow.document.write('</style>');
            printWindow.document.write('</head><body>');
            printWindow.document.write(printHeader.outerHTML);
            printWindow.document.write(printDate.outerHTML);
            printWindow.document.write('<div class="result-section">');
            printWindow.document.write(printContent.innerHTML);
            printWindow.document.write('</div>');

            // Add detailed results for each test
            tests.forEach(test => {
                const testAnswers = answers[test.id] || {};
                const scoreData = scores[test.id];

                printWindow.document.write(`<h2 class="mt-8">${test.name} - Detail</h2>`);
                printWindow.document.write(`<p><strong>Deskripsi:</strong> ${test.description}</p>`);
                printWindow.document.write(`<p><strong>Skor Total:</strong> <span class="score-value">${scoreData?.score !== undefined ? scoreData.score : 'N/A'}</span></p>`);
                printWindow.document.write(`<p><strong>Interpretasi:</strong> <span class="interpretation-value">${scoreData?.interpretation || 'Belum diisi'}</span></p>`);

                printWindow.document.write('<table>');
                printWindow.document.write('<thead><tr><th>No.</th><th>Pertanyaan</th><th>Jawaban Anda</th></tr></thead>');
                printWindow.document.write('<tbody>');
                test.questions.forEach((q, index) => {
                    const answerValue = testAnswers[q.id];
                    const optionText = test.options.find(opt => opt.value === answerValue)?.text || 'Belum dijawab';
                    printWindow.document.write(`<tr><td>${index + 1}.</td><td>${q.text}</td><td>${optionText}</td></tr>`);
                });
                printWindow.document.write('</tbody></table>');
            });

            printWindow.document.write('<div class="disclaimer">');
            printWindow.document.write('<p><strong>Penting:</strong> Asesmen ini bukan diagnosis. Selalu konsultasikan dengan profesional kesehatan jiwa (psikolog, psikiater) untuk diagnosis dan perawatan yang akurat. Hasil ini dirancang sebagai alat skrining awal dan bukan pengganti evaluasi klinis yang komprehensif.</p>');
            printWindow.document.write('<p>Informasi lebih lanjut dapat ditemukan di situs resmi Organisasi Kesehatan Dunia (WHO) terkait kesehatan mental.</p>');
            printWindow.document.write('</div>');

            printWindow.document.write('</body></html>');
            printWindow.document.close();
            printWindow.focus(); // Focus on the new window
            printWindow.print();
        }

        // Handle exporting results to XLSX
        function handleExportXLSX() {
            // Prepare data for XLSX
            const workbook = XLSX.utils.book_new();

            // Summary sheet
            const summaryData = [['Asesmen Kesehatan Jiwa Mandiri']];
            summaryData.push(['Tanggal:', new Date().toLocaleDateString('id-ID', { year: 'numeric', month: 'long', day: 'numeric' })]);
            summaryData.push([]); // Empty row for spacing

            summaryData.push(['Ringkasan Hasil Asesmen:']);
            tests.forEach(test => {
                const scoreData = scores[test.id];
                summaryData.push([test.name, `Skor: ${scoreData?.score !== undefined ? scoreData.score : 'N/A'}`, `Interpretasi: ${scoreData?.interpretation || 'Belum diisi'}`]);
            });
            summaryData.push([]);
            summaryData.push(['Penting:', 'Asesmen ini bukan diagnosis. Selalu konsultasikan dengan profesional kesehatan jiwa untuk diagnosis dan perawatan yang akurat.']);
            const summarySheet = XLSX.utils.aoa_to_sheet(summaryData);
            XLSX.utils.book_append_sheet(workbook, summarySheet, 'Ringkasan');

            // Detailed sheets for each test
            tests.forEach(test => {
                const testAnswers = answers[test.id] || {};
                const sheetData = [];
                sheetData.push([test.name + ' - Detail Jawaban']);
                sheetData.push(['Pertanyaan', 'Jawaban Yang Dipilih']);
                test.questions.forEach(q => {
                    const answerValue = testAnswers[q.id];
                    const optionText = test.options.find(opt => opt.value === answerValue)?.text || 'Belum dijawab';
                    sheetData.push([q.text, optionText]);
                });
                sheetData.push([]); // Spacer
                sheetData.push(['Skor Total', scores[test.id]?.score !== undefined ? scores[test.id].score : 'N/A']);
                sheetData.push(['Interpretasi', scores[test.id]?.interpretation || 'Belum diisi']);

                const worksheet = XLSX.utils.aoa_to_sheet(sheetData);
                XLSX.utils.book_append_sheet(workbook, worksheet, test.id);
            });

            XLSX.writeFile(workbook, 'Hasil_Asesmen_Kesehatan_Jiwa.xlsx');
            showAlert('Hasil telah diekspor ke XLSX!');
        }

        // Handle exporting results to PDF
        async function handleExportPDF() {
            const { jsPDF } = window.jspdf;
            const doc = new jsPDF('p', 'pt', 'a4'); // 'p' for portrait, 'pt' for points, 'a4' for size
            const margin = 40; // Margins from the edge of the PDF
            let yPos = margin; // Current Y position for content

            // Add header
            doc.setFontSize(24);
            doc.setTextColor(30, 58, 138); // Blue-800
            doc.text("Laporan Asesmen Mandiri Kesehatan Jiwa", doc.internal.pageSize.getWidth() / 2, yPos + 10, { align: 'center' });
            yPos += 40;
            doc.setFontSize(10);
            doc.setTextColor(75, 85, 99); // Gray-600
            doc.text(`Tanggal: ${new Date().toLocaleDateString('id-ID', { year: 'numeric', month: 'long', day: 'numeric' })}`, doc.internal.pageSize.getWidth() / 2, yPos, { align: 'center' });
            yPos += 30;

            // Add summary section
            doc.setFontSize(18);
            doc.setTextColor(30, 58, 138); // Blue-700
            doc.text("Ringkasan Hasil Asesmen", margin, yPos);
            yPos += 25;

            doc.setFontSize(12);
            doc.setTextColor(51, 65, 85); // Gray-700
            tests.forEach(test => {
                const scoreData = scores[test.id];
                const line1 = `${test.name}: Skor Anda: ${scoreData?.score !== undefined ? scoreData.score : 'N/A'}`;
                const line2 = `Interpretasi: ${scoreData?.interpretation || 'Belum diisi'}`;
                doc.text(line1, margin, yPos);
                yPos += 15;
                doc.text(line2, margin + 10, yPos);
                yPos += 25;
            });

            // Add disclaimer
            yPos += 10;
            doc.setFontSize(10);
            doc.setTextColor(146, 64, 14); // Yellow-800
            doc.text("Penting: Asesmen ini bukan diagnosis. Selalu konsultasikan dengan profesional kesehatan jiwa (psikolog, psikiater) untuk diagnosis dan perawatan yang akurat. Hasil ini dirancang sebagai alat skrining awal dan bukan pengganti evaluasi klinis yang komprehensif.", margin, yPos, { maxWidth: doc.internal.pageSize.getWidth() - 2 * margin });
            yPos += 60;


            // Add detailed results for each test
            tests.forEach(test => {
                const testAnswers = answers[test.id] || {};
                const scoreData = scores[test.id];

                // Check for new page if needed
                if (yPos > doc.internal.pageSize.getHeight() - margin - 100) {
                    doc.addPage();
                    yPos = margin;
                }

                doc.setFontSize(18);
                doc.setTextColor(30, 58, 138); // Blue-700
                doc.text(`${test.name} - Detail`, margin, yPos);
                yPos += 25;

                doc.setFontSize(12);
                doc.setTextColor(51, 65, 85);
                doc.text(`Deskripsi: ${test.description}`, margin, yPos, { maxWidth: doc.internal.pageSize.getWidth() - 2 * margin });
                yPos += 20;
                doc.text(`Skor Total: ${scoreData?.score !== undefined ? scoreData.score : 'N/A'}`, margin, yPos);
                yPos += 15;
                doc.text(`Interpretasi: ${scoreData?.interpretation || 'Belum diisi'}`, margin, yPos);
                yPos += 25;

                // Create table for questions and answers
                const tableColumn = ["No.", "Pertanyaan", "Jawaban Anda"];
                const tableRows = [];

                test.questions.forEach((q, index) => {
                    const answerValue = testAnswers[q.id];
                    const optionText = test.options.find(opt => opt.value === answerValue)?.text || 'Belum dijawab';
                    tableRows.push([index + 1, q.text, optionText]);
                });

                // Auto-table
                doc.autoTable({
                    startY: yPos,
                    head: [tableColumn],
                    body: tableRows,
                    theme: 'grid',
                    styles: {
                        fontSize: 10,
                        cellPadding: 6,
                        textColor: [51, 65, 85], // Gray-700
                        lineColor: [226, 232, 240], // Gray-200
                        lineWidth: 0.5,
                        font: 'Inter', // Custom font if loaded, otherwise default
                    },
                    headStyles: {
                        fillColor: [241, 245, 249], // Slate-100
                        textColor: [51, 65, 85], // Gray-700
                        fontStyle: 'bold',
                    },
                    columnStyles: {
                        0: { cellWidth: 30 }, // No.
                        1: { cellWidth: 'auto' }, // Pertanyaan
                        2: { cellWidth: 150 } // Jawaban Anda
                    },
                    margin: { left: margin, right: margin },
                    didParseCell: function (data) {
                        if (data.section === 'body' && data.column.index === 1) { // Apply styling for question column if needed
                            data.cell.styles.fontStyle = 'normal';
                        }
                    },
                    didDrawPage: function (data) {
                        yPos = data.cursor.y + 20; // Update yPos after table
                    }
                });

                yPos = doc.autoTable.previous.finalY + 30; // Update yPos after each table
            });

            doc.save('Laporan_Asesmen_Kesehatan_Jiwa.pdf');
            showAlert('Hasil telah diekspor ke PDF!');
        }


        // Initialize the app when the DOM is loaded
        document.addEventListener('DOMContentLoaded', () => {
            // Display initial message or instructions
            document.getElementById('assessment-sections').innerHTML = '<p class="text-gray-600 text-center text-lg mt-8">Pilih salah satu asesmen di atas untuk memulai.</p>';
        });
    </script>
</body>
</html>
