{"name": "santrimental", "version": "1.0.0", "description": "Platform modern untuk skrining kesehatan mental dengan berbagai jenis assessment", "main": "index.js", "scripts": {"dev": "concurrently \"npm run backend\" \"npm run frontend\"", "backend": "cd backend && php artisan serve", "frontend": "live-server --port=3000 --host=localhost", "build": "npm run build:css && npm run build:js", "build:css": "tailwindcss -i ./css/input.css -o ./css/output.css --minify", "build:js": "webpack --mode=production", "watch": "concurrently \"npm run watch:css\" \"npm run watch:js\"", "watch:css": "tailwindcss -i ./css/input.css -o ./css/output.css --watch", "watch:js": "webpack --mode=development --watch", "test": "jest", "test:watch": "jest --watch", "lint": "eslint js/**/*.js", "lint:fix": "eslint js/**/*.js --fix", "format": "prettier --write \"**/*.{js,css,html,md}\"", "setup": "npm install && cd backend && composer install", "migrate": "cd backend && php artisan migrate", "seed": "cd backend && php artisan db:seed", "fresh": "cd backend && php artisan migrate:fresh --seed", "serve": "cd backend && php artisan serve --port=8000", "docker:up": "docker-compose up -d", "docker:down": "docker-compose down", "docker:build": "docker-compose build", "docker:logs": "docker-compose logs -f"}, "keywords": ["mental-health", "assessment", "laravel", "javascript", "healthcare", "screening", "srq20", "gse", "mscs"], "author": "SantriMental Team", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/yourusername/santrimental.git"}, "bugs": {"url": "https://github.com/yourusername/santrimental/issues"}, "homepage": "https://github.com/yourusername/santrimental#readme", "devDependencies": {"@babel/core": "^7.22.0", "@babel/preset-env": "^7.22.0", "babel-loader": "^9.1.0", "concurrently": "^8.2.0", "eslint": "^8.42.0", "eslint-config-prettier": "^8.8.0", "eslint-plugin-prettier": "^4.2.1", "jest": "^29.5.0", "live-server": "^1.2.2", "prettier": "^2.8.8", "tailwindcss": "^3.3.0", "webpack": "^5.88.0", "webpack-cli": "^5.1.0"}, "dependencies": {"chart.js": "^4.3.0", "axios": "^1.4.0"}, "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}, "browserslist": ["> 1%", "last 2 versions", "not dead"]}