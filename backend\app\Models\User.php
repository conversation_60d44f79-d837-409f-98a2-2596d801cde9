<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use <PERSON><PERSON>\Sanctum\HasApiTokens;

class User extends Authenticatable
{
    use HasApiTokens, HasFactory, Notifiable;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'first_name',
        'last_name',
        'email',
        'password',
        'google_id',
        'avatar',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password',
        'remember_token',
        'google_id',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'email_verified_at' => 'datetime',
        'password' => 'hashed',
    ];

    /**
     * Get the user's full name.
     *
     * @return string
     */
    public function getFullNameAttribute(): string
    {
        return "{$this->first_name} {$this->last_name}";
    }

    /**
     * Get all assessments for the user.
     */
    public function assessments()
    {
        return $this->hasMany(Assessment::class);
    }

    /**
     * Get the latest assessment for the user.
     */
    public function latestAssessment()
    {
        return $this->hasOne(Assessment::class)->latestOfMany();
    }

    /**
     * Get monthly assessment statistics.
     */
    public function getMonthlyStats()
    {
        return $this->assessments()
            ->selectRaw('COUNT(*) as total_assessments')
            ->selectRaw('AVG(total_score) as average_score')
            ->selectRaw('COUNT(CASE WHEN status = "concern" THEN 1 END) as concerns')
            ->whereMonth('created_at', now()->month)
            ->whereYear('created_at', now()->year)
            ->first();
    }
}
