<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Visualisasi Algoritma Binary Search</title>
    
    <!-- Memuat Tailwind CSS untuk styling -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Memuat Font Inter dari Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;700&display=swap" rel="stylesheet">

    <style>
        /* Menggunakan font Inter sebagai default */
        body {
            font-family: 'Inter', sans-serif;
        }
        /* Transisi halus untuk semua properti yang berubah */
        .array-element-wrapper, .array-element, .pointer {
            transition: all 0.5s ease-in-out;
        }
        /* Efek hover untuk elemen array */
        .array-element:hover {
            transform: translateY(-4px);
        }
        /* Styling untuk pointer (low, mid, high) */
        .pointer {
            opacity: 0; /* Sembunyikan pointer secara default */
            transform: translateY(10px);
        }
    </style>
</head>
<body class="bg-gray-100 text-gray-800 flex items-center justify-center min-h-screen p-4">

    <div class="w-full max-w-5xl bg-white rounded-xl shadow-lg p-6 md:p-8">
        
        <!-- Judul Aplikasi -->
        <div class="text-center mb-6">
            <h1 class="text-2xl md:text-3xl font-bold text-gray-900">Visualisasi Algoritma Binary Search</h1>
            <p class="text-gray-600 mt-1">Simulasi pencarian pada **array terurut** dengan membagi dua ruang pencarian.</p>
        </div>

        <!-- Kontainer untuk visualisasi array dan pointer -->
        <div id="array-container" class="flex flex-wrap justify-center items-end gap-2 mb-8 min-h-[100px] relative">
            <!-- Elemen-elemen array dan pointer akan dibuat oleh JavaScript di sini -->
        </div>

        <!-- Panel Kontrol -->
        <div class="bg-gray-50 p-4 rounded-lg shadow-inner">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 items-center">
                <!-- Input untuk angka yang dicari -->
                <div class="flex flex-col">
                    <label for="search-input" class="text-sm font-medium text-gray-700 mb-1">Angka yang Dicari:</label>
                    <input type="number" id="search-input" placeholder="e.g., 42" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500">
                </div>
                
                <!-- Tombol Aksi -->
                <div class="md:col-span-2 flex flex-col sm:flex-row gap-3 mt-4 md:mt-0 md:items-end h-full">
                    <button id="search-button" class="w-full sm:w-auto flex-grow bg-indigo-600 text-white font-semibold py-2 px-4 rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition duration-150 ease-in-out">
                        Mulai Pencarian
                    </button>
                    <button id="reset-button" class="w-full sm:w-auto flex-grow bg-gray-600 text-white font-semibold py-2 px-4 rounded-md hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 transition duration-150 ease-in-out">
                        Buat Array Baru
                    </button>
                </div>
            </div>
        </div>

        <!-- Pesan Status -->
        <div id="status-message" class="text-center mt-6 font-medium text-lg text-gray-700 min-h-[28px]">
            <!-- Pesan status akan ditampilkan di sini -->
        </div>

    </div>

    <script>
        // --- Referensi Elemen DOM ---
        const arrayContainer = document.getElementById('array-container');
        const searchInput = document.getElementById('search-input');
        const searchButton = document.getElementById('search-button');
        const resetButton = document.getElementById('reset-button');
        const statusMessage = document.getElementById('status-message');

        // --- Variabel State ---
        let dataArray = [];
        const ARRAY_SIZE = 15; // Jumlah elemen dalam array
        const ANIMATION_SPEED_MS = 1200; // Kecepatan animasi dalam milidetik

        // --- Fungsi Utama ---

        /**
         * Fungsi untuk menunda eksekusi (digunakan dalam animasi).
         * @param {number} ms - Waktu tunda dalam milidetik.
         * @returns {Promise}
         */
        function sleep(ms) {
            return new Promise(resolve => setTimeout(resolve, ms));
        }

        /**
         * Menghasilkan array baru, mengurutkannya, dan merendernya ke layar.
         */
        function generateAndRenderArray() {
            dataArray = [];
            const usedNumbers = new Set();
            while (dataArray.length < ARRAY_SIZE) {
                const randomNumber = Math.floor(Math.random() * 100) + 1;
                if (!usedNumbers.has(randomNumber)) {
                    dataArray.push(randomNumber);
                    usedNumbers.add(randomNumber);
                }
            }
            // Binary search memerlukan array yang terurut
            dataArray.sort((a, b) => a - b);
            
            renderArray();
            statusMessage.textContent = 'Array terurut baru telah dibuat. Masukkan angka untuk dicari.';
            statusMessage.className = 'text-center mt-6 font-medium text-lg text-gray-700';
            enableControls();
        }

        /**
         * Merender array saat ini ke dalam DOM, termasuk elemen pointer.
         */
        function renderArray() {
            arrayContainer.innerHTML = ''; // Bersihkan kontainer
            dataArray.forEach((value, index) => {
                // Wrapper untuk elemen dan pointernya
                const wrapper = document.createElement('div');
                wrapper.id = `wrapper-${index}`;
                wrapper.className = 'array-element-wrapper flex flex-col items-center relative';

                // Pointer (low, mid, high)
                const pointerDiv = document.createElement('div');
                pointerDiv.id = `pointer-${index}`;
                pointerDiv.className = 'pointer text-xs font-bold mb-1';
                
                // Elemen array (kotak angka)
                const elementDiv = document.createElement('div');
                elementDiv.id = `element-${index}`;
                elementDiv.className = 'array-element flex items-center justify-center w-12 h-12 md:w-14 md:h-14 bg-gray-200 border-2 border-gray-300 rounded-md text-lg font-bold';
                elementDiv.textContent = value;
                
                wrapper.appendChild(pointerDiv);
                wrapper.appendChild(elementDiv);
                arrayContainer.appendChild(wrapper);
            });
        }
        
        /**
         * Membersihkan semua status pointer dari tampilan.
         */
        function clearAllPointers() {
             for (let i = 0; i < dataArray.length; i++) {
                const pointer = document.getElementById(`pointer-${i}`);
                if (pointer) {
                    pointer.textContent = '';
                    pointer.className = 'pointer text-xs font-bold mb-1';
                    pointer.style.opacity = 0;
                }
             }
        }

        /**
         * Menjalankan algoritma Binary Search dengan animasi.
         */
        async function performBinarySearch() {
            const targetValue = parseInt(searchInput.value, 10);

            if (isNaN(targetValue)) {
                statusMessage.textContent = 'Harap masukkan angka yang valid.';
                statusMessage.className = 'text-center mt-6 font-medium text-lg text-red-600';
                return;
            }

            disableControls();
            
            // Reset tampilan visual sebelum memulai
            for(let i = 0; i < dataArray.length; i++) {
                document.getElementById(`element-${i}`).classList.remove('bg-green-400', 'border-green-600', 'text-white');
                document.getElementById(`wrapper-${i}`).style.opacity = 1;
            }
            
            let low = 0;
            let high = dataArray.length - 1;
            let found = false;

            while (low <= high) {
                clearAllPointers();
                
                let mid = Math.floor((low + high) / 2);
                
                // --- Update Visual Pointer ---
                const lowPointer = document.getElementById(`pointer-${low}`);
                const highPointer = document.getElementById(`pointer-${high}`);
                const midPointer = document.getElementById(`pointer-${mid}`);
                const midElement = document.getElementById(`element-${mid}`);
                
                lowPointer.textContent = 'LOW';
                lowPointer.className = 'pointer text-xs font-bold mb-1 text-blue-600';
                lowPointer.style.opacity = 1;

                highPointer.textContent = 'HIGH';
                highPointer.className = 'pointer text-xs font-bold mb-1 text-red-600';
                highPointer.style.opacity = 1;

                midPointer.textContent = 'MID';
                midPointer.className = 'pointer text-xs font-bold mb-1 text-purple-600';
                midPointer.style.opacity = 1;
                
                statusMessage.textContent = `Mengecek elemen tengah di indeks ${mid} (nilai: ${dataArray[mid]})`;
                statusMessage.className = 'text-center mt-6 font-medium text-lg text-purple-700';

                midElement.classList.add('transform', 'scale-110', 'border-purple-500');
                
                await sleep(ANIMATION_SPEED_MS);

                if (dataArray[mid] === targetValue) {
                    statusMessage.textContent = `Elemen ${targetValue} ditemukan di indeks ${mid}!`;
                    statusMessage.className = 'text-center mt-6 font-medium text-lg text-green-600';
                    midElement.classList.remove('border-purple-500');
                    midElement.classList.add('bg-green-400', 'border-green-600', 'text-white');
                    found = true;
                    break;
                } else if (dataArray[mid] < targetValue) {
                    statusMessage.textContent = `${targetValue} > ${dataArray[mid]}. Mengabaikan bagian kiri.`;
                    // Animasikan elemen yang diabaikan
                    for (let i = low; i <= mid; i++) {
                        document.getElementById(`wrapper-${i}`).style.opacity = 0.3;
                    }
                    low = mid + 1;
                } else { // dataArray[mid] > targetValue
                    statusMessage.textContent = `${targetValue} < ${dataArray[mid]}. Mengabaikan bagian kanan.`;
                     // Animasikan elemen yang diabaikan
                    for (let i = mid; i <= high; i++) {
                         document.getElementById(`wrapper-${i}`).style.opacity = 0.3;
                    }
                    high = mid - 1;
                }
                
                midElement.classList.remove('transform', 'scale-110', 'border-purple-500');
                await sleep(ANIMATION_SPEED_MS);
            }

            if (!found) {
                statusMessage.textContent = `Elemen ${targetValue} tidak ditemukan dalam array.`;
                statusMessage.className = 'text-center mt-6 font-medium text-lg text-red-600';
            }
            
            clearAllPointers();
            enableControls(false); // Aktifkan kontrol tanpa membersihkan input
        }
        
        /**
         * Menonaktifkan tombol dan input selama animasi berjalan.
         */
        function disableControls() {
            searchInput.disabled = true;
            searchButton.disabled = true;
            resetButton.disabled = true;
            searchButton.classList.add('opacity-50', 'cursor-not-allowed');
            resetButton.classList.add('opacity-50', 'cursor-not-allowed');
        }
        
        /**
         * Mengaktifkan kembali tombol dan input.
         * @param {boolean} clearInput - Apakah akan membersihkan input field.
         */
        function enableControls(clearInput = true) {
            searchInput.disabled = false;
            searchButton.disabled = false;
            resetButton.disabled = false;
            searchButton.classList.remove('opacity-50', 'cursor-not-allowed');
            resetButton.classList.remove('opacity-50', 'cursor-not-allowed');
            if(clearInput) {
                searchInput.value = '';
            }
        }

        // --- Event Listeners ---
        searchButton.addEventListener('click', performBinarySearch);
        resetButton.addEventListener('click', generateAndRenderArray);
        
        searchInput.addEventListener('keyup', function(event) {
            if (event.key === 'Enter') {
                searchButton.click();
            }
        });

        // Inisialisasi aplikasi saat halaman dimuat
        window.addEventListener('DOMContentLoaded', generateAndRenderArray);

    </script>
</body>
</html>
