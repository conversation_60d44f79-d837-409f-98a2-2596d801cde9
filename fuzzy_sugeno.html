<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simulasi Fuzzy Sugeno: Orde 0 & 1</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chartjs-plugin-annotation@1.4.0"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;700&family=Roboto+Mono:wght@400;500&display=swap" rel="stylesheet">
    <style>
        /* General Styling */
        body {
            font-family: 'Inter', sans-serif;
            background-color: #f0f4f8;
            color: #334155;
            margin: 0;
            padding: 1rem;
            display: flex;
            flex-direction: column;
            align-items: center;
        }

        /* Main Container */
        .container {
            width: 100%;
            max-width: 1200px;
            background: #ffffff;
            border-radius: 1rem;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
            padding: 2rem;
            box-sizing: border-box;
        }

        h1, h2, h3, h4 {
            color: #1e293b;
            margin-bottom: 1rem;
        }
        
        h1 {
            font-size: 2rem;
            font-weight: 700;
            text-align: center;
        }

        h2 {
            font-size: 1.5rem;
            margin-top: 2rem;
            padding-bottom: 0.5rem;
            border-bottom: 2px solid #e2e8f0;
            text-align: center;
        }

        h3 {
             text-align: left;
             font-size: 1.2rem;
             color: #475569;
        }
        
        h4 {
            font-size: 1.1rem;
            color: #334155;
            margin-top: 1.5rem;
            margin-bottom: 0.5rem;
            padding-left: 10px;
            border-left: 3px solid #6d28d9; /* Sugeno purple */
        }

        /* Layout */
        .main-layout {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 2rem;
        }

        @media (max-width: 900px) {
            .main-layout {
                grid-template-columns: 1fr;
            }
        }
        
        .main-layout > div {
            padding: 1.5rem;
            background-color: #f8fafc;
            border-radius: 0.75rem;
            border: 1px solid #e2e8f0;
        }

        /* Input Controls */
        .input-controls .control-group {
            margin-bottom: 1.5rem;
        }

        .input-controls label {
            display: block;
            font-weight: 500;
            margin-bottom: 0.5rem;
            color: #475569;
        }

        .input-controls input[type="range"] {
            width: 100%;
            cursor: pointer;
        }
        
        .order-selector {
            margin-bottom: 2rem;
            background-color: #f8fafc;
            padding: 1rem;
            border-radius: 0.75rem;
            display: flex;
            gap: 1rem;
            justify-content: center;
        }
        .order-selector label {
            cursor: pointer;
            padding: 0.5rem 1rem;
            border-radius: 0.5rem;
            transition: all 0.2s ease-in-out;
        }
        .order-selector input {
            display: none;
        }
        .order-selector input:checked + label {
            background-color: #8b5cf6;
            color: white;
            font-weight: 500;
            box-shadow: 0 4px 10px rgba(139, 92, 246, 0.3);
        }

        /* Output Display */
        .output-display {
            text-align: center;
            padding: 2rem 1rem;
        }

        .output-display .label {
            font-size: 1.2rem;
            color: #64748b;
            margin-bottom: 0.5rem;
        }

        .output-display .value {
            font-size: 3.5rem;
            font-weight: 700;
            color: #1e293b;
            line-height: 1;
        }

        .output-display .unit {
            font-size: 1.5rem;
            color: #475569;
            margin-left: 0.5rem;
        }

        /* Calculation Steps */
        .calculation-steps {
            margin-top: 2rem;
            background-color: #ffffff;
            padding: 1.5rem;
            border-radius: 0.75rem;
            border: 1px solid #e2e8f0;
        }

        .calculation-steps .step {
            margin-bottom: 1.5rem;
        }
        
        .calculation-steps code {
            font-family: 'Roboto Mono', monospace;
            background-color: #e2e8f0;
            padding: 0.2rem 0.4rem;
            border-radius: 4px;
            font-size: 0.95em;
        }
        
        .calculation-steps ul { list-style-type: none; padding-left: 0; }
        .calculation-steps li {
            font-family: 'Roboto Mono', monospace; background: #f8fafc; border: 1px solid #e2e8f0;
            padding: 0.75rem; margin-bottom: 0.5rem; border-radius: 0.5rem;
            font-size: 0.9em;
        }
        .calculation-steps .formula {
            font-family: 'Roboto Mono', monospace; background-color: #f3e8ff; padding: 1rem;
            border-radius: 0.5rem; border-left: 4px solid #8b5cf6;
            overflow-x: auto; white-space: pre-wrap; margin-top: 1rem;
        }
        .calculation-steps .result-box {
            background-color: #f5f3ff; border: 1px solid #ddd6fe; padding: 1rem;
            border-radius: 0.5rem; margin-top: 1rem;
        }

        /* Graph Sections */
        .graphs-container { display: grid; grid-template-columns: 1fr; gap: 1.5rem; margin-top: 2rem; }
        .graph-wrapper { background-color: #f8fafc; padding: 1.5rem; border-radius: 0.75rem; border: 1px solid #e2e8f0; }
        .graph-wrapper h3 { text-align: center; margin-top: 0; }
        
        #implication-table { width: 100%; border-collapse: collapse; margin-top: 1rem; }
        #implication-table th, #implication-table td { border: 1px solid #e2e8f0; padding: 0.75rem; text-align: center; font-size: 0.9rem; }
        #implication-table th { background-color: #f8fafc; }
    </style>
</head>
<body>

    <div class="container">
        <h1>Simulasi Logika Fuzzy Sugeno</h1>
        <p style="text-align:center; max-width: 700px; margin: 0 auto 2rem auto;">Kontrol Kecepatan Kipas Angin (Pilih Orde Nol atau Orde Satu)</p>

        <div class="main-layout">
            <div class="input-output-panel">
                <div class="order-selector">
                    <input type="radio" id="order0" name="sugeno-order" value="0" checked>
                    <label for="order0">Orde Nol (Output Konstanta)</label>
                    <input type="radio" id="order1" name="sugeno-order" value="1">
                    <label for="order1">Orde Satu (Output Fungsi Linear)</label>
                </div>
                
                <div class="input-controls">
                    <h3>Parameter Input</h3>
                    <div class="control-group">
                        <label for="suhu">Suhu: <span id="suhu-value" class="value-display">25</span> °C</label>
                        <input type="range" id="suhu" min="15" max="40" value="25" step="0.1">
                    </div>
                    <div class="control-group">
                        <label for="kelembapan">Kelembapan: <span id="kelembapan-value" class="value-display">60</span> %</label>
                        <input type="range" id="kelembapan" min="20" max="100" value="60" step="1">
                    </div>
                    <div class="control-group">
                        <label for="amonia">Kadar Amonia: <span id="amonia-value" class="value-display">10</span> ppm</label>
                        <input type="range" id="amonia" min="0" max="40" value="10" step="0.5">
                    </div>
                </div>
                
                <div class="output-display">
                    <div class="label">Rekomendasi Kecepatan Kipas</div>
                    <div>
                        <span id="output-value" class="value">0</span><span class="unit">RPM</span>
                    </div>
                </div>
            </div>
            
            <div class="membership-graphs">
                <h3 style="text-align:center;">Grafik Fungsi Keanggotaan Input</h3>
                <div class="graphs-container">
                    <div class="graph-wrapper"><h3>Suhu (°C)</h3><canvas id="suhuChart"></canvas></div>
                    <div class="graph-wrapper"><h3>Kelembapan (%)</h3><canvas id="kelembapanChart"></canvas></div>
                    <div class="graph-wrapper"><h3>Amonia (ppm)</h3><canvas id="amoniaChart"></canvas></div>
                </div>
            </div>
        </div>

        <div class="calculation-steps">
            <h2>Perhitungan Manual (Step-by-Step)</h2>
            <div id="perhitungan-detail"></div>
        </div>
        
        <div class="final-visualization" style="margin-top: 2rem;">
            <h2>Visualisasi Hasil Akhir (Defuzzifikasi)</h2>
            <div class="graph-wrapper">
                <h3>Kontribusi Aturan & Rata-Rata Terbobot</h3>
                <canvas id="sugenoVisChart"></canvas>
            </div>
        </div>
    </div>

    <script>
        // === DOM ELEMENTS ===
        const suhuSlider = document.getElementById('suhu');
        const kelembapanSlider = document.getElementById('kelembapan');
        const amoniaSlider = document.getElementById('amonia');
        const suhuValue = document.getElementById('suhu-value');
        const kelembapanValue = document.getElementById('kelembapan-value');
        const amoniaValue = document.getElementById('amonia-value');
        const outputValue = document.getElementById('output-value');
        const perhitunganDetailDiv = document.getElementById('perhitungan-detail');
        const orderRadios = document.querySelectorAll('input[name="sugeno-order"]');

        // === CHART.JS SETUP ===
        let suhuChart, kelembapanChart, amoniaChart, sugenoVisChart;
        const CHART_COLORS = {
            blue: 'rgba(59, 130, 246, 0.6)', green: 'rgba(16, 185, 129, 0.6)',
            yellow: 'rgba(234, 179, 8, 0.6)', red: 'rgba(239, 68, 68, 0.6)',
            purple: 'rgba(139, 92, 246, 0.6)', inputLine: 'rgba(239, 68, 68, 1)',
            resultLine: 'rgba(16, 185, 129, 1)'
        };

        // === FUZZY MEMBERSHIP FUNCTIONS (Antecedent) ===
        const membershipSuhu = x => ({
            dingin: x <= 20 ? 1 : (x >= 25 ? 0 : (25 - x) / 5),
            hangat: (x <= 20 || x >= 32) ? 0 : (x < 26 ? (x - 20) / 6 : (32 - x) / 6),
            panas: x <= 28 ? 0 : (x >= 34 ? 1 : (x - 28) / 6)
        });
        const membershipKelembapan = x => ({
            kering: x <= 40 ? 1 : (x >= 60 ? 0 : (60 - x) / 20),
            ideal: (x <= 50 || x >= 80) ? 0 : (x < 65 ? (x - 50) / 15 : (80 - x) / 15),
            lembap: x <= 70 ? 0 : (x >= 90 ? 1 : (x - 70) / 20)
        });
        const membershipAmonia = x => ({
            rendah: x <= 10 ? 1 : (x >= 18 ? 0 : (18 - x) / 8),
            sedang: (x <= 15 || x >= 30) ? 0 : (x < 22 ? (x - 15) / 7 : (30 - x) / 8),
            tinggi: x <= 25 ? 0 : (x >= 35 ? 1 : (x - 25) / 10)
        });
        
        // === FUZZY RULES (SUGENO - Order 0 and 1) ===
        const rules = [
            { name: "R1", conditions: (s, k, a) => s.dingin, label: "Mati",
              consequent0: 0, 
              consequent1: { p_s: 0, p_k: 0, p_a: 0, const: 0, str: "z = 0" } },
            { name: "R2", conditions: (s, k, a) => Math.min(s.hangat, k.kering), label: "Pelan",
              consequent0: 2500,
              consequent1: { p_s: 50, p_k: 10, p_a: 20, const: 500, str: "z = 50s + 10k + 20a + 500" } },
            { name: "R3", conditions: (s, k, a) => Math.min(s.hangat, k.ideal, a.rendah), label: "Pelan",
              consequent0: 2500,
              consequent1: { p_s: 50, p_k: 5, p_a: 10, const: 1000, str: "z = 50s + 5k + 10a + 1000" } },
            { name: "R4", conditions: (s, k, a) => Math.min(s.hangat, k.lembap), label: "Sedang",
              consequent0: 5000,
              consequent1: { p_s: 100, p_k: 20, p_a: 50, const: 0, str: "z = 100s + 20k + 50a" } },
            { name: "R5", conditions: (s, k, a) => a.sedang, label: "Sedang",
              consequent0: 5000,
              consequent1: { p_s: 0, p_k: 0, p_a: 150, const: 1000, str: "z = 150a + 1000" } },
            { name: "R6", conditions: (s, k, a) => Math.min(s.panas, k.kering), label: "Sedang",
              consequent0: 5000,
              consequent1: { p_s: 150, p_k: 10, p_a: 20, const: 500, str: "z = 150s + 10k + 20a + 500" } },
            { name: "R7", conditions: (s, k, a) => Math.min(s.panas, k.ideal), label: "Cepat",
              consequent0: 8000,
              consequent1: { p_s: 200, p_k: 10, p_a: 50, const: 1000, str: "z = 200s + 10k + 50a + 1000" } },
            { name: "R8", conditions: (s, k, a) => Math.min(s.panas, k.lembap), label: "Cepat",
              consequent0: 8000,
              consequent1: { p_s: 200, p_k: 20, p_a: 80, const: 1000, str: "z = 200s + 20k + 80a + 1000" } },
            { name: "R9", conditions: (s, k, a) => a.tinggi, label: "Cepat",
              consequent0: 8000,
              consequent1: { p_s: 0, p_k: 0, p_a: 250, const: 1500, str: "z = 250a + 1500" } }
        ];

        // === MAIN CALCULATION FUNCTION ===
        function updateFuzzyLogic() {
            const order = document.querySelector('input[name="sugeno-order"]:checked').value;
            const suhu = parseFloat(suhuSlider.value);
            const kelembapan = parseFloat(kelembapanSlider.value);
            const amonia = parseFloat(amoniaSlider.value);

            suhuValue.textContent = suhu.toFixed(1);
            kelembapanValue.textContent = kelembapan.toFixed(0);
            amoniaValue.textContent = amonia.toFixed(1);
            
            let html = '';

            const fuzzySuhu = membershipSuhu(suhu);
            const fuzzyKelembapan = membershipKelembapan(kelembapan);
            const fuzzyAmonia = membershipAmonia(amonia);
            
            html += `<div class="step"><h3>1. Fuzzifikasi Input</h3><p>Mengubah nilai input crisp menjadi derajat keanggotaan fuzzy.</p>`;
            html += `<ul><li>Suhu (${suhu.toFixed(1)}°C): Dingin=${fuzzySuhu.dingin.toFixed(3)}, Hangat=${fuzzySuhu.hangat.toFixed(3)}, Panas=${fuzzySuhu.panas.toFixed(3)}</li>`;
            html += `<li>Kelembapan (${kelembapan.toFixed(0)}%): Kering=${fuzzyKelembapan.kering.toFixed(3)}, Ideal=${fuzzyKelembapan.ideal.toFixed(3)}, Lembap=${fuzzyKelembapan.lembap.toFixed(3)}</li>`;
            html += `<li>Amonia (${amonia.toFixed(1)}ppm): Rendah=${fuzzyAmonia.rendah.toFixed(3)}, Sedang=${fuzzyAmonia.sedang.toFixed(3)}, Tinggi=${fuzzyAmonia.tinggi.toFixed(3)}</li></ul></div>`;

            const ruleOutputs = rules.map(rule => {
                const alpha = rule.conditions(fuzzySuhu, fuzzyKelembapan, fuzzyAmonia);
                let z;
                if (order === '0') {
                    z = rule.consequent0;
                } else {
                    const c = rule.consequent1;
                    z = c.p_s * suhu + c.p_k * kelembapan + c.p_a * amonia + c.const;
                }
                return { name: rule.name, alpha, z, label: rule.label, formula: order === '1' ? rule.consequent1.str : null };
            }).filter(r => r.alpha > 0);

            html += `<div class="step"><h3>2. Implikasi Aturan</h3><p>Menghitung kekuatan (α) dan output (z) untuk setiap aturan yang aktif.</p>`;
            html += `<table id="implication-table"><thead><tr><th>Aturan</th><th>Kekuatan (αᵢ)</th><th>Output (zᵢ)</th><th>αᵢ * zᵢ</th></tr></thead><tbody>`;

            let totalAlpha_x_Z = 0, totalAlpha = 0;
            ruleOutputs.forEach(r => {
                const alpha_x_Z = r.alpha * r.z;
                let z_display = ``;
                if(r.formula){
                     z_display = `<small>${r.formula}</small><br>= ${r.z.toFixed(0)} RPM`;
                } else {
                     z_display = `${r.z.toFixed(0)} RPM`;
                }

                html += `<tr><td>${r.name} (${r.label})</td><td>${r.alpha.toFixed(3)}</td><td>${z_display}</td><td>${alpha_x_Z.toFixed(2)}</td></tr>`;
                totalAlpha_x_Z += alpha_x_Z;
                totalAlpha += r.alpha;
            });
            if (ruleOutputs.length === 0) html += `<tr><td colspan="4">Tidak ada aturan yang aktif.</td></tr>`;
            html += `</tbody></table></div>`;
            
            const crispOutput = totalAlpha === 0 ? 0 : totalAlpha_x_Z / totalAlpha;
            html += `<div class="step"><h3>3. Defuzzifikasi (Weighted Average)</h3><p>Menghitung output crisp akhir.</p>`;
            html += `<p class="formula">z* = ( Σ(αᵢ * zᵢ) ) / ( Σ(αᵢ) )</p>`;
            html += `<div class="result-box"><ul>`;
            html += `<li>Total Pembilang (Σ αᵢ * zᵢ): <code>${totalAlpha_x_Z.toFixed(2)}</code></li>`;
            html += `<li>Total Penyebut (Σ αᵢ): <code>${totalAlpha.toFixed(3)}</code></li>`;
            html += `<li><b>Kecepatan Kipas (z*)</b> = <code>${totalAlpha_x_Z.toFixed(2)} / ${totalAlpha.toFixed(3)}</code> = <strong><code>${crispOutput.toFixed(0)} RPM</code></strong></li>`;
            html += `</ul></div></div>`;

            perhitunganDetailDiv.innerHTML = html;
            outputValue.textContent = crispOutput.toFixed(0);
            
            updateInputGraphs(suhu, kelembapan, amonia);
            updateSugenoVisGraph(ruleOutputs, crispOutput);
        }
        
        // === CHARTING FUNCTIONS ===
        function initializeCharts() {
            const chartOptions = {
                scales: { y: { beginAtZero: true, max: 1.1 }, x: { ticks: { autoSkip: true, maxTicksLimit: 8 } } },
                animation: { duration: 0 }, plugins: { legend: { display: true }, annotation: { annotations: {} } }
            };
            const plotData = (mf, r, s) => {
                const data = { labels: [], datasets: [] };
                const sets = Object.keys(mf(r[0]));
                for(let i = r[0]; i <= r[1]; i += (r[1]-r[0])/s) data.labels.push(i.toFixed(1));
                sets.forEach((set, idx) => {
                    const d = { label: set[0].toUpperCase() + set.slice(1), data: [], borderColor: CHART_COLORS[Object.keys(CHART_COLORS)[idx]], backgroundColor: CHART_COLORS[Object.keys(CHART_COLORS)[idx]].replace('0.6','0.2'), fill: true, tension: 0.1, pointRadius: 0 };
                    data.labels.forEach(l => d.data.push(mf(parseFloat(l))[set]));
                    data.datasets.push(d);
                });
                return data;
            };
            suhuChart = new Chart('suhuChart', { type: 'line', data: plotData(membershipSuhu, [15, 40], 100), options: {...chartOptions} });
            kelembapanChart = new Chart('kelembapanChart', { type: 'line', data: plotData(membershipKelembapan, [20, 100], 100), options: {...chartOptions} });
            amoniaChart = new Chart('amoniaChart', { type: 'line', data: plotData(membershipAmonia, [0, 40], 100), options: {...chartOptions} });
        
            sugenoVisChart = new Chart('sugenoVisChart', { type: 'bar',
                data: { datasets: [{ label: 'Kekuatan Aturan (α)', data: [], backgroundColor: CHART_COLORS.purple, barPercentage: 0.1 }] },
                options: {
                    scales: {
                        x: { type: 'linear', min: -500, max: 10500, title: { display: true, text: 'Kecepatan Kipas (RPM)'}},
                        y: { beginAtZero: true, max: 1.0, title: { display: true, text: 'Kekuatan Aturan (α)'}}
                    },
                    plugins: { legend: { display: false }, annotation: { annotations: {} } }
                }
            });
        }
        
        function updateInputGraphs(s, k, a) {
            const ann = (v, t) => ({ line1: { type: 'line', scaleID: 'x', value: v, borderColor: CHART_COLORS.inputLine, borderWidth: 2, label: { content: t, enabled: true, position: 'start', backgroundColor: 'rgba(255,255,255,0.7)', color: 'black' } } });
            suhuChart.options.plugins.annotation.annotations = ann(s, `${s.toFixed(1)}°C`);
            kelembapanChart.options.plugins.annotation.annotations = ann(k, `${k.toFixed(0)}%`);
            amoniaChart.options.plugins.annotation.annotations = ann(a, `${a.toFixed(1)}ppm`);
            suhuChart.update('none');
            kelembapanChart.update('none');
            amoniaChart.update('none');
        }

        function updateSugenoVisGraph(ruleOutputs, crispOutput) {
            sugenoVisChart.data.datasets[0].data = ruleOutputs.map(r => ({ x: r.z, y: r.alpha }));
            sugenoVisChart.options.plugins.annotation.annotations = {
                resultLine: {
                    type: 'line', scaleID: 'x', value: crispOutput, borderColor: CHART_COLORS.resultLine, borderWidth: 3, borderDash: [6, 6],
                    label: { enabled: true, content: `Hasil Akhir: ${crispOutput.toFixed(0)} RPM`, position: 'end', yAdjust: -10, backgroundColor: 'rgba(255,255,255,0.8)', color: '#166534' }
                }
            };
            sugenoVisChart.update('none');
        }

        // === EVENT LISTENERS ===
        [suhuSlider, kelembapanSlider, amoniaSlider].forEach(slider => slider.addEventListener('input', updateFuzzyLogic));
        orderRadios.forEach(radio => radio.addEventListener('change', updateFuzzyLogic));

        // === INITIALIZE ===
        window.onload = () => {
            initializeCharts();
            updateFuzzyLogic();
        };
    </script>
</body>
</html>
