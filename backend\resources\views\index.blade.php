<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SantriMental - Platform Kesehatan Mental</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://accounts.google.com/gsi/client" async defer></script>
    <script src="https://cdn.jsdelivr.net/npm/qrcode@1.5.3/build/qrcode.min.js"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Inter', sans-serif;
        }
        
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        
        .glass-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(25px);
            -webkit-backdrop-filter: blur(25px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .floating-animation {
            animation: float 6s ease-in-out infinite;
        }
        
        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-20px); }
        }
        
        .fade-in {
            animation: fadeIn 0.8s ease-out forwards;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(30px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        .slide-in-left {
            animation: slideInLeft 0.8s ease-out forwards;
        }
        
        @keyframes slideInLeft {
            from { opacity: 0; transform: translateX(-50px); }
            to { opacity: 1; transform: translateX(0); }
        }
        
        .slide-in-right {
            animation: slideInRight 0.8s ease-out forwards;
        }
        
        @keyframes slideInRight {
            from { opacity: 0; transform: translateX(50px); }
            to { opacity: 1; transform: translateX(0); }
        }
        
        .pulse-glow {
            animation: pulseGlow 2s ease-in-out infinite alternate;
        }
        
        @keyframes pulseGlow {
            from { box-shadow: 0 0 20px rgba(102, 126, 234, 0.4); }
            to { box-shadow: 0 0 40px rgba(102, 126, 234, 0.8); }
        }
        
        .tab-active {
            background: rgba(255, 255, 255, 0.2);
            border-bottom: 2px solid #a78bfa;
        }
        
        .input-focus:focus {
            border-color: #a78bfa;
            box-shadow: 0 0 0 3px rgba(167, 139, 250, 0.1);
        }
    </style>
</head>
<body class="gradient-bg min-h-screen flex items-center justify-center p-4">
    
    <!-- Background Decorations -->
    <div class="fixed inset-0 overflow-hidden pointer-events-none">
        <div class="absolute top-20 left-20 w-32 h-32 bg-white/10 rounded-full floating-animation"></div>
        <div class="absolute top-40 right-32 w-24 h-24 bg-purple-300/20 rounded-full floating-animation" style="animation-delay: -2s;"></div>
        <div class="absolute bottom-32 left-1/4 w-40 h-40 bg-blue-300/10 rounded-full floating-animation" style="animation-delay: -4s;"></div>
        <div class="absolute bottom-20 right-20 w-28 h-28 bg-pink-300/15 rounded-full floating-animation" style="animation-delay: -1s;"></div>
    </div>

    <main class="w-full max-w-6xl mx-auto grid lg:grid-cols-2 gap-8 items-center">
        
        <!-- Left Side - Branding & Info -->
        <section class="text-center lg:text-left slide-in-left">
            <div class="mb-8">
                <h1 class="text-4xl lg:text-6xl font-bold text-white mb-4">
                    Santri<span class="text-purple-300">Mental</span>
                </h1>
                <p class="text-xl text-purple-100 mb-6">
                    Platform Skrining Kesehatan Mental Modern
                </p>
                <p class="text-purple-200 text-lg leading-relaxed">
                    Lakukan penilaian kesehatan mental dengan mudah menggunakan instrumen SRQ-20 yang telah tervalidasi. 
                    Dapatkan hasil instan dan rekomendasi yang tepat untuk kesejahteraan mental Anda.
                </p>
            </div>
            
            <!-- Features -->
            <div class="grid grid-cols-1 sm:grid-cols-2 gap-4 mb-8">
                <div class="glass-card p-4 rounded-lg">
                    <div class="text-purple-300 text-2xl mb-2">🧠</div>
                    <h3 class="font-semibold text-white mb-1">Skrining Akurat</h3>
                    <p class="text-purple-200 text-sm">Menggunakan instrumen SRQ-20 yang tervalidasi</p>
                </div>
                <div class="glass-card p-4 rounded-lg">
                    <div class="text-purple-300 text-2xl mb-2">📊</div>
                    <h3 class="font-semibold text-white mb-1">Analisis Mendalam</h3>
                    <p class="text-purple-200 text-sm">Visualisasi hasil dan riwayat penilaian</p>
                </div>
                <div class="glass-card p-4 rounded-lg">
                    <div class="text-purple-300 text-2xl mb-2">🔒</div>
                    <h3 class="font-semibold text-white mb-1">Data Aman</h3>
                    <p class="text-purple-200 text-sm">Privasi dan keamanan data terjamin</p>
                </div>
                <div class="glass-card p-4 rounded-lg">
                    <div class="text-purple-300 text-2xl mb-2">📱</div>
                    <h3 class="font-semibold text-white mb-1">Responsif</h3>
                    <p class="text-purple-200 text-sm">Akses dari perangkat apapun</p>
                </div>
            </div>
        </section>

        <!-- Right Side - Authentication -->
        <section class="slide-in-right">
            <div class="glass-card rounded-2xl p-8 max-w-md mx-auto pulse-glow">
                
                <!-- Tab Navigation -->
                <div class="flex mb-6 bg-white/10 rounded-lg p-1">
                    <button id="login-tab" class="tab-active flex-1 py-2 px-4 text-white font-medium rounded-md transition-all duration-300">
                        Masuk
                    </button>
                    <button id="register-tab" class="flex-1 py-2 px-4 text-white/70 font-medium rounded-md transition-all duration-300">
                        Daftar
                    </button>
                </div>

                <!-- Login Form -->
                <div id="login-form" class="fade-in">
                    <h2 class="text-2xl font-bold text-white mb-6 text-center">Selamat Datang Kembali</h2>
                    
                    <form id="loginForm" class="space-y-4">
                        @csrf
                        <div>
                            <label class="block text-purple-200 text-sm font-medium mb-2">Email</label>
                            <input type="email" id="login-email" required 
                                   class="input-focus w-full px-4 py-3 bg-white/10 border border-white/30 rounded-lg text-white placeholder-purple-200 focus:outline-none transition-all duration-300"
                                   placeholder="<EMAIL>">
                        </div>
                        <div>
                            <label class="block text-purple-200 text-sm font-medium mb-2">Password</label>
                            <input type="password" id="login-password" required 
                                   class="input-focus w-full px-4 py-3 bg-white/10 border border-white/30 rounded-lg text-white placeholder-purple-200 focus:outline-none transition-all duration-300"
                                   placeholder="••••••••">
                        </div>
                        <div class="flex items-center justify-between">
                            <label class="flex items-center text-purple-200 text-sm">
                                <input type="checkbox" class="mr-2 rounded">
                                Ingat saya
                            </label>
                            <a href="#" class="text-purple-300 text-sm hover:text-purple-200 transition-colors">
                                Lupa password?
                            </a>
                        </div>
                        <button type="submit" 
                                class="w-full bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 text-white font-bold py-3 px-4 rounded-lg transition-all duration-300 transform hover:scale-105">
                            Masuk
                        </button>
                    </form>

                    <!-- Alternative Login Options -->
                    <div class="mt-6">
                        <div class="relative">
                            <div class="absolute inset-0 flex items-center">
                                <div class="w-full border-t border-white/30"></div>
                            </div>
                            <div class="relative flex justify-center text-sm">
                                <span class="px-2 bg-transparent text-purple-200">Atau masuk dengan</span>
                            </div>
                        </div>

                        <div class="mt-4 grid grid-cols-2 gap-3">
                            <button id="google-login" 
                                    class="flex items-center justify-center px-4 py-2 border border-white/30 rounded-lg text-white hover:bg-white/10 transition-all duration-300">
                                <svg class="w-5 h-5 mr-2" viewBox="0 0 24 24">
                                    <path fill="currentColor" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                                    <path fill="currentColor" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                                    <path fill="currentColor" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                                    <path fill="currentColor" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
                                </svg>
                                Google
                            </button>
                            <button id="qr-login" 
                                    class="flex items-center justify-center px-4 py-2 border border-white/30 rounded-lg text-white hover:bg-white/10 transition-all duration-300">
                                <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 24 24">
                                    <path d="M3 11h8V3H3v8zm2-6h4v4H5V5zM3 21h8v-8H3v8zm2-6h4v4H5v-4zM13 3v8h8V3h-8zm6 6h-4V5h4v4zM19 13h2v2h-2zM13 13h2v2h-2zM15 15h2v2h-2zM13 17h2v2h-2zM15 19h2v2h-2zM17 17h2v2h-2zM17 19h2v2h-2zM19 17h2v2h-2zM19 19h2v2h-2z"/>
                                </svg>
                                QR Code
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Register Form -->
                <div id="register-form" class="hidden">
                    <h2 class="text-2xl font-bold text-white mb-6 text-center">Buat Akun Baru</h2>
                    
                    <form id="registerForm" class="space-y-4">
                        @csrf
                        <div class="grid grid-cols-2 gap-4">
                            <div>
                                <label class="block text-purple-200 text-sm font-medium mb-2">Nama Depan</label>
                                <input type="text" id="register-firstname" required 
                                       class="input-focus w-full px-4 py-3 bg-white/10 border border-white/30 rounded-lg text-white placeholder-purple-200 focus:outline-none transition-all duration-300"
                                       placeholder="John">
                            </div>
                            <div>
                                <label class="block text-purple-200 text-sm font-medium mb-2">Nama Belakang</label>
                                <input type="text" id="register-lastname" required 
                                       class="input-focus w-full px-4 py-3 bg-white/10 border border-white/30 rounded-lg text-white placeholder-purple-200 focus:outline-none transition-all duration-300"
                                       placeholder="Doe">
                            </div>
                        </div>
                        <div>
                            <label class="block text-purple-200 text-sm font-medium mb-2">Email</label>
                            <input type="email" id="register-email" required 
                                   class="input-focus w-full px-4 py-3 bg-white/10 border border-white/30 rounded-lg text-white placeholder-purple-200 focus:outline-none transition-all duration-300"
                                   placeholder="<EMAIL>">
                        </div>
                        <div>
                            <label class="block text-purple-200 text-sm font-medium mb-2">Password</label>
                            <input type="password" id="register-password" required 
                                   class="input-focus w-full px-4 py-3 bg-white/10 border border-white/30 rounded-lg text-white placeholder-purple-200 focus:outline-none transition-all duration-300"
                                   placeholder="••••••••">
                        </div>
                        <div>
                            <label class="block text-purple-200 text-sm font-medium mb-2">Konfirmasi Password</label>
                            <input type="password" id="register-confirm-password" required 
                                   class="input-focus w-full px-4 py-3 bg-white/10 border border-white/30 rounded-lg text-white placeholder-purple-200 focus:outline-none transition-all duration-300"
                                   placeholder="••••••••">
                        </div>
                        <div class="flex items-center">
                            <input type="checkbox" id="terms" required class="mr-2 rounded">
                            <label for="terms" class="text-purple-200 text-sm">
                                Saya setuju dengan <a href="#" class="text-purple-300 hover:text-purple-200">Syarat & Ketentuan</a>
                            </label>
                        </div>
                        <button type="submit"
                                class="w-full bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 text-white font-bold py-3 px-4 rounded-lg transition-all duration-300 transform hover:scale-105">
                            Daftar
                        </button>
                    </form>
                </div>

                <!-- QR Code Modal -->
                <div id="qr-modal" class="hidden fixed inset-0 bg-black/50 flex items-center justify-center z-50">
                    <div class="glass-card p-6 rounded-2xl max-w-sm mx-4">
                        <h3 class="text-xl font-bold text-white mb-4 text-center">Login dengan QR Code</h3>
                        <div id="qr-code" class="bg-white p-4 rounded-lg mb-4"></div>
                        <p class="text-purple-200 text-sm text-center mb-4">
                            Scan QR code ini dengan aplikasi mobile SantriMental
                        </p>
                        <button id="close-qr" class="w-full bg-white/20 hover:bg-white/30 text-white py-2 rounded-lg transition-colors">
                            Tutup
                        </button>
                    </div>
                </div>

            </div>
        </section>

    </main>

    <!-- Loading Overlay -->
    <div id="loading-overlay" class="hidden fixed inset-0 bg-black/50 flex items-center justify-center z-50">
        <div class="glass-card p-6 rounded-2xl text-center">
            <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-white mx-auto mb-4"></div>
            <p class="text-white">Memproses...</p>
        </div>
    </div>

    <!-- Toast Notification -->
    <div id="toast" class="hidden fixed top-4 right-4 glass-card p-4 rounded-lg shadow-lg z-50 max-w-sm">
        <div class="flex items-center">
            <div id="toast-icon" class="mr-3"></div>
            <div>
                <p id="toast-title" class="font-semibold text-white"></p>
                <p id="toast-message" class="text-purple-200 text-sm"></p>
            </div>
        </div>
    </div>

    <script src="{{ asset('js/auth.js') }}"></script>
    <script src="{{ asset('js/utils.js') }}"></script>
    <script>
        // Tab switching functionality
        document.getElementById('login-tab').addEventListener('click', () => {
            document.getElementById('login-tab').classList.add('tab-active');
            document.getElementById('register-tab').classList.remove('tab-active');
            document.getElementById('login-form').classList.remove('hidden');
            document.getElementById('register-form').classList.add('hidden');
        });

        document.getElementById('register-tab').addEventListener('click', () => {
            document.getElementById('register-tab').classList.add('tab-active');
            document.getElementById('login-tab').classList.remove('tab-active');
            document.getElementById('register-form').classList.remove('hidden');
            document.getElementById('login-form').classList.add('hidden');
        });

        // QR Code functionality
        document.getElementById('qr-login').addEventListener('click', () => {
            const qrData = `santrimental://login/${Date.now()}`;
            QRCode.toCanvas(document.getElementById('qr-code'), qrData, {
                width: 200,
                margin: 2,
                color: {
                    dark: '#000000',
                    light: '#FFFFFF'
                }
            });
            document.getElementById('qr-modal').classList.remove('hidden');
        });

        document.getElementById('close-qr').addEventListener('click', () => {
            document.getElementById('qr-modal').classList.add('hidden');
        });

        // Check if user is already logged in
        if (localStorage.getItem('authToken')) {
            window.location.href = '{{ route('dashboard') }}';
        }
    </script>
</body>
</html>
