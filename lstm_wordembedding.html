
<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simulasi Word Embedding & LSTM Sederhana</title>
    <!-- Tailwind CSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Inter', sans-serif;
            background-color: #f0f4f8; /* Light gray background */
            color: #334155; /* Darker text */
        }
        .container {
            max-width: 1000px;
            margin: 2rem auto;
            padding: 1.5rem;
            background-color: #ffffff;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
        }
        h1, h2, h3 {
            color: #1e293b; /* Even darker for headings */
            font-weight: 700;
        }
        .section-card {
            background-color: #f8fafc;
            border-radius: 8px;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            border: 1px solid #e2e8f0;
        }
        .btn-primary {
            @apply bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-lg transition duration-300 ease-in-out shadow-md;
        }
        .btn-secondary {
            @apply bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-lg transition duration-300 ease-in-out shadow-md;
        }
        textarea {
            @apply w-full p-3 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500 transition duration-200 ease-in-out;
        }
        table {
            @apply w-full text-sm text-left text-gray-500 dark:text-gray-400;
        }
        table th, table td {
            @apply px-4 py-2 border border-gray-200 dark:border-gray-700;
        }
        table thead {
            @apply text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:text-gray-400;
        }
        .highlight-code {
            background-color: #e0f2fe; /* Light blue background for code */
            padding: 0.25rem 0.5rem;
            border-radius: 4px;
            font-family: monospace;
            color: #1e40af;
        }
        .math-eq {
            font-family: serif; /* For better rendering of mathematical symbols */
            font-style: italic;
        }
        .lstm-diagram {
            width: 100%;
            height: auto;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            display: block;
            margin: 1rem auto;
        }
        .tooltip {
            position: relative;
            display: inline-block;
            cursor: pointer;
            border-bottom: 1px dashed #9ca3af;
        }
        .tooltip .tooltiptext {
            visibility: hidden;
            width: 200px;
            background-color: #334155;
            color: #fff;
            text-align: center;
            border-radius: 6px;
            padding: 5px 10px;
            position: absolute;
            z-index: 1;
            bottom: 125%; /* Position above the text */
            left: 50%;
            margin-left: -100px;
            opacity: 0;
            transition: opacity 0.3s;
        }
        .tooltip .tooltiptext::after {
            content: "";
            position: absolute;
            top: 100%;
            left: 50%;
            margin-left: -5px;
            border-width: 5px;
            border-style: solid;
            border-color: #334155 transparent transparent transparent;
        }
        .tooltip:hover .tooltiptext {
            visibility: visible;
            opacity: 1;
        }
        /* Graph Styles */
        #sentimentGraph {
            width: 100%;
            height: 200px;
            background-color: #edf2f7;
            border-radius: 8px;
            position: relative;
            overflow: hidden;
        }
        #sentimentBar {
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 0; /* Will be animated */
            transition: height 1s ease-out, background-color 1s ease-out;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            font-size: 1.5rem;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
        }
        .sentiment-label {
            position: absolute;
            font-size: 0.9rem;
            color: #64748b;
        }
        .sentiment-label.positive {
            top: 10px;
            right: 10px;
        }
        .sentiment-label.negative {
            bottom: 10px;
            left: 10px;
        }
    </style>
</head>
<body class="bg-gray-100 text-gray-800 p-4">

    <div class="container">
        <h1 class="text-4xl text-center mb-6">Simulasi Word Embedding & LSTM Sederhana</h1>
        <p class="text-center text-lg mb-8 text-gray-600">Pelajari cara kerja Word Embedding dan LSTM dengan studi kasus sentimen analisis yang interaktif dan visual.</p>

        <!-- Bagian Input Teks -->
        <div class="section-card">
            <h2 class="text-2xl mb-4">Coba Sekarang!</h2>
            <p class="mb-4">Masukkan kalimat sederhana Anda (misal: "Film ini bagus sekali" atau "Makanan ini sangat buruk") untuk melihat simulasinya.</p>
            <textarea id="inputText" rows="3" placeholder="Ketik kalimat di sini..." class="mb-4"></textarea>
            <div class="flex space-x-4">
                <button id="processBtn" class="btn-primary">Proses Kalimat</button>
                <button id="resetBtn" class="btn-secondary">Reset</button>
            </div>
            <div id="outputMessage" class="mt-4 p-3 rounded-lg bg-blue-100 text-blue-800 hidden"></div>
        </div>

        <!-- Konsep Dasar -->
        <div class="section-card">
            <h2 class="text-2xl mb-4">Konsep Dasar</h2>
            <p class="mb-2"><strong>Word Embedding</strong> adalah teknik untuk merepresentasikan kata-kata sebagai vektor angka. Vektor ini menangkap makna dan hubungan semantik antar kata. Kata-kata dengan makna yang mirip akan memiliki vektor yang "dekat" dalam ruang vektor.</p>
            <p class="mb-4"><strong>Long Short-Term Memory (LSTM)</strong> adalah jenis khusus dari Recurrent Neural Network (RNN) yang mampu mempelajari dependensi jangka panjang. Ini mengatasi masalah "vanishing gradient" atau "exploding gradient" yang sering terjadi pada RNN tradisional.</p>
            <p class="mb-4">LSTM memiliki struktur sel khusus yang terdiri dari "gerbang" (gates) yang mengontrol aliran informasi:
                <ul class="list-disc pl-5">
                    <li><span class="font-semibold">Gerbang Lupa (Forget Gate)</span>: Menentukan informasi mana yang harus dibuang dari sel.</li>
                    <li><span class="font-semibold">Gerbang Input (Input Gate)</span>: Menentukan informasi baru apa yang akan disimpan di sel.</li>
                    <li><span class="font-semibold">Keadaan Sel (Cell State)</span>: Jalur utama di mana informasi mengalir.</li>
                    <li><span class="font-semibold">Gerbang Output (Output Gate)</span>: Menentukan nilai output berdasarkan keadaan sel.</li>
                </ul>
            </p>
            <img src="https://placehold.co/800x400/94a3b8/ffffff?text=Diagram+Konseptual+LSTM+Cell" alt="Diagram Konseptual LSTM Cell" class="lstm-diagram">
            <p class="text-sm text-gray-500 text-center mt-2">Diagram Konseptual Struktur Sel LSTM</p>
        </div>

        <!-- Dataset Sederhana -->
        <div class="section-card">
            <h2 class="text-2xl mb-4">Dataset Sederhana Kami</h2>
            <p class="mb-2">Ini adalah dataset sangat sederhana yang digunakan untuk simulasi. Setiap kata yang dikenali akan diubah menjadi vektor numerik dasar.</p>
            <table class="table-auto">
                <thead>
                    <tr>
                        <th>Kata</th>
                        <th>Vektor (Representasi Sederhana)</th>
                        <th>Sentimen</th>
                    </tr>
                </thead>
                <tbody id="datasetTableBody">
                    <!-- Data akan diisi oleh JavaScript -->
                </tbody>
            </table>
            <p class="text-sm text-gray-500 mt-2">Catatan: Untuk kata-kata di luar daftar ini, akan dianggap sebagai "Tidak Dikenal" dan diberi vektor nol.</p>
        </div>

        <!-- Pra-pemrosesan Teks -->
        <div class="section-card">
            <h2 class="text-2xl mb-4">Langkah 1: Pra-pemrosesan Teks (Tokenisasi)</h2>
            <p class="mb-2">Kalimat Anda akan dipecah menjadi unit-unit yang lebih kecil, yang disebut <strong>token</strong> (biasanya kata).</p>
            <div id="tokenizationOutput" class="p-3 bg-white border border-gray-200 rounded-lg hidden"></div>
            <img src="https://placehold.co/800x400/94a3b8/ffffff?text=Diagram+Tokenisasi+Teks" alt="Diagram Tokenisasi Teks" class="lstm-diagram">
            <p class="text-sm text-gray-500 text-center mt-2">Diagram Konseptual Proses Tokenisasi Teks</p>
        </div>

        <!-- Word Embedding Sederhana -->
        <div class="section-card">
            <h2 class="text-2xl mb-4">Langkah 2: Word Embedding Sederhana</h2>
            <p class="mb-2">Setiap token akan diubah menjadi vektor numerik berdasarkan "word embedding" sederhana yang telah kami definisikan.</p>
            <div id="embeddingOutput" class="p-3 bg-white border border-gray-200 rounded-lg hidden"></div>
            <img src="https://placehold.co/800x400/94a3b8/ffffff?text=Diagram+Representasi+Vektor+Kata" alt="Diagram Representasi Vektor Kata" class="lstm-diagram">
            <p class="text-sm text-gray-500 text-center mt-2">Diagram Konseptual Representasi Vektor Kata (Word Embedding)</p>
        </div>

        <!-- Simulasi LSTM - Perhitungan Manual -->
        <div class="section-card">
            <h2 class="text-2xl mb-4">Langkah 3: Simulasi LSTM - Perhitungan Manual (Satu Time Step)</h2>
            <p class="mb-2">Di sini, kami akan menunjukkan perhitungan manual untuk <strong>satu langkah waktu (time step)</strong> dalam LSTM, menggunakan vektor dari kata pertama kalimat Anda. Ini adalah versi yang sangat disederhanakan untuk tujuan pembelajaran.</p>
            <div id="lstmCalculationOutput" class="p-3 bg-white border border-gray-200 rounded-lg hidden">
                <h3 class="text-xl font-semibold mb-3">Input untuk Time Step Ini:</h3>
                <p class="mb-2">Vektor Kata ($x_t$): <span id="currentWordVector" class="highlight-code"></span></p>
                <p class="mb-4">State Tersembunyi Sebelumnya ($h_{t-1}$): <span id="prevHiddenState" class="highlight-code"></span></p>
                <p class="mb-4">Cell State Sebelumnya ($C_{t-1}$): <span id="prevCellState" class="highlight-code"></span></p>

                <h3 class="text-xl font-semibold mb-3">Bobot dan Bias (Contoh Sederhana):</h3>
                <p class="mb-2">Bobot Input-ke-Gerbang ($W_{gate}$): Kami menggunakan bobot sangat sederhana, misalnya, matriks 2x2 atau vektor 1x2 untuk ilustrasi. Bobot ini disetel secara manual untuk demonstrasi.</p>
                <p class="mb-2">Bias ($b_{gate}$): Nilai bias sederhana.</p>
                <p class="text-sm text-gray-600 mt-2">Catatan: Dalam LSTM sungguhan, bobot dan bias adalah matriks yang kompleks dan dipelajari selama pelatihan.</p>

                <h3 class="text-xl font-semibold mb-3">Perhitungan Gerbang Lupa ($f_t$ - Forget Gate):</h3>
                <p class="mb-2">Gerbang lupa menentukan berapa banyak informasi dari cell state sebelumnya ($C_{t-1}$) yang harus "dilupakan".</p>
                <p class="math-eq mb-2">$$f_t = \sigma(W_f x_t + U_f h_{t-1} + b_f)$$</p>
                <p id="forgetGateCalc" class="p-2 bg-gray-50 rounded-md"></p>

                <h3 class="text-xl font-semibold mb-3 mt-4">Perhitungan Gerbang Input ($i_t$ - Input Gate) dan Kandidat Cell State ($\tilde{C}_t$):</h3>
                <p class="mb-2">Gerbang input menentukan informasi baru mana yang akan disimpan. Kandidat cell state adalah nilai baru potensial yang ingin kita tambahkan ke cell state.</p>
                <p class="math-eq mb-2">$$i_t = \sigma(W_i x_t + U_i h_{t-1} + b_i)$$</p>
                <p class="math-eq mb-2">$$\tilde{C}_t = \tanh(W_C x_t + U_C h_{t-1} + b_C)$$</p>
                <p id="inputGateCalc" class="p-2 bg-gray-50 rounded-md"></p>
                <p id="candidateCellCalc" class="p-2 bg-gray-50 rounded-md"></p>

                <h3 class="text-xl font-semibold mb-3 mt-4">Perhitungan Cell State Baru ($C_t$ - New Cell State):</h3>
                <p class="mb-2">Cell state baru adalah kombinasi dari informasi yang dilupakan dari cell state sebelumnya dan informasi baru yang ditambahkan.</p>
                <p class="math-eq mb-2">$$C_t = f_t \odot C_{t-1} + i_t \odot \tilde{C}_t$$</p>
                <p id="newCellStateCalc" class="p-2 bg-gray-50 rounded-md"></p>

                <h3 class="text-xl font-semibold mb-3 mt-4">Perhitungan Gerbang Output ($o_t$ - Output Gate) dan Hidden State Baru ($h_t$ - New Hidden State):</h3>
                <p class="mb-2">Gerbang output menentukan bagian mana dari cell state yang akan dioutputkan sebagai hidden state baru.</p>
                <p class="math-eq mb-2">$$o_t = \sigma(W_o x_t + U_o h_{t-1} + b_o)$$</p>
                <p class="math-eq mb-2">$$h_t = o_t \odot \tanh(C_t)$$</p>
                <p id="outputGateCalc" class="p-2 bg-gray-50 rounded-md"></p>
                <p id="newHiddenStateCalc" class="p-2 bg-gray-50 rounded-md"></p>

                <p class="mt-4">Hasil Akhir untuk Time Step Ini:</p>
                <p>Hidden State Baru ($h_t$): <span id="finalHiddenState" class="highlight-code"></span></p>
                <p>Cell State Baru ($C_t$): <span id="finalCellState" class="highlight-code"></span></p>
            </div>
            <img src="https://placehold.co/800x400/94a3b8/ffffff?text=Flowchart+Perhitungan+LSTM+Time+Step" alt="Flowchart Perhitungan LSTM Time Step" class="lstm-diagram">
            <p class="text-sm text-gray-500 text-center mt-2">Flowchart Konseptual Perhitungan Satu Time Step LSTM</p>
        </div>

        <!-- Grafik Visualisasi Hasil -->
        <div class="section-card">
            <h2 class="text-2xl mb-4">Langkah 4: Visualisasi Hasil</h2>
            <p class="mb-2">Berdasarkan hasil pemrosesan LSTM (disimulasikan), kami akan menampilkan sentimen prediksi.</p>
            <div id="sentimentGraph" class="relative">
                <div id="sentimentBar"></div>
                <div class="sentiment-label positive">Positif</div>
                <div class="sentiment-label negative">Negatif</div>
            </div>
            <p id="predictedSentimentText" class="text-center text-xl font-bold mt-4"></p>
        </div>
    </div>

    <script type="module">
        // Fungsi untuk mengonversi nilai ke format LaTeX
        function toLaTeX(value) {
            if (Array.isArray(value)) {
                return `\\begin{bmatrix}${value.map(v => v.toFixed(3)).join(' \\\\ ')}\\end{bmatrix}`;
            }
            return value.toFixed(3);
        }

        // --- Data Sederhana ---
        const wordEmbeddings = {
            "ini": [0.1, 0.2],
            "film": [0.3, 0.1],
            "makanan": [0.2, 0.3],
            "bagus": [0.8, 0.9],
            "sekali": [0.1, 0.1],
            "sangat": [0.1, 0.1],
            "buruk": [0.9, 0.8],
            "enak": [0.7, 0.8],
            "tidak": [0.0, -0.5],
            "jelek": [0.9, 0.7],
            "luar": [0.2, 0.2],
            "biasa": [0.3, 0.3],
            "adalah": [0.05, 0.05],
            "baik": [0.75, 0.85],
            "unk": [0.0, 0.0] // Unknown word
        };

        const datasetSentiments = [
            { word: "ini", vector: "[0.1, 0.2]", sentiment: "Netral" },
            { word: "film", vector: "[0.3, 0.1]", sentiment: "Netral" },
            { word: "makanan", vector: "[0.2, 0.3]", sentiment: "Netral" },
            { word: "bagus", vector: "[0.8, 0.9]", sentiment: "Positif" },
            { word: "sekali", vector: "[0.1, 0.1]", sentiment: "Penguat" },
            { word: "sangat", vector: "[0.1, 0.1]", sentiment: "Penguat" },
            { word: "buruk", vector: "[0.9, 0.8]", sentiment: "Negatif" },
            { word: "enak", vector: "[0.7, 0.8]", sentiment: "Positif" },
            { word: "tidak", vector: "[0.0, -0.5]", sentiment: "Negasi" },
            { word: "jelek", vector: "[0.9, 0.7]", sentiment: "Negatif" },
            { word: "luar", vector: "[0.2, 0.2]", sentiment: "Netral" },
            { word: "biasa", vector: "[0.3, 0.3]", sentiment: "Positif" },
            { word: "adalah", vector: "[0.05, 0.05]", sentiment: "Netral" },
            { word: "baik", vector: "[0.75, 0.85]", sentiment: "Positif" },
            { word: "unk", vector: "[0.0, 0.0]", sentiment: "Tidak Dikenal" }
        ];

        // --- Elemen UI ---
        const inputText = document.getElementById('inputText');
        const processBtn = document.getElementById('processBtn');
        const resetBtn = document.getElementById('resetBtn');
        const outputMessage = document.getElementById('outputMessage');
        const datasetTableBody = document.getElementById('datasetTableBody');
        const tokenizationOutput = document.getElementById('tokenizationOutput');
        const embeddingOutput = document.getElementById('embeddingOutput');
        const lstmCalculationOutput = document.getElementById('lstmCalculationOutput');
        const currentWordVectorSpan = document.getElementById('currentWordVector');
        const prevHiddenStateSpan = document.getElementById('prevHiddenState');
        const prevCellStateSpan = document.getElementById('prevCellState');
        const forgetGateCalcDiv = document.getElementById('forgetGateCalc');
        const inputGateCalcDiv = document.getElementById('inputGateCalc');
        const candidateCellCalcDiv = document.getElementById('candidateCellCalc');
        const newCellStateCalcDiv = document.getElementById('newCellStateCalc');
        const outputGateCalcDiv = document.getElementById('outputGateCalc');
        const newHiddenStateCalcDiv = document.getElementById('newHiddenStateCalc');
        const finalHiddenStateSpan = document.getElementById('finalHiddenState');
        const finalCellStateSpan = document.getElementById('finalCellState');
        const sentimentBar = document.getElementById('sentimentBar');
        const predictedSentimentText = document.getElementById('predictedSentimentText');

        // --- Fungsi Matematika Sederhana ---
        const sigmoid = (x) => 1 / (1 + Math.exp(-x));
        const tanh = (x) => Math.tanh(x);

        // Dot product of two vectors
        const dot = (vec1, vec2) => {
            return vec1.reduce((sum, val, i) => sum + val * vec2[i], 0);
        };

        // Vector addition
        const add = (vec1, vec2) => vec1.map((val, i) => val + vec2[i]);

        // Scalar multiplication
        const multiplyScalar = (vec, scalar) => vec.map(val => val * scalar);

        // Element-wise product (Hadamard product)
        const hadamard = (vec1, vec2) => vec1.map((val, i) => val * vec2[i]);

        // Simple matrix-vector multiplication (for 1xN vector * NxM matrix, result is 1xM)
        // Here, we simplify W and U as vectors for simplicity, making dot product appropriate.
        const matMul = (vector, matrix) => {
            // Assuming vector is a row vector [1xN] and matrix is [NxM]
            // For this simple simulation, we'll treat W_f, U_f etc. as vectors of the same dimension as x_t and h_t-1
            // and perform dot product for simplicity.
            // In a real LSTM, W would be (input_dim, hidden_dim) and U would be (hidden_dim, hidden_dim).
            // For a 2D vector, W might be a 2x2 matrix, and U a 2x2 matrix.
            // For simplicity, we'll assume W and U are effectively applied as a dot product for a single output value.
            // This is a MAJOR simplification for manual calculation demo.
            return dot(vector, matrix); // If matrix is also a vector
        };

        // --- LSTM Bobot & Bias Sederhana (Manual untuk Ilustrasi) ---
        // Dimensi: input_dim = 2 (karena embedding 2D), hidden_dim = 2
        // Kita akan menggunakan vektor sederhana untuk bobot dan bias agar perhitungan mudah.
        // Dalam LSTM sungguhan, ini adalah matriks!
        const weights = {
            Wf: [0.5, 0.3], Uf: [0.2, 0.4], bf: 0.1,
            Wi: [0.4, 0.6], Ui: [0.3, 0.2], bi: 0.2,
            Wc: [0.6, 0.2], Uc: [0.1, 0.5], bc: 0.0,
            Wo: [0.3, 0.5], Uo: [0.4, 0.1], bo: 0.15
        };

        // Initial hidden state and cell state (biasanya vektor nol)
        let hiddenState = [0.0, 0.0];
        let cellState = [0.0, 0.0];

        // --- Inisialisasi UI ---
        function initializeUI() {
            // Isi tabel dataset
            datasetTableBody.innerHTML = datasetSentiments.map(data => `
                <tr>
                    <td>${data.word}</td>
                    <td>${data.vector}</td>
                    <td>${data.sentiment}</td>
                </tr>
            `).join('');

            resetApp();
        }

        function resetApp() {
            inputText.value = '';
            outputMessage.classList.add('hidden');
            tokenizationOutput.classList.add('hidden');
            embeddingOutput.classList.add('hidden');
            lstmCalculationOutput.classList.add('hidden');
            sentimentBar.style.height = '0';
            sentimentBar.style.backgroundColor = '#ccc';
            sentimentBar.textContent = 'Netral';
            predictedSentimentText.textContent = '';

            // Reset LSTM states
            hiddenState = [0.0, 0.0];
            cellState = [0.0, 0.0];
        }

        // --- Event Listeners ---
        processBtn.addEventListener('click', () => {
            const text = inputText.value.trim();
            if (!text) {
                outputMessage.textContent = "Mohon masukkan kalimat.";
                outputMessage.classList.remove('hidden', 'bg-blue-100', 'text-blue-800', 'bg-red-100', 'text-red-800');
                outputMessage.classList.add('bg-red-100', 'text-red-800');
                resetApp(); // Clear previous outputs but keep message
                return;
            }
            outputMessage.classList.add('hidden'); // Hide info message if successful
            processText(text);
        });

        resetBtn.addEventListener('click', resetApp);

        // --- Fungsi Utama Proses ---
        function processText(text) {
            // 1. Tokenisasi
            const tokens = text.toLowerCase().split(/\s+/).filter(t => t.length > 0);
            tokenizationOutput.innerHTML = `<p class="mb-2"><strong>Kalimat Asli:</strong> "${text}"</p><p><strong>Tokens:</strong> ${tokens.map(t => `<span class="highlight-code">${t}</span>`).join(', ')}</p>`;
            tokenizationOutput.classList.remove('hidden');

            // 2. Word Embedding
            let embeddingsHtml = '<h3 class="text-lg font-semibold mb-2">Vektor Kata:</h3>';
            const embeddedTokens = tokens.map(token => {
                const vector = wordEmbeddings[token] || wordEmbeddings['unk'];
                embeddingsHtml += `<p><span class="highlight-code">${token}</span>: ${JSON.stringify(vector)}</p>`;
                return vector;
            });
            embeddingOutput.innerHTML = embeddingsHtml;
            embeddingOutput.classList.remove('hidden');

            // 3. Simulasi LSTM (hanya time step pertama untuk demonstrasi manual)
            if (embeddedTokens.length > 0) {
                const firstWordVector = embeddedTokens[0];
                simulateLSTMSingleStep(firstWordVector, hiddenState, cellState);
                lstmCalculationOutput.classList.remove('hidden');
            } else {
                lstmCalculationOutput.classList.add('hidden');
            }

            // 4. Prediksi Sentimen (Sangat Disederhanakan)
            // Ini adalah bagian yang sangat disederhanakan. Dalam aplikasi nyata,
            // hiddenState terakhir dari urutan akan dilewatkan ke layer Dense/Output
            // untuk menghasilkan probabilitas sentimen.
            // Untuk demo, kita akan menjumlahkan nilai di hiddenState terakhir
            // dan menggunakannya sebagai skor sentimen.
            let sentimentScore = hiddenState[0] - hiddenState[1]; // simplified score
            if (tokens.includes("bagus") || tokens.includes("enak") || tokens.includes("baik") || tokens.includes("luar") && tokens.includes("biasa")) {
                sentimentScore += 0.5; // Manual boost for positive words
            }
            if (tokens.includes("buruk") || tokens.includes("jelek") || tokens.includes("tidak")) {
                sentimentScore -= 0.5; // Manual drop for negative words
            }

            // Normalize score to -1 to 1 range (for visualization)
            let normalizedScore = Math.min(1, Math.max(-1, sentimentScore)); // Clamp between -1 and 1
            updateSentimentGraph(normalizedScore);
        }

        // --- Fungsi Simulasi LSTM Satu Time Step ---
        function simulateLSTMSingleStep(xt, ht_prev, Ct_prev) {
            currentWordVectorSpan.textContent = JSON.stringify(xt);
            prevHiddenStateSpan.textContent = JSON.stringify(ht_prev);
            prevCellStateSpan.textContent = JSON.stringify(Ct_prev);

            // Calculation for each gate (simplified for 2D vectors and manual demo)
            // In a real LSTM, this involves matrix multiplications.
            // Here, we treat W and U as vectors and perform dot products for simplicity.

            // Forget Gate
            // f_t = sigma(Wf * xt + Uf * ht_prev + bf)
            const f_t_input = dot(weights.Wf, xt) + dot(weights.Uf, ht_prev) + weights.bf;
            const f_t = sigmoid(f_t_input);
            forgetGateCalcDiv.innerHTML = `
                <p>$$W_f x_t = ${toLaTeX(weights.Wf)} \\cdot ${toLaTeX(xt)} = ${dot(weights.Wf, xt).toFixed(3)}$$</p>
                <p>$$U_f h_{t-1} = ${toLaTeX(weights.Uf)} \\cdot ${toLaTeX(ht_prev)} = ${dot(weights.Uf, ht_prev).toFixed(3)}$$</p>
                <p>$$f_t_{input} = (${dot(weights.Wf, xt).toFixed(3)}) + (${dot(weights.Uf, ht_prev).toFixed(3)}) + (${weights.bf.toFixed(3)}) = ${f_t_input.toFixed(3)}$$</p>
                <p>$$f_t = \\sigma(${f_t_input.toFixed(3)}) = ${f_t.toFixed(3)}$$</p>
            `;

            // Input Gate
            // i_t = sigma(Wi * xt + Ui * ht_prev + bi)
            const i_t_input = dot(weights.Wi, xt) + dot(weights.Ui, ht_prev) + weights.bi;
            const i_t = sigmoid(i_t_input);
            inputGateCalcDiv.innerHTML = `
                <p>$$W_i x_t = ${toLaTeX(weights.Wi)} \\cdot ${toLaTeX(xt)} = ${dot(weights.Wi, xt).toFixed(3)}$$</p>
                <p>$$U_i h_{t-1} = ${toLaTeX(weights.Ui)} \\cdot ${toLaTeX(ht_prev)} = ${dot(weights.Ui, ht_prev).toFixed(3)}$$</p>
                <p>$$i_t_{input} = (${dot(weights.Wi, xt).toFixed(3)}) + (${dot(weights.Ui, ht_prev).toFixed(3)}) + (${weights.bi.toFixed(3)}) = ${i_t_input.toFixed(3)}$$</p>
                <p>$$i_t = \\sigma(${i_t_input.toFixed(3)}) = ${i_t.toFixed(3)}$$</p>
            `;

            // Candidate Cell State
            // C_tilde_t = tanh(Wc * xt + Uc * ht_prev + bc)
            const C_tilde_t_input = dot(weights.Wc, xt) + dot(weights.Uc, ht_prev) + weights.bc;
            const C_tilde_t = tanh(C_tilde_t_input);
            candidateCellCalcDiv.innerHTML = `
                <p>$$W_C x_t = ${toLaTeX(weights.Wc)} \\cdot ${toLaTeX(xt)} = ${dot(weights.Wc, xt).toFixed(3)}$$</p>
                <p>$$U_C h_{t-1} = ${toLaTeX(weights.Uc)} \\cdot ${toLaTeX(ht_prev)} = ${dot(weights.Uc, ht_prev).toFixed(3)}$$</p>
                <p>$$\\tilde{C}_t_{input} = (${dot(weights.Wc, xt).toFixed(3)}) + (${dot(weights.Uc, ht_prev).toFixed(3)}) + (${weights.bc.toFixed(3)}) = ${C_tilde_t_input.toFixed(3)}$$</p>
                <p>$$\\tilde{C}_t = \\tanh(${C_tilde_t_input.toFixed(3)}) = ${C_tilde_t.toFixed(3)}$$</p>
            `;

            // New Cell State
            // C_t = f_t * C_prev + i_t * C_tilde_t (Hadamard product applied element-wise)
            // For this 2D simplification, f_t, i_t, C_tilde_t are scalars.
            // Realistically, f_t, i_t, C_tilde_t are vectors of the hidden_dim size.
            // Let's adjust to assume scalar output from gates for simpler demo,
            // then scale Ct_prev and C_tilde_t by these scalars.
            // This is a further simplification for visual manual calculation.
            // In actual LSTM, it's element-wise multiplication of vectors.
            const new_Ct_val1 = f_t * Ct_prev[0] + i_t * C_tilde_t; // Simplified
            const new_Ct_val2 = f_t * Ct_prev[1] + i_t * C_tilde_t; // Simplified
            const new_Ct = [new_Ct_val1, new_Ct_val2]; // Simplified output vector
            cellState = new_Ct; // Update global cell state for next step if needed

            newCellStateCalcDiv.innerHTML = `
                <p>$$f_t \\odot C_{t-1} = ${f_t.toFixed(3)} \\odot ${toLaTeX(Ct_prev)} = ${toLaTeX(multiplyScalar(Ct_prev, f_t))}$$</p>
                <p>$$i_t \\odot \\tilde{C}_t = ${i_t.toFixed(3)} \\odot ${C_tilde_t.toFixed(3)} = ${toLaTeX(multiplyScalar(Array(Ct_prev.length).fill(1), i_t * C_tilde_t))}$$ (Sederhana, sebenarnya vektor)</p>
                <p>$$C_t = ${toLaTeX(multiplyScalar(Ct_prev, f_t))} + ${toLaTeX(multiplyScalar(Array(Ct_prev.length).fill(1), i_t * C_tilde_t))} = ${toLaTeX(new_Ct)}$$</p>
            `;

            // Output Gate
            // o_t = sigma(Wo * xt + Uo * ht_prev + bo)
            const o_t_input = dot(weights.Wo, xt) + dot(weights.Uo, ht_prev) + weights.bo;
            const o_t = sigmoid(o_t_input);
            outputGateCalcDiv.innerHTML = `
                <p>$$W_o x_t = ${toLaTeX(weights.Wo)} \\cdot ${toLaTeX(xt)} = ${dot(weights.Wo, xt).toFixed(3)}$$</p>
                <p>$$U_o h_{t-1} = ${toLaTeX(weights.Uo)} \\cdot ${toLaTeX(ht_prev)} = ${dot(weights.Uo, ht_prev).toFixed(3)}$$</p>
                <p>$$o_t_{input} = (${dot(weights.Wo, xt).toFixed(3)}) + (${dot(weights.Uo, ht_prev).toFixed(3)}) + (${weights.bo.toFixed(3)}) = ${o_t_input.toFixed(3)}$$</p>
                <p>$$o_t = \\sigma(${o_t_input.toFixed(3)}) = ${o_t.toFixed(3)}$$</p>
            `;

            // New Hidden State
            // h_t = o_t * tanh(C_t) (Hadamard product applied element-wise)
            // Again, simplifying o_t to scalar here for demo.
            const new_ht_val1 = o_t * tanh(new_Ct[0]); // Simplified
            const new_ht_val2 = o_t * tanh(new_Ct[1]); // Simplified
            const new_ht = [new_ht_val1, new_ht_val2]; // Simplified output vector
            hiddenState = new_ht; // Update global hidden state

            newHiddenStateCalcDiv.innerHTML = `
                <p>$$\\tanh(C_t) = \\tanh(${toLaTeX(new_Ct)}) = ${toLaTeX(new_Ct.map(v => tanh(v)))} $$</p>
                <p>$$h_t = o_t \\odot \\tanh(C_t) = ${o_t.toFixed(3)} \\odot ${toLaTeX(new_Ct.map(v => tanh(v)))} = ${toLaTeX(new_ht)}$$</p>
            `;

            finalHiddenStateSpan.textContent = JSON.stringify(hiddenState.map(v => v.toFixed(3)));
            finalCellStateSpan.textContent = JSON.stringify(cellState.map(v => v.toFixed(3)));

            // Render math equations after content is updated
            if (typeof MathJax !== 'undefined') {
                MathJax.typesetPromise();
            }
        }

        // --- Fungsi Visualisasi Grafik ---
        function updateSentimentGraph(score) {
            // Score ranges from -1 (negative) to 1 (positive)
            // Map to percentage height for the bar (0% to 100%)
            // 0 -> 50% (neutral), 1 -> 100% (positive), -1 -> 0% (negative)

            let barHeight = ((score + 1) / 2) * 100;
            let sentimentText = '';
            let barColor = '';

            if (score > 0.2) {
                sentimentText = 'Positif';
                barColor = 'bg-green-500';
            } else if (score < -0.2) {
                sentimentText = 'Negatif';
                barColor = 'bg-red-500';
            } else {
                sentimentText = 'Netral';
                barColor = 'bg-gray-400';
            }

            sentimentBar.style.height = `${barHeight}%`;
            sentimentBar.className = `absolute bottom-0 left-0 w-full transition-all duration-1000 ease-in-out flex items-center justify-center text-white font-bold text-2xl rounded-b-lg ${barColor}`;
            sentimentBar.textContent = sentimentText;
            predictedSentimentText.textContent = `Sentimen Prediksi: ${sentimentText}`;
        }

        // --- Initialize on load ---
        window.onload = () => {
            initializeUI();
            // Load MathJax for LaTeX rendering
            const script = document.createElement('script');
            script.src = 'https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js';
            script.async = true;
            document.head.appendChild(script);
        };
    </script>
</body>
</html>
