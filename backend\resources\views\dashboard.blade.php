<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>Dashboard - SantriMental</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Inter', sans-serif;
        }
        
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        
        .glass-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(25px);
            -webkit-backdrop-filter: blur(25px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .sidebar-active {
            background: rgba(255, 255, 255, 0.2);
            border-right: 3px solid #a78bfa;
        }
        
        .fade-in {
            animation: fadeIn 0.6s ease-out forwards;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        .slide-in {
            animation: slideIn 0.5s ease-out forwards;
        }
        
        @keyframes slideIn {
            from { opacity: 0; transform: translateX(-20px); }
            to { opacity: 1; transform: translateX(0); }
        }
        
        .pulse-glow {
            animation: pulseGlow 2s ease-in-out infinite alternate;
        }
        
        @keyframes pulseGlow {
            from { box-shadow: 0 0 20px rgba(102, 126, 234, 0.4); }
            to { box-shadow: 0 0 40px rgba(102, 126, 234, 0.8); }
        }
        
        .stat-card:hover {
            transform: translateY(-5px);
            transition: all 0.3s ease;
        }
        
        .mobile-menu-open {
            transform: translateX(0);
        }
        
        .mobile-menu-closed {
            transform: translateX(-100%);
        }
    </style>
</head>
<body class="gradient-bg min-h-screen">
    
    <!-- Mobile Menu Overlay -->
    <div id="mobile-overlay" class="fixed inset-0 bg-black/50 z-40 lg:hidden hidden"></div>
    
    <!-- Sidebar -->
    <aside id="sidebar" class="fixed left-0 top-0 h-full w-64 glass-card z-50 transform mobile-menu-closed lg:mobile-menu-open transition-transform duration-300">
        <div class="p-6">
            <!-- Logo -->
            <div class="flex items-center mb-8">
                <div class="w-10 h-10 bg-gradient-to-r from-purple-500 to-pink-500 rounded-lg flex items-center justify-center mr-3">
                    <span class="text-white font-bold text-xl">S</span>
                </div>
                <h1 class="text-xl font-bold text-white">SantriMental</h1>
            </div>
            
            <!-- Navigation -->
            <nav class="space-y-2">
                <a href="#" data-page="dashboard" class="sidebar-link sidebar-active flex items-center px-4 py-3 text-white rounded-lg transition-all duration-300">
                    <svg class="w-5 h-5 mr-3" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"/>
                    </svg>
                    Dashboard
                </a>
                
                <a href="#" data-page="assessment" class="sidebar-link flex items-center px-4 py-3 text-white/70 hover:text-white hover:bg-white/10 rounded-lg transition-all duration-300">
                    <svg class="w-5 h-5 mr-3" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                    </svg>
                    Skrining SRQ-20
                </a>
                
                <a href="{{ route('history') }}" class="sidebar-link flex items-center px-4 py-3 text-white/70 hover:text-white hover:bg-white/10 rounded-lg transition-all duration-300">
                    <svg class="w-5 h-5 mr-3" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M9 2a1 1 0 000 2h2a1 1 0 100-2H9z"/>
                        <path fill-rule="evenodd" d="M4 5a2 2 0 012-2v1a1 1 0 102 0V3h4v1a1 1 0 102 0V3a2 2 0 012 2v6a2 2 0 01-2 2H6a2 2 0 01-2-2V5zm3 4a1 1 0 000 2h.01a1 1 0 100-2H7zm3 0a1 1 0 000 2h3a1 1 0 100-2h-3z" clip-rule="evenodd"/>
                    </svg>
                    Riwayat
                </a>
                
                <a href="#" data-page="profile" class="sidebar-link flex items-center px-4 py-3 text-white/70 hover:text-white hover:bg-white/10 rounded-lg transition-all duration-300">
                    <svg class="w-5 h-5 mr-3" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd"/>
                    </svg>
                    Profil
                </a>
            </nav>
            
            <!-- User Info -->
            <div class="absolute bottom-6 left-6 right-6">
                <div class="glass-card p-4 rounded-lg">
                    <div class="flex items-center">
                        <div id="user-avatar" class="w-10 h-10 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center mr-3">
                            <span id="user-initials" class="text-white font-semibold text-sm">DU</span>
                        </div>
                        <div class="flex-1 min-w-0">
                            <p id="user-name" class="text-white font-medium text-sm truncate">Demo User</p>
                            <p id="user-email" class="text-purple-200 text-xs truncate"><EMAIL></p>
                        </div>
                        <button id="logout-btn" class="text-white/70 hover:text-white transition-colors">
                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M3 3a1 1 0 00-1 1v12a1 1 0 102 0V4a1 1 0 00-1-1zm10.293 9.293a1 1 0 001.414 1.414l3-3a1 1 0 000-1.414l-3-3a1 1 0 10-1.414 1.414L14.586 9H7a1 1 0 100 2h7.586l-1.293 1.293z" clip-rule="evenodd"/>
                            </svg>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </aside>

    <!-- Main Content -->
    <main class="lg:ml-64 min-h-screen">
        <!-- Header -->
        <header class="glass-card p-4 lg:p-6">
            <div class="flex items-center justify-between">
                <div class="flex items-center">
                    <button id="mobile-menu-btn" class="lg:hidden text-white mr-4">
                        <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M3 5a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM3 10a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM3 15a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clip-rule="evenodd"/>
                        </svg>
                    </button>
                    <div>
                        <h1 id="page-title" class="text-2xl font-bold text-white">Dashboard</h1>
                        <p id="page-subtitle" class="text-purple-200">Selamat datang kembali! Berikut ringkasan kesehatan mental Anda.</p>
                    </div>
                </div>
                
                <div class="flex items-center space-x-4">
                    <button class="text-white/70 hover:text-white transition-colors">
                        <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M10 2a6 6 0 00-6 6v3.586l-.707.707A1 1 0 004 14h12a1 1 0 00.707-1.707L16 11.586V8a6 6 0 00-6-6zM10 18a3 3 0 01-3-3h6a3 3 0 01-3 3z"/>
                        </svg>
                    </button>
                </div>
            </div>
        </header>

        <!-- Page Content -->
        <div class="p-4 lg:p-6">
            
            <!-- Dashboard Content -->
            <div id="dashboard-content" class="page-content">
                
                <!-- Stats Cards -->
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                    <div class="stat-card glass-card p-6 rounded-xl">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-purple-200 text-sm font-medium">Total Skrining</p>
                                <p id="total-assessments" class="text-3xl font-bold text-white">15</p>
                            </div>
                            <div class="w-12 h-12 bg-blue-500/20 rounded-lg flex items-center justify-center">
                                <svg class="w-6 h-6 text-blue-400" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                </svg>
                            </div>
                        </div>
                    </div>
                    
                    <div class="stat-card glass-card p-6 rounded-xl">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-purple-200 text-sm font-medium">Skor Terakhir</p>
                                <p id="latest-score" class="text-3xl font-bold text-white">4</p>
                            </div>
                            <div class="w-12 h-12 bg-green-500/20 rounded-lg flex items-center justify-center">
                                <svg class="w-6 h-6 text-green-400" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                                </svg>
                            </div>
                        </div>
                    </div>
                    
                    <div class="stat-card glass-card p-6 rounded-xl">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-purple-200 text-sm font-medium">Status</p>
                                <p id="latest-status" class="text-xl font-semibold text-green-400">Normal</p>
                            </div>
                            <div class="w-12 h-12 bg-purple-500/20 rounded-lg flex items-center justify-center">
                                <svg class="w-6 h-6 text-purple-400" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M3 6a3 3 0 013-3h10a1 1 0 01.8 1.6L14.25 8l2.55 3.4A1 1 0 0116 13H6a1 1 0 00-1 1v3a1 1 0 11-2 0V6z" clip-rule="evenodd"/>
                                </svg>
                            </div>
                        </div>
                    </div>
                    
                    <div class="stat-card glass-card p-6 rounded-xl">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-purple-200 text-sm font-medium">Bulan Ini</p>
                                <p id="monthly-count" class="text-3xl font-bold text-white">5</p>
                            </div>
                            <div class="w-12 h-12 bg-yellow-500/20 rounded-lg flex items-center justify-center">
                                <svg class="w-6 h-6 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clip-rule="evenodd"/>
                                </svg>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Charts Section -->
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
                    <!-- Score Trend Chart -->
                    <div class="glass-card rounded-xl p-6 fade-in" style="animation-delay: 0.4s;">
                        <h4 class="text-lg font-semibold text-white mb-4">Tren Skor SRQ-20</h4>
                        <div class="h-64">
                            <canvas id="score-trend-chart"></canvas>
                        </div>
                    </div>

                    <!-- Monthly Distribution -->
                    <div class="glass-card rounded-xl p-6 fade-in" style="animation-delay: 0.5s;">
                        <h4 class="text-lg font-semibold text-white mb-4">Distribusi Bulanan</h4>
                        <div class="h-64">
                            <canvas id="monthly-chart"></canvas>
                        </div>
                    </div>
                </div>

                <!-- Recent Activity -->
                <div class="glass-card rounded-xl p-6 fade-in" style="animation-delay: 0.6s;">
                    <h4 class="text-lg font-semibold text-white mb-4">Aktivitas Terbaru</h4>
                    <div id="recent-activity" class="space-y-4">
                        <!-- Activity items will be inserted here -->
                    </div>
                </div>

            </div>

            <!-- Other pages will be loaded here -->
            <div id="assessment-page" class="page-content hidden">
                <div class="glass-card rounded-xl p-6 text-center">
                    <h3 class="text-xl font-bold text-white mb-4">Tes SRQ-20</h3>
                    <p class="text-purple-200 mb-6">Halaman tes akan dimuat di sini</p>
                    <button onclick="window.location.href='{{ route('srq20-form') }}'" class="bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 text-white font-bold py-3 px-6 rounded-lg transition-all duration-300">
                        Mulai Tes
                    </button>
                </div>
            </div>

            <div id="history-page" class="page-content hidden">
                <div class="glass-card rounded-xl p-6">
                    <h3 class="text-xl font-bold text-white mb-4">Riwayat Tes</h3>
                    <p class="text-purple-200">Riwayat tes Anda akan ditampilkan di sini</p>
                </div>
            </div>

            <div id="analytics-page" class="page-content hidden">
                <div class="glass-card rounded-xl p-6">
                    <h3 class="text-xl font-bold text-white mb-4">Analitik</h3>
                    <p class="text-purple-200">Analitik mendalam akan ditampilkan di sini</p>
                </div>
            </div>

            <div id="profile-page" class="page-content hidden">
                <div class="glass-card rounded-xl p-6">
                    <h3 class="text-xl font-bold text-white mb-4">Profil Pengguna</h3>
                    <p class="text-purple-200">Pengaturan profil akan ditampilkan di sini</p>
                </div>
            </div>

        </div>
    </main>

    <script src="{{ asset('js/auth.js') }}"></script>
    <script src="{{ asset('js/utils.js') }}"></script>
    <script src="{{ asset('js/dashboard.js') }}"></script>
</body>
</html>
