<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Visualisasi Insertion Sort</title>
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@400;700&display=swap" rel="stylesheet">
    <style>
        /* Pengaturan Dasar dan Font */
        body {
            font-family: 'Roboto', sans-serif;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: #333;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            padding: 20px;
            box-sizing: border-box;
        }

        /* Kontainer Utama */
        .container {
            background-color: rgba(255, 255, 255, 0.95);
            padding: 30px 40px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            width: 95%;
            max-width: 900px;
            text-align: center;
        }

        header h1 {
            color: #2c3e50;
            margin-bottom: 5px;
        }

        header p {
            color: #7f8c8d;
            margin-bottom: 25px;
        }

        /* Tombol Kontrol */
        .controls button {
            background-color: #3498db;
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            margin: 0 10px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 6px rgba(52, 152, 219, 0.2);
        }

        .controls button:hover {
            background-color: #2980b9;
            transform: translateY(-2px);
            box-shadow: 0 6px 8px rgba(41, 128, 185, 0.3);
        }

        .controls button:disabled {
            background-color: #bdc3c7;
            cursor: not-allowed;
            transform: translateY(0);
            box-shadow: none;
        }

        /* Kontainer Balok Visualisasi */
        #bar-container {
            margin-top: 40px;
            display: flex;
            justify-content: center;
            align-items: flex-end;
            height: 320px;
            border-bottom: 3px solid #ecf0f1;
            padding-bottom: 10px;
        }

        .bar {
            width: 30px;
            margin: 0 3px;
            color: white;
            text-align: center;
            font-size: 14px;
            font-weight: bold;
            border-radius: 5px 5px 0 0;
            display: flex;
            justify-content: center;
            padding-top: 10px;
            /* Transisi untuk animasi yang mulus */
            transition: height 0.4s ease, background-color 0.4s ease, transform 0.4s ease;
        }

        /* Status Balok */
        .bar-default {
            background: linear-gradient(to top, #3498db, #5dade2);
        }
        .bar-key {
            background: linear-gradient(to top, #f1c40f, #f39c12);
            transform: translateY(-25px); /* Angkat balok kunci */
        }
        .bar-comparing {
            background: linear-gradient(to top, #e74c3c, #c0392b);
        }
        .bar-sorted {
            background: linear-gradient(to top, #2ecc71, #27ae60);
        }

        /* Panel Penjelasan Kode */
        .explanation-panel {
            display: flex;
            margin-top: 40px;
            gap: 20px;
            text-align: left;
        }

        .code-display, .explanation-text {
            background-color: #ecf0f1;
            padding: 20px;
            border-radius: 8px;
            flex: 1;
        }

        .code-display h2, .explanation-text h3 {
            margin-top: 0;
            color: #2c3e50;
            border-bottom: 2px solid #bdc3c7;
            padding-bottom: 10px;
            margin-bottom: 15px;
        }

        pre {
            background-color: #2c3e50;
            color: #ecf0f1;
            padding: 15px;
            border-radius: 5px;
            white-space: pre-wrap;
            word-wrap: break-word;
            font-size: 15px;
            line-height: 1.6;
        }

        #code-snippet .highlight {
            background-color: #f39c12;
            color: #2c3e50;
            padding: 2px 0;
            border-radius: 3px;
            display: block;
        }

        #explanation-p {
            font-size: 16px;
            line-height: 1.7;
            color: #34495e;
        }
    </style>
</head>
<body>

    <div class="container">
        <header>
            <h1>Visualisasi Algoritma Insertion Sort</h1>
            <p>Lihat bagaimana algoritma Insertion Sort bekerja langkah demi langkah.</p>
        </header>
        
        <div class="controls">
            <button id="generate-array-btn">Buat Array Baru</button>
            <button id="sort-btn">Mulai Urutkan!</button>
        </div>

        <div id="bar-container">
            <!-- Balok-balok array akan digenerate oleh JavaScript di sini -->
        </div>

        <div class="explanation-panel">
            <div class="code-display">
                <h2>Langkah Algoritma</h2>
                <pre><code id="code-snippet"></code></pre>
            </div>
            <div class="explanation-text">
                <h3>Penjelasan</h3>
                <p id="explanation-p">Klik "Buat Array Baru" untuk memulai.</p>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', () => {
            // Mengambil elemen-elemen dari DOM
            const generateArrayBtn = document.getElementById('generate-array-btn');
            const sortBtn = document.getElementById('sort-btn');
            const barContainer = document.getElementById('bar-container');
            const codeSnippetEl = document.getElementById('code-snippet');
            const explanationPEl = document.getElementById('explanation-p');

            let array = [];
            const animationSpeed = 500; // Kecepatan animasi dalam milidetik

            // Kode dasar algoritma untuk ditampilkan
            const codeLines = [
                "for (let i = 1; i < n; i++) {",
                "  let key = array[i];",
                "  let j = i - 1;",
                "  while (j >= 0 && array[j] > key) {",
                "    array[j + 1] = array[j];",
                "    j--;",
                "  }",
                "  array[j + 1] = key;",
                "}"
            ];

            // Fungsi untuk membuat array baru
            const generateArray = () => {
                array = [];
                for (let i = 0; i < 12; i++) {
                    array.push(Math.floor(Math.random() * 90) + 10);
                }
                renderBars();
                updateUI("Klik 'Mulai Urutkan' untuk melihat simulasi.", -1);
                sortBtn.disabled = false;
            };

            // Fungsi untuk merender balok berdasarkan array saat ini
            const renderBars = (indices = {}) => {
                barContainer.innerHTML = '';
                array.forEach((value, i) => {
                    const bar = document.createElement('div');
                    bar.classList.add('bar');
                    bar.style.height = `${value * 3}px`;
                    
                    // Menentukan warna balok berdasarkan statusnya
                    if (indices.key === i) {
                        bar.classList.add('bar-key');
                    } else if (indices.comparing === i) {
                        bar.classList.add('bar-comparing');
                    } else if (indices.sorted && i < indices.sorted) {
                        bar.classList.add('bar-sorted');
                    } else {
                        bar.classList.add('bar-default');
                    }
                    
                    const text = document.createElement('span');
                    text.innerText = value;
                    bar.appendChild(text);
                    barContainer.appendChild(bar);
                });
            };

            // Fungsi untuk memperbarui panel kode dan penjelasan
            const updateUI = (explanation, highlightLine) => {
                explanationPEl.innerText = explanation;
                codeSnippetEl.innerHTML = codeLines.map((line, index) => 
                    index === highlightLine 
                    ? `<span class="highlight">${line}</span>` 
                    : line
                ).join('\n');
            };

            // Fungsi jeda untuk animasi
            const sleep = (ms) => new Promise(resolve => setTimeout(resolve, ms));

            // Implementasi Algoritma Insertion Sort dengan visualisasi
            const insertionSort = async () => {
                sortBtn.disabled = true;
                generateArrayBtn.disabled = true;
                
                const n = array.length;
                let sortedUntil = 1;

                for (let i = 1; i < n; i++) {
                    updateUI(`Memulai iterasi baru. Bagian terurut ada di sebelah kiri.`, 0);
                    renderBars({ sorted: sortedUntil });
                    await sleep(animationSpeed);

                    let key = array[i];
                    let j = i - 1;

                    updateUI(`Pilih elemen pertama dari bagian yang belum terurut sebagai 'kunci'. Kunci = ${key}.`, 1);
                    renderBars({ key: i, sorted: sortedUntil });
                    await sleep(animationSpeed * 1.5);

                    updateUI(`Bandingkan 'kunci' (${key}) dengan elemen di bagian terurut.`, 3);
                    await sleep(animationSpeed);

                    while (j >= 0 && array[j] > key) {
                        updateUI(`Karena ${array[j]} > ${key}, geser ${array[j]} ke kanan.`, 4);
                        renderBars({ key: i, comparing: j, sorted: sortedUntil });
                        await sleep(animationSpeed);

                        array[j + 1] = array[j];
                        renderBars({ key: i, sorted: sortedUntil });
                        await sleep(animationSpeed);
                        j--;
                    }
                    
                    updateUI(`Menemukan posisi yang tepat. Sisipkan 'kunci' (${key}) di sana.`, 7);
                    array[j + 1] = key;
                    sortedUntil++;
                    renderBars({ sorted: sortedUntil });
                    await sleep(animationSpeed * 1.5);
                }

                updateUI("Selesai! Array telah berhasil diurutkan.", -1);
                renderBars({ sorted: n + 1 }); // Warnai semua balok sebagai terurut
                generateArrayBtn.disabled = false;
            };

            // Event listener untuk tombol
            generateArrayBtn.addEventListener('click', generateArray);
            sortBtn.addEventListener('click', insertionSort);

            // Inisialisasi aplikasi saat halaman dimuat
            generateArray();
        });
    </script>
</body>
</html>
