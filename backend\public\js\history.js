/**
 * History Page Logic
 */
document.addEventListener('DOMContentLoaded', () => {
    const auth = window.auth;
    const Utils = window.Utils;

    // Check authentication
    if (!auth.isAuthenticated()) {
        window.location.href = '/';
        return;
    }

    // Elements
    const loadingState = document.getElementById('loading-state');
    const emptyState = document.getElementById('empty-state');
    const historyList = document.getElementById('history-list');
    const pagination = document.getElementById('pagination');
    const filterStatus = document.getElementById('filter-status');
    const filterPeriod = document.getElementById('filter-period');
    
    // Statistics elements
    const totalCountEl = document.getElementById('total-count');
    const avgScoreEl = document.getElementById('avg-score');
    const latestScoreEl = document.getElementById('latest-score');
    const thisMonthEl = document.getElementById('this-month');

    let currentPage = 1;
    let currentFilters = {
        status: '',
        period: ''
    };

    // Load history data
    async function loadHistory() {
        try {
            showLoading();
            
            let historyData;
            
            if (Utils.MOCK_MODE) {
                // Load from localStorage for demo
                const savedResults = JSON.parse(localStorage.getItem('srq20_results') || '[]');
                historyData = generateMockHistoryData(savedResults);
            } else {
                // Real API call
                const params = new URLSearchParams({
                    page: currentPage,
                    ...currentFilters
                });
                historyData = await Utils.apiCall(`/assessments?${params}`);
            }

            updateStatistics(historyData.statistics);
            displayHistory(historyData.data);
            updatePagination(historyData.pagination);

        } catch (error) {
            console.error('Failed to load history:', error);
            Utils.showNotification('Gagal memuat riwayat', 'error');
            showEmpty();
        }
    }

    // Generate mock history data
    function generateMockHistoryData(savedResults) {
        // Apply filters
        let filteredResults = [...savedResults];
        
        if (currentFilters.status) {
            filteredResults = filteredResults.filter(r => r.status === currentFilters.status);
        }
        
        if (currentFilters.period) {
            const now = new Date();
            const filterDate = new Date();
            
            switch (currentFilters.period) {
                case 'week':
                    filterDate.setDate(now.getDate() - 7);
                    break;
                case 'month':
                    filterDate.setMonth(now.getMonth() - 1);
                    break;
                case '3months':
                    filterDate.setMonth(now.getMonth() - 3);
                    break;
            }
            
            filteredResults = filteredResults.filter(r => new Date(r.created_at) >= filterDate);
        }

        // Sort by date (newest first)
        filteredResults.sort((a, b) => new Date(b.created_at) - new Date(a.created_at));

        // Calculate statistics
        const currentMonth = new Date().getMonth();
        const currentYear = new Date().getFullYear();
        const thisMonthResults = savedResults.filter(r => {
            const date = new Date(r.created_at);
            return date.getMonth() === currentMonth && date.getFullYear() === currentYear;
        });

        const statistics = {
            total: savedResults.length,
            average_score: savedResults.length > 0 
                ? (savedResults.reduce((sum, r) => sum + r.score, 0) / savedResults.length).toFixed(1)
                : 0,
            latest_score: savedResults.length > 0 ? savedResults[savedResults.length - 1].score : null,
            this_month: thisMonthResults.length
        };

        // Pagination (simple mock)
        const itemsPerPage = 10;
        const startIndex = (currentPage - 1) * itemsPerPage;
        const endIndex = startIndex + itemsPerPage;
        const paginatedResults = filteredResults.slice(startIndex, endIndex);

        return {
            data: paginatedResults,
            statistics: statistics,
            pagination: {
                current_page: currentPage,
                total_pages: Math.ceil(filteredResults.length / itemsPerPage),
                total_items: filteredResults.length,
                per_page: itemsPerPage
            }
        };
    }

    // Update statistics
    function updateStatistics(stats) {
        totalCountEl.textContent = stats.total || 0;
        avgScoreEl.textContent = stats.average_score || '0';
        latestScoreEl.textContent = stats.latest_score || '-';
        thisMonthEl.textContent = stats.this_month || 0;
    }

    // Display history items
    function displayHistory(items) {
        if (items.length === 0) {
            showEmpty();
            return;
        }

        const historyHTML = items.map(item => {
            const date = new Date(item.created_at);
            const formattedDate = Utils.formatDate(date);
            const status = getStatusInfo(item.status);
            
            return `
                <div class="history-card glass-card p-6 rounded-xl">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-4">
                            <div class="w-12 h-12 ${status.bgColor} rounded-lg flex items-center justify-center">
                                ${status.icon}
                            </div>
                            <div>
                                <h3 class="font-semibold text-white">Skrining SRQ-20</h3>
                                <p class="text-purple-200 text-sm">${formattedDate}</p>
                            </div>
                        </div>
                        
                        <div class="text-right">
                            <div class="flex items-center space-x-4">
                                <div class="text-center">
                                    <p class="text-2xl font-bold text-white">${item.score}</p>
                                    <p class="text-purple-200 text-xs">Skor</p>
                                </div>
                                <div class="text-center">
                                    <p class="font-semibold ${status.textColor}">${status.text}</p>
                                    <p class="text-purple-200 text-xs">Status</p>
                                </div>
                                <button onclick="viewDetails('${item.id || Date.now()}')" class="bg-white/10 hover:bg-white/20 text-white px-4 py-2 rounded-lg transition-colors">
                                    Detail
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }).join('');

        historyList.innerHTML = historyHTML;
        showHistory();
    }

    // Get status information
    function getStatusInfo(status) {
        const statusMap = {
            'normal': {
                text: 'Normal',
                textColor: 'text-green-400',
                bgColor: 'bg-green-500/20',
                icon: `<svg class="w-6 h-6 text-green-400" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                </svg>`
            },
            'concern': {
                text: 'Perlu Perhatian',
                textColor: 'text-yellow-400',
                bgColor: 'bg-yellow-500/20',
                icon: `<svg class="w-6 h-6 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"/>
                </svg>`
            },
            'high_risk': {
                text: 'Risiko Tinggi',
                textColor: 'text-red-400',
                bgColor: 'bg-red-500/20',
                icon: `<svg class="w-6 h-6 text-red-400" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"/>
                </svg>`
            }
        };

        return statusMap[status] || statusMap['normal'];
    }

    // Update pagination
    function updatePagination(paginationData) {
        if (paginationData.total_pages <= 1) {
            pagination.classList.add('hidden');
            return;
        }

        let paginationHTML = '';
        
        // Previous button
        if (paginationData.current_page > 1) {
            paginationHTML += `
                <button onclick="changePage(${paginationData.current_page - 1})" class="bg-white/10 hover:bg-white/20 text-white px-3 py-2 rounded-lg transition-colors">
                    ‹ Prev
                </button>
            `;
        }

        // Page numbers
        for (let i = 1; i <= paginationData.total_pages; i++) {
            const isActive = i === paginationData.current_page;
            paginationHTML += `
                <button onclick="changePage(${i})" class="${isActive ? 'bg-purple-500' : 'bg-white/10 hover:bg-white/20'} text-white px-3 py-2 rounded-lg transition-colors">
                    ${i}
                </button>
            `;
        }

        // Next button
        if (paginationData.current_page < paginationData.total_pages) {
            paginationHTML += `
                <button onclick="changePage(${paginationData.current_page + 1})" class="bg-white/10 hover:bg-white/20 text-white px-3 py-2 rounded-lg transition-colors">
                    Next ›
                </button>
            `;
        }

        pagination.innerHTML = paginationHTML;
        pagination.classList.remove('hidden');
    }

    // Show/hide states
    function showLoading() {
        loadingState.classList.remove('hidden');
        emptyState.classList.add('hidden');
        historyList.classList.add('hidden');
        pagination.classList.add('hidden');
    }

    function showEmpty() {
        loadingState.classList.add('hidden');
        emptyState.classList.remove('hidden');
        historyList.classList.add('hidden');
        pagination.classList.add('hidden');
    }

    function showHistory() {
        loadingState.classList.add('hidden');
        emptyState.classList.add('hidden');
        historyList.classList.remove('hidden');
    }

    // Event listeners
    filterStatus.addEventListener('change', (e) => {
        currentFilters.status = e.target.value;
        currentPage = 1;
        loadHistory();
    });

    filterPeriod.addEventListener('change', (e) => {
        currentFilters.period = e.target.value;
        currentPage = 1;
        loadHistory();
    });

    // Global functions
    window.changePage = (page) => {
        currentPage = page;
        loadHistory();
    };

    window.viewDetails = (id) => {
        Utils.showNotification('Detail skrining akan ditampilkan', 'info');
        // TODO: Implement detail view
    };

    // Initialize
    loadHistory();
});
