<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simulasi <PERSON>: <PERSON><PERSON><PERSON></title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chartjs-plugin-annotation@1.4.0"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;700&family=Roboto+Mono:wght@400;500&display=swap" rel="stylesheet">
    <style>
        /* General Styling */
        body {
            font-family: 'Inter', sans-serif;
            background-color: #f0f4f8;
            color: #334155;
            margin: 0;
            padding: 1rem;
            display: flex;
            flex-direction: column;
            align-items: center;
        }

        /* Main Container */
        .container {
            width: 100%;
            max-width: 1200px;
            background: #ffffff;
            border-radius: 1rem;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
            padding: 2rem;
            box-sizing: border-box;
        }

        h1, h2, h3, h4 {
            color: #1e293b;
            margin-bottom: 1rem;
        }
        
        h1 {
            font-size: 2rem;
            font-weight: 700;
            text-align: center;
        }

        h2 {
            font-size: 1.5rem;
            margin-top: 2rem;
            padding-bottom: 0.5rem;
            border-bottom: 2px solid #e2e8f0;
            text-align: center;
        }

        h3 {
             text-align: left;
             font-size: 1.2rem;
             color: #475569;
        }
        
        h4 {
            font-size: 1.1rem;
            color: #334155;
            margin-top: 1.5rem;
            margin-bottom: 0.5rem;
            padding-left: 10px;
            border-left: 3px solid #3b82f6;
        }

        /* Layout */
        .main-layout {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 2rem;
        }

        @media (max-width: 900px) {
            .main-layout {
                grid-template-columns: 1fr;
            }
        }
        
        .main-layout > div {
            padding: 1.5rem;
            background-color: #f8fafc;
            border-radius: 0.75rem;
            border: 1px solid #e2e8f0;
        }

        /* Input Controls */
        .input-controls .control-group {
            margin-bottom: 1.5rem;
        }

        .input-controls label {
            display: block;
            font-weight: 500;
            margin-bottom: 0.5rem;
            color: #475569;
        }

        .input-controls input[type="range"] {
            width: 100%;
            cursor: pointer;
            -webkit-appearance: none;
            height: 8px;
            background: #e2e8f0;
            border-radius: 5px;
            outline: none;
            transition: background 0.2s;
        }
        .input-controls input[type="range"]::-webkit-slider-thumb {
            -webkit-appearance: none;
            appearance: none;
            width: 20px;
            height: 20px;
            background: #3b82f6;
            border-radius: 50%;
            border: 2px solid #ffffff;
            cursor: pointer;
            box-shadow: 0 2px 5px rgba(0,0,0,0.2);
        }
        .input-controls input[type="range"]::-moz-range-thumb {
            width: 20px;
            height: 20px;
            background: #3b82f6;
            border-radius: 50%;
            border: 2px solid #ffffff;
            cursor: pointer;
            box-shadow: 0 2px 5px rgba(0,0,0,0.2);
        }

        .input-controls .value-display {
            font-weight: 700;
            color: #3b82f6;
            font-size: 1.1rem;
        }

        /* Output Display */
        .output-display {
            text-align: center;
            padding: 2rem;
        }

        .output-display .label {
            font-size: 1.2rem;
            color: #64748b;
            margin-bottom: 0.5rem;
        }

        .output-display .value {
            font-size: 3.5rem;
            font-weight: 700;
            color: #1e293b;
            line-height: 1;
        }

        .output-display .unit {
            font-size: 1.5rem;
            color: #475569;
            margin-left: 0.5rem;
        }

        /* Calculation Steps */
        .calculation-steps {
            margin-top: 2rem;
            background-color: #ffffff;
            padding: 1.5rem;
            border-radius: 0.75rem;
            border: 1px solid #e2e8f0;
        }

        .calculation-steps .step {
            margin-bottom: 1.5rem;
        }
        
        .calculation-steps code {
            font-family: 'Roboto Mono', monospace;
            background-color: #e2e8f0;
            padding: 0.2rem 0.4rem;
            border-radius: 4px;
            font-size: 0.95em;
        }
        
        .calculation-steps ul {
            list-style-type: none;
            padding-left: 0;
        }
        
        .calculation-steps li {
            font-family: 'Roboto Mono', monospace;
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            padding: 0.75rem;
            margin-bottom: 0.5rem;
            border-radius: 0.5rem;
            font-size: 0.9em;
        }
        
        .calculation-steps .formula {
            font-family: 'Roboto Mono', monospace;
            background-color: #f1f5f9;
            padding: 1rem;
            border-radius: 0.5rem;
            border-left: 4px solid #3b82f6;
            overflow-x: auto;
            white-space: pre-wrap;
            margin-top: 1rem;
        }

        /* Graph Sections */
        .graphs-container {
            display: grid;
            grid-template-columns: 1fr;
            gap: 1.5rem;
            margin-top: 2rem;
        }
        
        .graph-wrapper {
             background-color: #f8fafc;
             padding: 1.5rem;
             border-radius: 0.75rem;
             border: 1px solid #e2e8f0;
        }
        
        .graph-wrapper h3 {
            text-align: center;
            margin-top: 0;
        }

    </style>
</head>
<body>

    <div class="container">
        <h1>Simulasi Logika Fuzzy Mamdani</h1>
        <p style="text-align:center; max-width: 700px; margin: 0 auto 2rem auto;">Kontrol Kecepatan Kipas Angin untuk Greenhouse/Peternakan Ayam</p>

        <div class="main-layout">
            <!-- Input and Output Section -->
            <div class="input-output-panel">
                <div class="input-controls">
                    <h3>Parameter Input</h3>
                    <!-- Suhu -->
                    <div class="control-group">
                        <label for="suhu">Suhu: <span id="suhu-value" class="value-display">25</span> °C</label>
                        <input type="range" id="suhu" min="15" max="40" value="25" step="0.1">
                    </div>
                    <!-- Kelembapan -->
                    <div class="control-group">
                        <label for="kelembapan">Kelembapan: <span id="kelembapan-value" class="value-display">60</span> %</label>
                        <input type="range" id="kelembapan" min="20" max="100" value="60" step="1">
                    </div>
                    <!-- Amonia -->
                    <div class="control-group">
                        <label for="amonia">Kadar Amonia: <span id="amonia-value" class="value-display">10</span> ppm</label>
                        <input type="range" id="amonia" min="0" max="40" value="10" step="0.5">
                    </div>
                </div>
                
                <div class="output-display">
                    <div class="label">Rekomendasi Kecepatan Kipas</div>
                    <div>
                        <span id="output-value" class="value">0</span><span class="unit">RPM</span>
                    </div>
                </div>
            </div>
            
             <!-- Graphs of Membership Functions -->
            <div class="membership-graphs">
                <h3 style="text-align:center;">Grafik Fungsi Keanggotaan</h3>
                 <div class="graphs-container">
                    <div class="graph-wrapper">
                         <h3>Suhu (°C)</h3>
                         <canvas id="suhuChart"></canvas>
                    </div>
                     <div class="graph-wrapper">
                         <h3>Kelembapan (%)</h3>
                         <canvas id="kelembapanChart"></canvas>
                     </div>
                     <div class="graph-wrapper">
                         <h3>Amonia (ppm)</h3>
                         <canvas id="amoniaChart"></canvas>
                     </div>
                 </div>
            </div>
        </div>

        <!-- Calculation Display -->
        <div class="calculation-steps">
            <h2>Perhitungan Manual (Step-by-Step)</h2>
            <div id="perhitungan-detail">
                <!-- Content will be generated by JavaScript -->
            </div>
        </div>
        
        <!-- Defuzzification Graph -->
        <div class="defuzzification-graph" style="margin-top: 2rem;">
            <h2>Visualisasi Proses Defuzzifikasi</h2>
            <div class="graph-wrapper">
                <h3>Komposisi Aturan & Hasil Centroid</h3>
                <canvas id="defuzzifikasiChart"></canvas>
            </div>
        </div>

    </div>

    <script>
        // === DOM ELEMENTS ===
        const suhuSlider = document.getElementById('suhu');
        const kelembapanSlider = document.getElementById('kelembapan');
        const amoniaSlider = document.getElementById('amonia');
        const suhuValue = document.getElementById('suhu-value');
        const kelembapanValue = document.getElementById('kelembapan-value');
        const amoniaValue = document.getElementById('amonia-value');
        const outputValue = document.getElementById('output-value');
        const perhitunganDetailDiv = document.getElementById('perhitungan-detail');

        // === CHART.JS SETUP ===
        let suhuChart, kelembapanChart, amoniaChart, defuzzifikasiChart;
        const CHART_COLORS = {
            blue: 'rgba(59, 130, 246, 0.6)',
            green: 'rgba(16, 185, 129, 0.6)',
            yellow: 'rgba(234, 179, 8, 0.6)',
            red: 'rgba(239, 68, 68, 0.6)',
            purple: 'rgba(139, 92, 246, 0.6)',
            gray: 'rgba(203, 213, 225, 0.5)',
            inputLine: 'rgba(239, 68, 68, 1)',
            centroidLine: 'rgba(16, 185, 129, 1)'
        };

        // === FUZZY MEMBERSHIP FUNCTIONS ===

        // Fungsi Keanggotaan SUHU
        const membershipSuhu = (x) => {
            const dingin = x <= 20 ? 1 : (x >= 25 ? 0 : (25 - x) / (25 - 20));
            const hangat = (x <= 20 || x >= 32) ? 0 : (x < 26 ? (x - 20) / (26 - 20) : (32 - x) / (32 - 26));
            const panas = x <= 28 ? 0 : (x >= 34 ? 1 : (x - 28) / (34 - 28));
            return { dingin, hangat, panas };
        };

        // Fungsi Keanggotaan KELEMBAPAN
        const membershipKelembapan = (x) => {
            const kering = x <= 40 ? 1 : (x >= 60 ? 0 : (60 - x) / (60 - 40));
            const ideal = (x <= 50 || x >= 80) ? 0 : (x < 65 ? (x - 50) / (65 - 50) : (80 - x) / (80 - 65));
            const lembap = x <= 70 ? 0 : (x >= 90 ? 1 : (x - 70) / (90 - 70));
            return { kering, ideal, lembap };
        };

        // Fungsi Keanggotaan AMONIA
        const membershipAmonia = (x) => {
            const rendah = x <= 10 ? 1 : (x >= 18 ? 0 : (18 - x) / (18 - 10));
            const sedang = (x <= 15 || x >= 30) ? 0 : (x < 22 ? (x - 15) / (22 - 15) : (30 - x) / (30 - 22));
            const tinggi = x <= 25 ? 0 : (x >= 35 ? 1 : (x - 25) / (35 - 25));
            return { rendah, sedang, tinggi };
        };
        
        // Fungsi Keanggotaan Output KIPAS (untuk plotting & defuzzifikasi)
        const KIPAS_SPEED = {
            mati: { range: [0, 2000], center: 0 },
            pelan: { range: [1000, 4000], center: 2500 },
            sedang: { range: [3000, 7000], center: 5000 },
            cepat: { range: [6000, 10000], center: 8000 },
        };
        
        const membershipKipas = (x, state) => {
            const params = KIPAS_SPEED[state];
            if (!params) return 0;
            const [a, b, c] = state === 'mati' ? [0, 0, 2000] 
                           : state === 'cepat' ? [6000, 10000, 10000]
                           : [params.range[0], params.center, params.range[1]];
            
            if (x <= a || x >= c) return 0;
            if (x > a && x <= b) return (x - a) / (b - a);
            if (x > b && x < c) return (c - x) / (c - b);
            return 0;
        };
        
        // === FUZZY RULES ===
        // Aturan didefinisikan di sini. Ini adalah "otak" dari sistem.
        const rules = [
            // [Kondisi], [Konsekuensi]
            { name: "R1", conditions: (s, k, a) => s.dingin, consequent: "mati" },
            { name: "R2", conditions: (s, k, a) => Math.min(s.hangat, k.kering), consequent: "pelan" },
            { name: "R3", conditions: (s, k, a) => Math.min(s.hangat, k.ideal, a.rendah), consequent: "pelan" },
            { name: "R4", conditions: (s, k, a) => Math.min(s.hangat, k.lembap), consequent: "sedang" },
            { name: "R5", conditions: (s, k, a) => a.sedang, consequent: "sedang" },
            { name: "R6", conditions: (s, k, a) => Math.min(s.panas, k.kering), consequent: "sedang" },
            { name: "R7", conditions: (s, k, a) => Math.min(s.panas, k.ideal), consequent: "cepat" },
            { name: "R8", conditions: (s, k, a) => Math.min(s.panas, k.lembap), consequent: "cepat" },
            { name: "R9", conditions: (s, k, a) => a.tinggi, consequent: "cepat" },
        ];

        // === MAIN CALCULATION FUNCTION ===
        function updateFuzzyLogic() {
            // 1. Get current crisp inputs
            const suhu = parseFloat(suhuSlider.value);
            const kelembapan = parseFloat(kelembapanSlider.value);
            const amonia = parseFloat(amoniaSlider.value);

            // Update display values
            suhuValue.textContent = suhu.toFixed(1);
            kelembapanValue.textContent = kelembapan.toFixed(0);
            amoniaValue.textContent = amonia.toFixed(1);
            
            let html = '';

            // === TAHAP 1: FUZZIFIKASI (Dengan Perhitungan Detail) ===
            const fuzzySuhu = membershipSuhu(suhu);
            const fuzzyKelembapan = membershipKelembapan(kelembapan);
            const fuzzyAmonia = membershipAmonia(amonia);
            
            html += `<div class="step"><h3>1. Fuzzifikasi Input</h3>`;
            html += `<p>Mengubah nilai input crisp menjadi derajat keanggotaan fuzzy dengan menggunakan fungsi keanggotaan yang telah didefinisikan.</p>`;
            
            // --- Detail Suhu ---
            html += `<h4>A. Suhu (${suhu.toFixed(1)}°C)</h4><ul>`;
            // Dingin
            if (suhu <= 20) { html += `<li>μ_dingin = <strong>1</strong> (karena ${suhu.toFixed(1)} ≤ 20)</li>`; } 
            else if (suhu > 20 && suhu < 25) { html += `<li>μ_dingin = (25 - ${suhu.toFixed(1)}) / (25 - 20) = <strong>${fuzzySuhu.dingin.toFixed(3)}</strong></li>`; } 
            else { html += `<li>μ_dingin = <strong>0</strong> (karena ${suhu.toFixed(1)} ≥ 25)</li>`; }
            // Hangat
            if (suhu <= 20 || suhu >= 32) { html += `<li>μ_hangat = <strong>0</strong></li>`; } 
            else if (suhu > 20 && suhu < 26) { html += `<li>μ_hangat = (${suhu.toFixed(1)} - 20) / (26 - 20) = <strong>${fuzzySuhu.hangat.toFixed(3)}</strong></li>`; } 
            else { html += `<li>μ_hangat = (32 - ${suhu.toFixed(1)}) / (32 - 26) = <strong>${fuzzySuhu.hangat.toFixed(3)}</strong></li>`; }
            // Panas
            if (suhu <= 28) { html += `<li>μ_panas = <strong>0</strong> (karena ${suhu.toFixed(1)} ≤ 28)</li>`; } 
            else if (suhu > 28 && suhu < 34) { html += `<li>μ_panas = (${suhu.toFixed(1)} - 28) / (34 - 28) = <strong>${fuzzySuhu.panas.toFixed(3)}</strong></li>`; } 
            else { html += `<li>μ_panas = <strong>1</strong> (karena ${suhu.toFixed(1)} ≥ 34)</li>`; }
            html += `</ul>`;

            // --- Detail Kelembapan ---
            html += `<h4>B. Kelembapan (${kelembapan.toFixed(0)}%)</h4><ul>`;
            // Kering
            if (kelembapan <= 40) { html += `<li>μ_kering = <strong>1</strong></li>`; } 
            else if (kelembapan > 40 && kelembapan < 60) { html += `<li>μ_kering = (60 - ${kelembapan.toFixed(0)}) / (60 - 40) = <strong>${fuzzyKelembapan.kering.toFixed(3)}</strong></li>`; }
            else { html += `<li>μ_kering = <strong>0</strong></li>`; }
            // Ideal
            if (kelembapan <= 50 || kelembapan >= 80) { html += `<li>μ_ideal = <strong>0</strong></li>`; } 
            else if (kelembapan > 50 && kelembapan < 65) { html += `<li>μ_ideal = (${kelembapan.toFixed(0)} - 50) / (65 - 50) = <strong>${fuzzyKelembapan.ideal.toFixed(3)}</strong></li>`; } 
            else { html += `<li>μ_ideal = (80 - ${kelembapan.toFixed(0)}) / (80 - 65) = <strong>${fuzzyKelembapan.ideal.toFixed(3)}</strong></li>`; }
            // Lembap
            if (kelembapan <= 70) { html += `<li>μ_lembap = <strong>0</strong></li>`; } 
            else if (kelembapan > 70 && kelembapan < 90) { html += `<li>μ_lembap = (${kelembapan.toFixed(0)} - 70) / (90 - 70) = <strong>${fuzzyKelembapan.lembap.toFixed(3)}</strong></li>`; } 
            else { html += `<li>μ_lembap = <strong>1</strong></li>`; }
            html += `</ul>`;

            // --- Detail Amonia ---
            html += `<h4>C. Amonia (${amonia.toFixed(1)} ppm)</h4><ul>`;
            // Rendah
            if (amonia <= 10) { html += `<li>μ_rendah = <strong>1</strong></li>`; } 
            else if (amonia > 10 && amonia < 18) { html += `<li>μ_rendah = (18 - ${amonia.toFixed(1)}) / (18 - 10) = <strong>${fuzzyAmonia.rendah.toFixed(3)}</strong></li>`; } 
            else { html += `<li>μ_rendah = <strong>0</strong></li>`; }
            // Sedang
            if (amonia <= 15 || amonia >= 30) { html += `<li>μ_sedang = <strong>0</strong></li>`; }
            else if (amonia > 15 && amonia < 22) { html += `<li>μ_sedang = (${amonia.toFixed(1)} - 15) / (22 - 15) = <strong>${fuzzyAmonia.sedang.toFixed(3)}</strong></li>`; } 
            else { html += `<li>μ_sedang = (30 - ${amonia.toFixed(1)}) / (30 - 22) = <strong>${fuzzyAmonia.sedang.toFixed(3)}</strong></li>`; }
            // Tinggi
            if (amonia <= 25) { html += `<li>μ_tinggi = <strong>0</strong></li>`; } 
            else if (amonia > 25 && amonia < 35) { html += `<li>μ_tinggi = (${amonia.toFixed(1)} - 25) / (35 - 25) = <strong>${fuzzyAmonia.tinggi.toFixed(3)}</strong></li>`; } 
            else { html += `<li>μ_tinggi = <strong>1</strong></li>`; }
            html += `</ul>`;
            html += `</div>`;

            // === TAHAP 2: EVALUASI ATURAN (INFERENSI) ===
            const ruleStrengths = rules.map(rule => ({
                name: rule.name,
                alpha: rule.conditions(fuzzySuhu, fuzzyKelembapan, fuzzyAmonia),
                consequent: rule.consequent
            })).filter(r => r.alpha > 0); 

            html += `<div class="step"><h3>2. Evaluasi Aturan (Inferensi)</h3>`;
            html += `<p>Menghitung kekuatan (nilai alpha) untuk setiap aturan berdasarkan derajat keanggotaan input (menggunakan operator MIN untuk AND). Aturan dengan alpha = 0 tidak ditampilkan.</p>`;
            ruleStrengths.forEach(r => {
                html += `<p><b>${r.name}</b> → Konsekuensi: Kipas <b>${r.consequent.toUpperCase()}</b>, Kekuatan (α): <code>${r.alpha.toFixed(3)}</code></p>`;
            });
            if (ruleStrengths.length === 0) {
                 html += `<p>Tidak ada aturan yang aktif untuk kombinasi input saat ini.</p>`;
            }
            html += `</div>`;
            
            // === TAHAP 3 & 4: AGREASI & DEFUZZIFIKASI (Centroid) ===
            html += `<div class="step"><h3>3. Agregasi & 4. Defuzzifikasi (Metode Centroid)</h3>`;
            html += `<p>Menggabungkan hasil dari semua aturan aktif (Agregasi), lalu menghitung nilai crisp-nya menggunakan metode Center of Gravity/Centroid (Defuzzifikasi).</p>`;
            
            let numerator = 0;
            let denominator = 0; 
            const samples = 100;
            const maxSpeed = 10000;
            const step = maxSpeed / samples;
            const aggregatedYs = [];
            const aggregatedXs = [];
            
            for (let z = 0; z <= maxSpeed; z += step) {
                let maxMembership = 0;
                ruleStrengths.forEach(rule => {
                    const clippedMembership = Math.min(rule.alpha, membershipKipas(z, rule.consequent));
                    if (clippedMembership > maxMembership) {
                        maxMembership = clippedMembership;
                    }
                });
                
                aggregatedXs.push(z);
                aggregatedYs.push(maxMembership);
                numerator += z * maxMembership;
                denominator += maxMembership;
            }

            const crispOutput = denominator === 0 ? 0 : numerator / denominator;
            
            html += `<p class="formula">z* = (Σ zᵢ * μ_agregat(zᵢ)) / (Σ μ_agregat(zᵢ))</p>`;
            html += `<ul><li>Pembilang (Σ zᵢ * μ): <code>${numerator.toFixed(2)}</code></li>`;
            html += `<li>Penyebut (Σ μ): <code>${denominator.toFixed(2)}</code></li>`;
            html += `<li><b>Kecepatan Kipas (z*)</b> = <code>${numerator.toFixed(2)} / ${denominator.toFixed(2)}</code> = <strong><code>${crispOutput.toFixed(0)} RPM</code></strong></li></ul>`;
            html += `</div>`;

            // Update UI
            perhitunganDetailDiv.innerHTML = html;
            outputValue.textContent = crispOutput.toFixed(0);
            
            // Update Graphs
            updateGraphs(suhu, kelembapan, amonia, {x: aggregatedXs, y: aggregatedYs}, crispOutput);
        }
        
        // === CHARTING FUNCTIONS ===
        function createPlotData(membershipFunc, range, steps) {
            const data = { labels: [], datasets: [] };
            const sets = Object.keys(membershipFunc(range[0]));
            
            for(let i = range[0]; i <= range[1]; i += (range[1]-range[0])/steps) {
                data.labels.push(i.toFixed(1));
            }
            
            sets.forEach((set, index) => {
                const colorKeys = ['blue', 'yellow', 'red', 'green', 'purple'];
                const dataset = {
                    label: set.charAt(0).toUpperCase() + set.slice(1),
                    data: [],
                    borderColor: CHART_COLORS[colorKeys[index % colorKeys.length]],
                    backgroundColor: CHART_COLORS[colorKeys[index % colorKeys.length]].replace('0.6', '0.2'),
                    fill: true,
                    tension: 0.1,
                    pointRadius: 0
                };
                data.labels.forEach(label => {
                    dataset.data.push(membershipFunc(parseFloat(label))[set]);
                });
                data.datasets.push(dataset);
            });
            return data;
        }

        function initializeCharts() {
            // Suhu Chart
            const suhuData = createPlotData(membershipSuhu, [15, 40], 100);
            const suhuCtx = document.getElementById('suhuChart').getContext('2d');
            suhuChart = new Chart(suhuCtx, { type: 'line', data: suhuData, options: { scales: { y: { beginAtZero: true, max: 1.1 } }, animation: { duration: 0 } } });
            
            // Kelembapan Chart
            const kelembapanData = createPlotData(membershipKelembapan, [20, 100], 100);
            const kelembapanCtx = document.getElementById('kelembapanChart').getContext('2d');
            kelembapanChart = new Chart(kelembapanCtx, { type: 'line', data: kelembapanData, options: { scales: { y: { beginAtZero: true, max: 1.1 } }, animation: { duration: 0 } } });

            // Amonia Chart
            const amoniaData = createPlotData(membershipAmonia, [0, 40], 100);
            const amoniaCtx = document.getElementById('amoniaChart').getContext('2d');
            amoniaChart = new Chart(amoniaCtx, { type: 'line', data: amoniaData, options: { scales: { y: { beginAtZero: true, max: 1.1 } }, animation: { duration: 0 } } });

            // Defuzzifikasi Chart
            const defuzzCtx = document.getElementById('defuzzifikasiChart').getContext('2d');
            const defuzzData = createPlotData(z => ({
                mati: membershipKipas(z, 'mati'), pelan: membershipKipas(z, 'pelan'),
                sedang: membershipKipas(z, 'sedang'), cepat: membershipKipas(z, 'cepat'),
            }), [0, 10000], 100);
            
            defuzzData.datasets.forEach(ds => {
                ds.borderColor = CHART_COLORS.gray;
                ds.backgroundColor = CHART_COLORS.gray.replace('0.5', '0.1');
            });
            defuzzData.datasets.push({
                label: 'Hasil Agregasi', data: [], borderColor: CHART_COLORS.purple,
                backgroundColor: CHART_COLORS.purple.replace('0.6', '0.3'),
                fill: true, stepped: false, pointRadius: 0
            });

            defuzzifikasiChart = new Chart(defuzzCtx, {
                type: 'line', data: defuzzData,
                options: { scales: { y: { beginAtZero: true, max: 1.1 }, x: { title: {display: true, text: 'Kecepatan Kipas (RPM)'} } }, animation: { duration: 0 } }
            });
        }
        
        function updateGraphs(suhu, kelembapan, amonia, aggregatedShape, crispOutput) {
            const annotationPlugin = {
                id: 'annotation',
                beforeDraw(chart, args, options) {
                    if (!options.annotations) return;
                    const { ctx, chartArea: { top, bottom }, scales: { x, y } } = chart;
                    for (const id in options.annotations) {
                        const box = options.annotations[id];
                        if (box.type === 'line' && box.scaleID === 'x') {
                             const xValue = x.getPixelForValue(box.value);
                             ctx.save();
                             ctx.beginPath();
                             ctx.moveTo(xValue, top);
                             ctx.lineTo(xValue, bottom);
                             ctx.lineWidth = box.borderWidth;
                             ctx.strokeStyle = box.borderColor;
                             ctx.setLineDash(box.borderDash || []);
                             ctx.stroke();
                             if (box.label && box.label.enabled) {
                                 ctx.fillStyle = box.borderColor;
                                 ctx.textAlign = box.label.position === 'start' ? 'left' : 'center';
                                 ctx.fillText(box.label.content, xValue + 5, top + 10);
                             }
                             ctx.restore();
                        }
                    }
                }
            };
            
            // Update vertical lines on input charts
            suhuChart.options.plugins.annotation = { annotations: { line1: { type: 'line', scaleID: 'x', value: suhu, borderColor: CHART_COLORS.inputLine, borderWidth: 2, label: { content: `Input: ${suhu.toFixed(1)}`, enabled: true, position: 'start' } } } };
            kelembapanChart.options.plugins.annotation = { annotations: { line1: { type: 'line', scaleID: 'x', value: kelembapan, borderColor: CHART_COLORS.inputLine, borderWidth: 2, label: { content: `Input: ${kelembapan.toFixed(0)}`, enabled: true, position: 'start' } } } };
            amoniaChart.options.plugins.annotation = { annotations: { line1: { type: 'line', scaleID: 'x', value: amonia, borderColor: CHART_COLORS.inputLine, borderWidth: 2, label: { content: `Input: ${amonia.toFixed(1)}`, enabled: true, position: 'start' } } } };
            
            suhuChart.update();
            kelembapanChart.update();
            amoniaChart.update();

            // Update defuzzification chart
            defuzzifikasiChart.data.labels = aggregatedShape.x;
            defuzzifikasiChart.data.datasets.find(ds => ds.label === 'Hasil Agregasi').data = aggregatedShape.y;
            defuzzifikasiChart.options.plugins.annotation = { annotations: {
                centroidLine: { type: 'line', scaleID: 'x', value: crispOutput, borderColor: CHART_COLORS.centroidLine, borderWidth: 3, borderDash: [6, 6], label: { content: `Centroid: ${crispOutput.toFixed(0)} RPM`, enabled: true, position: 'start' } }
            }};
            defuzzifikasiChart.update();
        }

        // === EVENT LISTENERS ===
        suhuSlider.addEventListener('input', updateFuzzyLogic);
        kelembapanSlider.addEventListener('input', updateFuzzyLogic);
        amoniaSlider.addEventListener('input', updateFuzzyLogic);

        // === INITIALIZE ===
        window.onload = () => {
            // Chart.register(annotationPlugin); // For Chart.js v3+, but let's use the provided CDN version which might be v2 and registers automatically
            initializeCharts();
            updateFuzzyLogic();
        };

    </script>
</body>
</html>
