<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Aplikasi Lo<PERSON><PERSON></title>
    <!-- Tailwind CSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- Chart.js CDN for fuzzy function visualization -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body {
            font-family: 'Inter', sans-serif; /* Menggunakan font Inter */
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }
        /* Custom styles if needed, but primarily use Tailwind */
        input[type="range"]::-webkit-slider-thumb {
            -webkit-appearance: none;
            appearance: none;
            width: 20px;
            height: 20px;
            background: #2563eb; /* Biru */
            border-radius: 50%;
            cursor: pointer;
            box-shadow: 0 0 0 4px rgba(37, 99, 235, 0.3);
            transition: background .15s ease-in-out;
        }
        input[type="range"]::-moz-range-thumb {
            width: 20px;
            height: 20px;
            background: #2563eb;
            border-radius: 50%;
            cursor: pointer;
            box-shadow: 0 0 0 4px rgba(37, 99, 235, 0.3);
            transition: background .15s ease-in-out;
        }
    </style>
</head>
<body class="bg-gray-100 flex items-center justify-center min-h-screen p-4">
    <div class="bg-white rounded-xl shadow-lg p-8 w-full max-w-4xl border border-gray-200">
        <h1 class="text-3xl font-bold text-center text-blue-700 mb-4">Penentu Kualitas Layanan Restoran (Logika Fuzzy)</h1>
        <p class="text-center text-gray-600 mb-8">Sesuaikan input 'Kebersihan' dan 'Keramahan Pelayanan' untuk melihat kualitas layanan menggunakan model Mamdani atau Sugeno.</p>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-8 mb-8">
            <!-- Input Section -->
            <div class="space-y-6">
                <h2 class="text-xl font-semibold text-gray-800 border-b pb-2 mb-4">Input Parameter</h2>

                <!-- Kebersihan Input -->
                <div>
                    <label for="kebersihan" class="block text-lg font-medium text-gray-700 mb-2">Kebersihan:</label>
                    <div class="flex items-center space-x-4">
                        <input type="range" id="kebersihan" min="0" max="100" value="50"
                               class="w-full h-2 bg-blue-200 rounded-lg appearance-none cursor-pointer">
                        <span id="kebersihan-value" class="text-xl font-bold text-blue-700 w-12 text-right">50</span>
                    </div>
                </div>

                <!-- Keramahan Pelayanan Input -->
                <div>
                    <label for="keramahan" class="block text-lg font-medium text-gray-700 mb-2">Keramahan Pelayanan:</label>
                    <div class="flex items-center space-x-4">
                        <input type="range" id="keramahan" min="0" max="100" value="50"
                               class="w-full h-2 bg-blue-200 rounded-lg appearance-none cursor-pointer">
                        <span id="keramahan-value" class="text-xl font-bold text-blue-700 w-12 text-right">50</span>
                    </div>
                </div>

                <!-- Model Selection -->
                <div class="mt-8">
                    <h3 class="text-lg font-medium text-gray-700 mb-3">Pilih Model Fuzzy:</h3>
                    <div class="flex items-center space-x-6">
                        <div class="flex items-center">
                            <input type="radio" id="mamdani" name="fuzzy-model" value="mamdani" checked
                                   class="form-radio h-5 w-5 text-blue-600 border-gray-300 rounded-full focus:ring-blue-500">
                            <label for="mamdani" class="ml-2 text-gray-700 text-base">Model Mamdani</label>
                        </div>
                        <div class="flex items-center">
                            <input type="radio" id="sugeno" name="fuzzy-model" value="sugeno"
                                   class="form-radio h-5 w-5 text-blue-600 border-gray-300 rounded-full focus:ring-blue-500">
                            <label for="sugeno" class="ml-2 text-gray-700 text-base">Model Sugeno</label>
                        </div>
                    </div>
                </div>

                <button id="calculate-btn"
                        class="mt-8 w-full bg-green-600 hover:bg-green-700 text-white font-bold py-3 px-6 rounded-lg shadow-md transition duration-300 ease-in-out transform hover:scale-105">
                    Hitung Kualitas Layanan
                </button>
            </div>

            <!-- Result Section -->
            <div class="bg-blue-50 p-6 rounded-lg border border-blue-200 flex flex-col justify-between">
                <div>
                    <h2 class="text-xl font-semibold text-gray-800 border-b pb-2 mb-4">Hasil Perhitungan</h2>
                    <div class="text-center mb-6">
                        <p class="text-gray-700 text-xl">Kualitas Layanan Dihitung:</p>
                        <p id="kualitas-output" class="text-5xl font-extrabold text-blue-700 mt-2">--</p>
                    </div>

                    <!-- Quality Bar -->
                    <div class="w-full bg-gray-200 rounded-full h-6 relative overflow-hidden">
                        <div id="quality-fill" class="bg-gradient-to-r from-red-500 via-yellow-400 to-green-500 h-full rounded-full transition-all duration-500 ease-out" style="width: 0%;"></div>
                        <span id="quality-label" class="absolute inset-0 flex items-center justify-center text-white font-bold text-sm"></span>
                    </div>
                    <p id="model-info" class="text-sm text-gray-500 mt-2 text-center"></p>
                </div>
            </div>
        </div>

        <!-- Fuzzy Function Visualization -->
        <div class="mt-8 pt-8 border-t border-gray-200">
            <h2 class="text-xl font-semibold text-gray-800 mb-4 text-center">Visualisasi Fungsi Keanggotaan</h2>
            <canvas id="fuzzyChart" class="w-full h-80"></canvas>
            <p class="text-sm text-gray-500 mt-2 text-center">Garis putus-putus menunjukkan posisi input Kebersihan (biru) dan Keramahan (hijau gelap).</p>
        </div>
    </div>

    <script>
        // Fungsi untuk menghitung derajat keanggotaan (segitiga)
        function triangleMembership(x, a, b, c) {
            if (x <= a || x >= c) return 0;
            if (x >= a && x <= b) return (x - a) / (b - a);
            if (x >= b && x <= c) return (c - x) / (c - b);
            return 0;
        }

        // Fungsi untuk menghitung derajat keanggotaan (trapesium)
        function trapezoidMembership(x, a, b, c, d) {
            if (x <= a || x >= d) return 0;
            if (x >= a && x <= b) return (x - a) / (b - a);
            if (x >= b && x <= c) return 1;
            if (x >= c && x <= d) return (d - x) / (d - c);
            return 0;
        }

        // Definisi fungsi keanggotaan untuk input dan output
        const membershipFunctions = {
            kebersihan: {
                buruk: (x) => trapezoidMembership(x, 0, 0, 30, 50),
                sedang: (x) => triangleMembership(x, 40, 60, 80),
                baik: (x) => trapezoidMembership(x, 70, 90, 100, 100),
            },
            keramahan: {
                tidakRamah: (x) => trapezoidMembership(x, 0, 0, 30, 50),
                cukupRamah: (x) => triangleMembership(x, 40, 60, 80),
                sangatRamah: (x) => trapezoidMembership(x, 70, 90, 100, 100),
            },
            kualitasMamdani: { // Output untuk Mamdani
                sangatBuruk: (x) => trapezoidMembership(x, 0, 0, 10, 20),
                buruk: (x) => triangleMembership(x, 10, 30, 50),
                cukup: (x) => triangleMembership(x, 40, 60, 80),
                baik: (x) => triangleMembership(x, 70, 90, 100),
                sangatBaik: (x) => trapezoidMembership(x, 90, 100, 100, 100),
            }
        };

        // Aturan Fuzzy untuk Mamdani dan Sugeno (antecedents sama)
        const fuzzyRules = [
            { kebersihan: 'buruk', keramahan: 'tidakRamah', kualitasMamdani: 'sangatBuruk', kualitasSugeno: 10 },
            { kebersihan: 'buruk', keramahan: 'cukupRamah', kualitasMamdani: 'buruk', kualitasSugeno: 30 },
            { kebersihan: 'buruk', keramahan: 'sangatRamah', kualitasMamdani: 'cukup', kualitasSugeno: 50 },

            { kebersihan: 'sedang', keramahan: 'tidakRamah', kualitasMamdani: 'buruk', kualitasSugeno: 30 },
            { kebersihan: 'sedang', keramahan: 'cukupRamah', kualitasMamdani: 'cukup', kualitasSugeno: 50 },
            { kebersihan: 'sedang', keramahan: 'sangatRamah', kualitasMamdani: 'baik', kualitasSugeno: 70 },

            { kebersihan: 'baik', keramahan: 'tidakRamah', kualitasMamdani: 'cukup', kualitasSugeno: 50 },
            { kebersihan: 'baik', keramahan: 'cukupRamah', kualitasMamdani: 'baik', kualitasSugeno: 70 },
            { kebersihan: 'baik', keramahan: 'sangatRamah', kualitasMamdani: 'sangatBaik', kualitasSugeno: 90 },
        ];

        // --- Mamdani Inference ---
        function inferMamdani(kebersihan, keramahan) {
            // 1. Fuzzifikasi
            const muKebersihan = {};
            for (const term in membershipFunctions.kebersihan) {
                muKebersihan[term] = membershipFunctions.kebersihan[term](kebersihan);
            }

            const muKeramahan = {};
            for (const term in membershipFunctions.keramahan) {
                muKeramahan[term] = membershipFunctions.keramahan[term](keramahan);
            }

            // 2. Aplikasi Operator Fuzzy (Evaluasi Aturan) & Agregasi Konsekuen
            // Ini akan membangun sebuah fungsi keanggotaan output gabungan
            // Default ke 0 jika tidak ada aturan yang teraktivasi
            const combinedOutputMembership = (x) => {
                let maxMu = 0;
                fuzzyRules.forEach(rule => {
                    const alphaKebersihan = muKebersihan[rule.kebersihan];
                    const alphaKeramahan = muKeramahan[rule.keramahan];

                    // T-norm (AND) = minimum dari derajat keanggotaan antecedent
                    const alpha = Math.min(alphaKebersihan, alphaKeramahan);

                    // Potong fungsi keanggotaan output sesuai alpha
                    const clippedMembership = Math.min(alpha, membershipFunctions.kualitasMamdani[rule.kualitasMamdani](x));

                    // T-conorm (OR) = maksimum dari semua hasil aturan
                    maxMu = Math.max(maxMu, clippedMembership);
                });
                return maxMu;
            };

            // 3. Defuzzifikasi (Centroid Method - Aproksimasi)
            const minVal = 0;
            const maxVal = 100;
            const steps = 100; // Jumlah sampel untuk aproksimasi integral
            let numerator = 0;
            let denominator = 0;

            for (let i = 0; i <= steps; i++) {
                const x = minVal + (i * (maxVal - minVal)) / steps;
                const mu = combinedOutputMembership(x);
                numerator += x * mu;
                denominator += mu;
            }

            return denominator === 0 ? 0 : numerator / denominator;
        }

        // --- Sugeno Inference ---
        function inferSugeno(kebersihan, keramahan) {
            let sumNumerator = 0;
            let sumDenominator = 0;

            // Fuzzifikasi input
            const muKebersihan = {};
            for (const term in membershipFunctions.kebersihan) {
                muKebersihan[term] = membershipFunctions.kebersihan[term](kebersihan);
            }

            const muKeramahan = {};
            for (const term in membershipFunctions.keramahan) {
                muKeramahan[term] = membershipFunctions.keramahan[term](keramahan);
            }

            fuzzyRules.forEach(rule => {
                const alphaKebersihan = muKebersihan[rule.kebersihan];
                const alphaKeramahan = muKeramahan[rule.keramahan];

                // Derajat aktivasi aturan (AND = MIN)
                const alpha = Math.min(alphaKebersihan, alphaKeramahan);

                // Konsekuen Sugeno (nilai konstanta)
                const z = rule.kualitasSugeno;

                sumNumerator += alpha * z;
                sumDenominator += alpha;
            });

            return sumDenominator === 0 ? 0 : sumNumerator / sumDenominator;
        }

        // --- UI Elements and Event Listeners ---
        const kebersihanSlider = document.getElementById('kebersihan');
        const keramahanSlider = document.getElementById('keramahan');
        const kebersihanValueSpan = document.getElementById('kebersihan-value');
        const keramahanValueSpan = document.getElementById('keramahan-value');
        const calculateBtn = document.getElementById('calculate-btn');
        const kualitasOutput = document.getElementById('kualitas-output');
        const qualityFill = document.getElementById('quality-fill');
        const qualityLabel = document.getElementById('quality-label');
        const modelInfo = document.getElementById('model-info');

        // Update slider value displays
        kebersihanSlider.addEventListener('input', () => {
            kebersihanValueSpan.textContent = kebersihanSlider.value;
            updateFuzzyChart(parseInt(kebersihanSlider.value), parseInt(keramahanSlider.value));
        });

        keramahanSlider.addEventListener('input', () => {
            keramahanValueSpan.textContent = keramahanSlider.value;
            updateFuzzyChart(parseInt(kebersihanSlider.value), parseInt(keramahanSlider.value));
        });

        calculateBtn.addEventListener('click', () => {
            const kebersihan = parseInt(kebersihanSlider.value);
            const keramahan = parseInt(keramahanSlider.value);
            const selectedModel = document.querySelector('input[name="fuzzy-model"]:checked').value;

            let result;
            let modelName = '';

            if (selectedModel === 'mamdani') {
                result = inferMamdani(kebersihan, keramahan);
                modelName = 'Model Mamdani';
            } else { // Sugeno
                result = inferSugeno(kebersihan, keramahan);
                modelName = 'Model Sugeno';
            }

            // Update result display
            kualitasOutput.textContent = result.toFixed(2);
            qualityFill.style.width = `${result}%`;

            // Update quality label based on result range
            let qualityText = "";
            if (result >= 90) qualityText = "Sangat Baik";
            else if (result >= 70) qualityText = "Baik";
            else if (result >= 50) qualityText = "Cukup";
            else if (result >= 30) qualityText = "Buruk";
            else qualityText = "Sangat Buruk";

            qualityLabel.textContent = qualityText;
            modelInfo.textContent = `Menggunakan ${modelName}`;

            updateFuzzyChart(kebersihan, keramahan); // Refresh chart with current inputs
        });

        // Initial calculation and chart update on load
        document.addEventListener('DOMContentLoaded', () => {
            calculateBtn.click(); // Trigger initial calculation and chart display
        });


        // --- Chart.js Visualization ---
        let fuzzyChartInstance;
        const ctx = document.getElementById('fuzzyChart').getContext('2d');

        function updateFuzzyChart(kebersihanVal, keramahanVal) {
            const labels = Array.from({ length: 101 }, (_, i) => i); // 0-100

            const datasets = [];

            // Add input membership functions
            for (const term in membershipFunctions.kebersihan) {
                datasets.push({
                    label: `Kebersihan - ${term.charAt(0).toUpperCase() + term.slice(1)}`,
                    data: labels.map(x => membershipFunctions.kebersihan[term](x)),
                    borderColor: term === 'buruk' ? 'rgba(255, 99, 132, 1)' : term === 'sedang' ? 'rgba(255, 159, 64, 1)' : 'rgba(75, 192, 192, 1)',
                    backgroundColor: 'rgba(0,0,0,0)', // Transparan
                    borderWidth: 2,
                    pointRadius: 0,
                    tension: 0.4
                });
            }

            for (const term in membershipFunctions.keramahan) {
                datasets.push({
                    label: `Keramahan - ${term.charAt(0).toUpperCase() + term.slice(1)}`,
                    data: labels.map(x => membershipFunctions.keramahan[term](x)),
                    borderColor: term === 'tidakRamah' ? 'rgba(153, 102, 255, 1)' : term === 'cukupRamah' ? 'rgba(255, 205, 86, 1)' : 'rgba(54, 162, 235, 1)',
                    backgroundColor: 'rgba(0,0,0,0)', // Transparan
                    borderWidth: 2,
                    pointRadius: 0,
                    tension: 0.4
                });
            }

            // Add current input markers
            datasets.push({
                label: `Input Kebersihan: ${kebersihanVal}`,
                data: [{ x: kebersihanVal, y: 0 }, { x: kebersihanVal, y: 1 }],
                borderColor: 'blue',
                borderDash: [5, 5],
                borderWidth: 2,
                pointRadius: 0,
                showLine: true,
                type: 'line'
            });

            datasets.push({
                label: `Input Keramahan: ${keramahanVal}`,
                data: [{ x: keramahanVal, y: 0 }, { x: keramahanVal, y: 1 }],
                borderColor: 'darkgreen',
                borderDash: [5, 5],
                borderWidth: 2,
                pointRadius: 0,
                showLine: true,
                type: 'line'
            });

            // Destroy existing chart instance if it exists
            if (fuzzyChartInstance) {
                fuzzyChartInstance.destroy();
            }

            fuzzyChartInstance = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: labels,
                    datasets: datasets
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        title: {
                            display: true,
                            text: 'Fungsi Keanggotaan Input',
                            font: {
                                size: 18
                            }
                        },
                        tooltip: {
                            mode: 'index',
                            intersect: false,
                        },
                    },
                    scales: {
                        x: {
                            title: {
                                display: true,
                                text: 'Nilai (0-100)',
                                font: {
                                    size: 16
                                }
                            },
                            min: 0,
                            max: 100,
                            grid: {
                                color: 'rgba(0,0,0,0.1)'
                            }
                        },
                        y: {
                            title: {
                                display: true,
                                text: 'Derajat Keanggotaan (μ)',
                                font: {
                                    size: 16
                                }
                            },
                            min: 0,
                            max: 1.05, // Slightly above 1 for better visual
                            grid: {
                                color: 'rgba(0,0,0,0.1)'
                            }
                        }
                    }
                }
            });
        }
    </script>
</body>
</html>
