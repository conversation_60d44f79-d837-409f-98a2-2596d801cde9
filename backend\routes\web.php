<?php

use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "web" middleware group. Make something great!
|
*/

Route::get('/', function () {
    return view('index');
});

Route::get('/dashboard', function () {
    return view('dashboard');
})->name('dashboard');

Route::get('/assessments', function () {
    return view('assessments');
})->name('assessments');

Route::get('/assessment/{code}', function ($code) {
    return view('dynamic-form', compact('code'));
})->name('dynamic-form');

Route::get('/srq20-form', function () {
    return redirect()->route('dynamic-form', ['code' => 'SRQ20']);
})->name('srq20-form');

Route::get('/history', function () {
    return view('history');
})->name('history');
