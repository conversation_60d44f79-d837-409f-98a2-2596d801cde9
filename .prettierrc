{"semi": true, "trailingComma": "none", "singleQuote": true, "printWidth": 120, "tabWidth": 2, "useTabs": false, "bracketSpacing": true, "bracketSameLine": false, "arrowParens": "avoid", "endOfLine": "lf", "quoteProps": "as-needed", "jsxSingleQuote": true, "htmlWhitespaceSensitivity": "css", "vueIndentScriptAndStyle": false, "embeddedLanguageFormatting": "auto", "overrides": [{"files": "*.json", "options": {"printWidth": 80}}, {"files": "*.md", "options": {"printWidth": 80, "proseWrap": "always"}}, {"files": "*.html", "options": {"printWidth": 120, "htmlWhitespaceSensitivity": "ignore"}}, {"files": "*.css", "options": {"printWidth": 120}}, {"files": "*.php", "options": {"printWidth": 120, "tabWidth": 4}}]}