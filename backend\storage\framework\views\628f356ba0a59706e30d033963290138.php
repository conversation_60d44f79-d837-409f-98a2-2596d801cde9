<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">
    <title>Assessment - SantriMental</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Inter', sans-serif;
        }
        
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        
        .glass-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(25px);
            -webkit-backdrop-filter: blur(25px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .custom-radio-label, .custom-scale-label {
            transition: all 0.2s ease-in-out;
        }
        
        input[type="radio"]:checked + .custom-radio-label,
        input[type="radio"]:checked + .custom-scale-label {
            border-color: #a78bfa;
            background-color: rgba(167, 139, 250, 0.2);
            color: #a78bfa;
            font-weight: 600;
        }
        
        .result-card {
            animation: fadeIn 0.5s ease-in-out;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        .question-card {
            transition: all 0.3s ease;
        }
        
        .question-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        }
    </style>
</head>
<body class="gradient-bg min-h-screen p-4">
    
    <!-- Navigation Header -->
    <nav class="glass-card rounded-xl p-4 mb-6 max-w-4xl mx-auto">
        <div class="flex items-center justify-between">
            <div class="flex items-center">
                <a href="<?php echo e(route('assessments')); ?>" class="text-white hover:text-purple-200 transition-colors mr-4">
                    <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z" clip-rule="evenodd"/>
                    </svg>
                </a>
                <div class="w-8 h-8 bg-gradient-to-r from-purple-500 to-pink-500 rounded-lg flex items-center justify-center mr-3">
                    <span class="text-white font-bold text-sm">S</span>
                </div>
                <h1 class="text-xl font-bold text-white">SantriMental</h1>
            </div>
            <div class="text-purple-200 text-sm" id="form-category">
                Assessment
            </div>
        </div>
    </nav>

    <main class="glass-card w-full max-w-4xl mx-auto rounded-2xl p-6 sm:p-8 md:p-10">
        
        <!-- Loading State -->
        <div id="loading-state" class="text-center py-12">
            <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-white mx-auto mb-4"></div>
            <p class="text-purple-200">Memuat form assessment...</p>
        </div>

        <!-- Progress Bar -->
        <div id="progress-section" class="hidden mb-8">
            <div class="flex items-center justify-between mb-2">
                <span class="text-purple-200 text-sm">Progress</span>
                <span id="progress-text" class="text-purple-200 text-sm">0/0</span>
            </div>
            <div class="w-full bg-white/20 rounded-full h-2">
                <div id="progress-bar" class="bg-gradient-to-r from-purple-500 to-pink-500 h-2 rounded-full transition-all duration-300" style="width: 0%"></div>
            </div>
        </div>
        
        <!-- Form Section -->
        <div id="form-section" class="hidden">
            <div class="text-center mb-8">
                <h1 id="form-title" class="text-2xl sm:text-3xl font-bold text-white mb-4"></h1>
                <p id="form-description" class="text-purple-200 max-w-2xl mx-auto"></p>
                <div id="form-instructions" class="mt-4 p-4 bg-white/10 rounded-lg">
                    <p class="text-purple-100 text-sm"></p>
                </div>
            </div>

            <div id="alert-message" class="hidden bg-red-500/20 border border-red-400 text-red-200 px-4 py-3 rounded-lg relative mb-6" role="alert">
                <div class="flex items-center">
                    <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"/>
                    </svg>
                    <div>
                        <strong class="font-bold">Perhatian!</strong>
                        <span class="block sm:inline">Harap jawab semua pertanyaan sebelum melihat hasil.</span>
                    </div>
                </div>
            </div>

            <form id="assessmentForm">
                <?php echo csrf_field(); ?>
                <div id="questions-container" class="space-y-6">
                    <!-- Questions will be dynamically inserted here -->
                </div>

                <div class="mt-10 text-center">
                    <button type="submit" id="submit-btn" class="bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 text-white font-bold py-4 px-8 rounded-lg shadow-lg transition-all duration-300 transform hover:scale-105 focus:outline-none focus:ring-4 focus:ring-purple-300 disabled:opacity-50 disabled:cursor-not-allowed">
                        <span id="submit-text">Lihat Hasil</span>
                        <svg id="submit-loading" class="hidden animate-spin -mr-1 ml-3 h-5 w-5 text-white inline" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                    </button>
                </div>
            </form>
        </div>

        <!-- Result Section -->
        <div id="result-section" class="hidden text-center result-card">
            <div class="mb-6">
                <div class="w-20 h-20 mx-auto mb-4 rounded-full flex items-center justify-center" id="result-icon">
                    <!-- Icon will be inserted here -->
                </div>
                <h2 id="result-title" class="text-2xl font-bold text-white mb-2">Hasil Assessment</h2>
                <p class="text-purple-200">Berdasarkan jawaban Anda pada <?php echo e(date('d F Y')); ?></p>
            </div>
            
            <div id="result-content" class="glass-card p-8 rounded-xl mb-6">
                <div class="mb-6">
                    <p class="text-purple-200 text-lg mb-2">Total Skor Anda:</p>
                    <p id="score" class="text-6xl font-bold text-white mb-2"></p>
                    <p id="score-range" class="text-purple-200 text-sm"></p>
                </div>
                
                <div class="border-t border-white/20 pt-6">
                    <h3 id="interpretation" class="text-xl font-semibold text-white mb-4"></h3>
                    <p id="recommendation" class="text-purple-200 leading-relaxed max-w-2xl mx-auto"></p>
                </div>
                
                <div id="next-steps" class="mt-6 p-4 bg-white/10 rounded-lg">
                    <h4 class="font-semibold text-white mb-2">Langkah Selanjutnya:</h4>
                    <ul id="steps-list" class="text-purple-200 text-sm space-y-1">
                        <!-- Steps will be inserted here -->
                    </ul>
                </div>
            </div>
            
            <div class="flex flex-col sm:flex-row gap-4 justify-center">
                <button id="save-result-btn" class="bg-green-600 hover:bg-green-700 text-white font-bold py-3 px-6 rounded-lg shadow-lg transition-all duration-300 transform hover:scale-105">
                    Simpan Hasil
                </button>
                <button id="reset-button" class="bg-white/20 hover:bg-white/30 text-white font-bold py-3 px-6 rounded-lg shadow-lg transition-all duration-300 transform hover:scale-105">
                    Ulangi Assessment
                </button>
                <a href="<?php echo e(route('dashboard')); ?>" class="bg-purple-600 hover:bg-purple-700 text-white font-bold py-3 px-6 rounded-lg shadow-lg transition-all duration-300 transform hover:scale-105 text-center">
                    Kembali ke Dashboard
                </a>
            </div>
        </div>

        <!-- Error State -->
        <div id="error-state" class="hidden text-center py-12">
            <div class="w-16 h-16 bg-red-500/20 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg class="w-8 h-8 text-red-400" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"/>
                </svg>
            </div>
            <h3 class="text-lg font-semibold text-white mb-2">Assessment Tidak Ditemukan</h3>
            <p class="text-purple-200 mb-4">Assessment yang Anda cari tidak tersedia atau tidak aktif</p>
            <a href="<?php echo e(route('assessments')); ?>" class="bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 text-white font-bold py-2 px-4 rounded-lg transition-all duration-300">
                Kembali ke Daftar Assessment
            </a>
        </div>

    </main>

    <!-- Toast Notification -->
    <div id="toast" class="hidden fixed top-4 right-4 glass-card p-4 rounded-lg shadow-lg z-50 max-w-sm">
        <div class="flex items-center">
            <div id="toast-icon" class="mr-3"></div>
            <div>
                <p id="toast-title" class="font-semibold text-white"></p>
                <p id="toast-message" class="text-purple-200 text-sm"></p>
            </div>
        </div>
    </div>

    <script src="<?php echo e(asset('js/auth.js')); ?>"></script>
    <script src="<?php echo e(asset('js/utils.js')); ?>"></script>
    <script>
        // Pass form code from Laravel to JavaScript
        window.formCode = '<?php echo e($code); ?>';
    </script>
    <script src="<?php echo e(asset('js/dynamic-form.js')); ?>"></script>
</body>
</html>
<?php /**PATH C:\laragon\www\santrimental\backend\resources\views/dynamic-form.blade.php ENDPATH**/ ?>