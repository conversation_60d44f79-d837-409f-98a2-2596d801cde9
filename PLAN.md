# SRQ20 Dashboard Application - Development Plan

## Project Overview
Modern, responsive SRQ20 mental health screening dashboard with authentication and database integration capabilities.

## Features to Implement

### 1. Authentication System
- Login page with email/password
- User registration
- Google OAuth integration
- QR Code login option
- JWT token management
- Protected routes

### 2. Dashboard Components
- Welcome/Landing page
- SRQ20 questionnaire form
- Results visualization
- User profile management
- Assessment history
- Analytics dashboard

### 3. SRQ20 Form Features
- 20 standardized questions
- Yes/No responses (Ya=1, Tidak=0)
- Progress tracking
- Timer functionality
- Validation (score >6 = mental health concern)
- Results interpretation

### 4. Database Integration Ready
- REST API endpoints structure
- Data models for users and assessments
- Error handling
- Loading states
- Offline capability preparation

### 5. Modern UI/UX
- Vibrant gradient backgrounds
- Glassmorphism effects
- Smooth animations
- Responsive design
- Dark/light mode
- Accessibility features

## File Structure
```
/
├── index.html (Landing/Login page)
├── register.html (Registration page)
├── dashboard.html (Main dashboard)
├── srq20-form.html (Assessment form)
├── profile.html (User profile)
├── history.html (Assessment history)
├── css/
│   ├── main.css (Global styles)
│   └── components.css (Component styles)
├── js/
│   ├── auth.js (Authentication logic)
│   ├── api.js (API integration)
│   ├── srq20.js (Form logic)
│   └── utils.js (Utility functions)
└── assets/
    └── icons/ (SVG icons)
```

## Technology Stack
- HTML5, CSS3, JavaScript (ES6+)
- Tailwind CSS for styling
- Chart.js for data visualization
- Google OAuth API
- QR Code generation library
- Local Storage for offline data
- Fetch API for backend communication

## API Endpoints (Ready for Backend)
- POST /api/auth/login
- POST /api/auth/register
- POST /api/auth/google
- POST /api/auth/qr-login
- GET /api/user/profile
- POST /api/assessments
- GET /api/assessments/history
- GET /api/analytics/dashboard
