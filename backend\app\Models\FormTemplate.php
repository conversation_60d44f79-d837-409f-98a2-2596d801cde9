<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class FormTemplate extends Model
{
    use HasFactory;

    protected $fillable = [
        'code',
        'name',
        'description',
        'category',
        'questions',
        'scoring_rules',
        'interpretation_rules',
        'time_limit',
        'is_active',
        'version'
    ];

    protected $casts = [
        'questions' => 'array',
        'scoring_rules' => 'array',
        'interpretation_rules' => 'array',
        'is_active' => 'boolean'
    ];

    public function formQuestions()
    {
        return $this->hasMany(FormQuestion::class);
    }

    public function responses()
    {
        return $this->hasMany(FormResponse::class);
    }

    public function calculateScore($answers)
    {
        $score = 0;
        $scoringRules = $this->scoring_rules;

        foreach ($answers as $questionNumber => $answer) {
            if (isset($scoringRules['questions'][$questionNumber])) {
                $questionRule = $scoringRules['questions'][$questionNumber];

                if ($questionRule['type'] === 'binary') {
                    $score += $answer ? $questionRule['yes_score'] : $questionRule['no_score'];
                } elseif ($questionRule['type'] === 'scale') {
                    $score += $answer * $questionRule['multiplier'];
                }
            }
        }

        return $score;
    }

    public function interpretScore($score)
    {
        $rules = $this->interpretation_rules;

        foreach ($rules as $rule) {
            if ($score >= $rule['min_score'] && $score <= $rule['max_score']) {
                return [
                    'status' => $rule['status'],
                    'interpretation' => $rule['interpretation'],
                    'recommendations' => $rule['recommendations']
                ];
            }
        }

        return [
            'status' => 'unknown',
            'interpretation' => 'Tidak dapat menentukan interpretasi',
            'recommendations' => 'Konsultasikan dengan profesional'
        ];
    }
}
