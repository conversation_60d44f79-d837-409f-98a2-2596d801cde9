/**
 * Utility functions and configurations
 */
const Utils = {
    // API Configuration
    API_BASE_URL: 'http://localhost:8000/api',
    MOCK_MODE: false, // Set to true when API is not available

    // Check if API is available
    async checkApiAvailability() {
        try {
            const response = await fetch(this.API_BASE_URL + '/health-check', {
                method: 'GET',
                headers: { 'Accept': 'application/json' }
            });
            this.MOCK_MODE = !response.ok;
        } catch (error) {
            console.warn('API server not available, switching to mock mode');
            this.MOCK_MODE = true;
        }
        
        if (this.MOCK_MODE) {
            this.showNotification('Using demo data - API server not connected', 'warning');
        }
    },

    // API call wrapper with mock data fallback
    async apiCall(endpoint, options = {}) {
        if (!this.MOCK_MODE) {
            try {
                const response = await fetch(this.API_BASE_URL + endpoint, {
                    ...options,
                    headers: {
                        'Accept': 'application/json',
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${localStorage.getItem('authToken')}`,
                        ...options.headers
                    }
                });

                if (!response.ok) {
                    throw new Error(`API call failed: ${response.statusText}`);
                }

                return await response.json();
            } catch (error) {
                console.error('API call failed:', error);
                this.MOCK_MODE = true;
                this.showNotification('API server error, switching to demo data', 'error');
                return this.getMockData(endpoint);
            }
        }

        return this.getMockData(endpoint);
    },

    // Mock data for offline/demo mode
    getMockData(endpoint) {
        const mockData = {
            '/auth/login': {
                success: true,
                token: 'mock-jwt-token',
                user: {
                    id: 1,
                    first_name: 'Demo',
                    last_name: 'User',
                    email: '<EMAIL>'
                }
            },
            '/assessments/dashboard-stats': {
                total_assessments: 15,
                latest_assessment: {
                    id: 1,
                    total_score: 4,
                    status: 'normal',
                    created_at: '2024-07-11T10:00:00Z'
                },
                monthly_stats: {
                    total_assessments: 5,
                    average_score: 3.2,
                    concerns: 1
                },
                trend: [
                    { date: '2024-07-01', score: 2 },
                    { date: '2024-07-05', score: 4 },
                    { date: '2024-07-10', score: 3 }
                ]
            },
            '/assessments': {
                data: [
                    {
                        id: 1,
                        total_score: 4,
                        status: 'normal',
                        created_at: '2024-07-11T10:00:00Z'
                    },
                    {
                        id: 2,
                        total_score: 7,
                        status: 'concern',
                        created_at: '2024-07-10T15:30:00Z'
                    }
                ],
                meta: {
                    current_page: 1,
                    total: 2
                }
            }
        };

        return mockData[endpoint] || { error: 'Mock data not found' };
    },

    // Show notification toast
    showNotification(message, type = 'info') {
        const toast = document.getElementById('toast');
        if (!toast) return;

        const icons = {
            success: '✅',
            error: '❌',
            warning: '⚠️',
            info: 'ℹ️'
        };

        document.getElementById('toast-icon').textContent = icons[type] || icons.info;
        document.getElementById('toast-message').textContent = message;
        
        toast.classList.remove('hidden');
        setTimeout(() => toast.classList.add('hidden'), 5000);
    },

    // Format date
    formatDate(date, options = {}) {
        return new Intl.DateTimeFormat('id-ID', {
            dateStyle: 'medium',
            timeStyle: 'short',
            ...options
        }).format(new Date(date));
    },

    // Get initials from name
    getInitials(name) {
        return name
            .split(' ')
            .map(word => word[0])
            .join('')
            .toUpperCase();
    },

    // Validate email format
    validateEmail(email) {
        return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email);
    },

    // Validate password strength
    validatePassword(password) {
        return password.length >= 8;
    }
};

// Export for use in other modules
window.Utils = Utils;

// Check API availability on load
document.addEventListener('DOMContentLoaded', () => {
    Utils.checkApiAvailability();
});
