<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Visualisasi Algoritma Jump Search</title>
    
    <!-- Memuat Tailwind CSS untuk styling -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Memuat Font Inter dari Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;700&display=swap" rel="stylesheet">

    <style>
        body {
            font-family: 'Inter', sans-serif;
        }
        #array-wrapper {
            position: relative;
            padding-top: 50px; /* <PERSON>uang untuk pointer */
        }
        #search-pointer {
            position: absolute;
            top: 0;
            left: 0;
            transition: transform 0.5s cubic-bezier(0.68, -0.55, 0.27, 1.55); /* Animasi 'bouncy' */
            will-change: transform;
            opacity: 0;
        }
        .array-element {
            transition: all 0.4s ease-in-out;
        }
        .faded {
            opacity: 0.25;
            transform: scale(0.9);
        }
        .found-animation {
            animation: pulse 1.2s infinite;
        }
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }
    </style>
</head>
<body class="bg-gray-100 text-gray-800 flex items-center justify-center min-h-screen p-4">

    <div class="w-full max-w-5xl bg-white rounded-xl shadow-lg p-6 md:p-8">
        
        <div class="text-center mb-6">
            <h1 class="text-2xl md:text-3xl font-bold text-gray-900">Visualisasi Algoritma Jump Search</h1>
            <p class="text-gray-600 mt-1">Mencari di **array terurut** dengan melompat antar blok, lalu mencari linear.</p>
        </div>

        <div id="array-wrapper" class="mb-6 min-h-[120px]">
            <div id="array-container" class="flex flex-wrap justify-center items-center gap-2">
                <!-- Elemen array dibuat oleh JS -->
            </div>
        </div>

        <div class="bg-gray-50 p-4 rounded-lg shadow-inner">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 items-center">
                <div class="flex flex-col">
                    <label for="search-input" class="text-sm font-medium text-gray-700 mb-1">Angka yang Dicari:</label>
                    <input type="number" id="search-input" placeholder="e.g., 42" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-indigo-500">
                </div>
                
                <div class="md:col-span-2 flex flex-col sm:flex-row gap-3 mt-4 md:mt-0 md:items-end h-full">
                    <button id="search-button" class="w-full sm:w-auto flex-grow bg-indigo-600 text-white font-semibold py-2 px-4 rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition">
                        Mulai Pencarian
                    </button>
                    <button id="reset-button" class="w-full sm:w-auto flex-grow bg-gray-600 text-white font-semibold py-2 px-4 rounded-md hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 transition">
                        Buat Array Baru
                    </button>
                </div>
            </div>
        </div>

        <div id="status-message" class="text-center mt-6 font-medium text-lg text-gray-700 min-h-[28px]">
            <!-- Pesan status -->
        </div>

    </div>

    <script>
        const arrayWrapper = document.getElementById('array-wrapper');
        const arrayContainer = document.getElementById('array-container');
        const searchInput = document.getElementById('search-input');
        const searchButton = document.getElementById('search-button');
        const resetButton = document.getElementById('reset-button');
        const statusMessage = document.getElementById('status-message');

        let dataArray = [];
        let searchPointer = null;
        const ARRAY_SIZE = 16; // Ukuran array yang bagus untuk sqrt(16)=4
        const JUMP_SPEED_MS = 800;
        const LINEAR_SPEED_MS = 400;

        function sleep(ms) {
            return new Promise(resolve => setTimeout(resolve, ms));
        }

        function createSearchPointer() {
            if (searchPointer && searchPointer.parentNode) {
                searchPointer.parentNode.removeChild(searchPointer);
            }
            searchPointer = document.createElement('div');
            searchPointer.id = 'search-pointer';
            searchPointer.innerHTML = `
                <svg width="32" height="32" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" class="text-indigo-600 drop-shadow-lg">
                    <path d="M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7zm0 9.5c-1.38 0-2.5-1.12-2.5-2.5s1.12-2.5 2.5-2.5 2.5 1.12 2.5 2.5-1.12 2.5-2.5 2.5z" fill="currentColor"/>
                </svg>
            `;
            arrayWrapper.prepend(searchPointer);
        }
        
        function movePointerTo(index) {
            const element = document.getElementById(`element-${index}`);
            if (!element) return;
            const elementRect = element.getBoundingClientRect();
            const containerRect = arrayWrapper.getBoundingClientRect();
            const pointerOffset = (elementRect.width / 2) - 16; // 16 is half pointer width
            const newX = elementRect.left - containerRect.left + pointerOffset;
            searchPointer.style.transform = `translateX(${newX}px)`;
        }

        function generateAndRenderArray() {
            dataArray = [];
            const usedNumbers = new Set();
            while (dataArray.length < ARRAY_SIZE) {
                const num = Math.floor(Math.random() * 100) + 1;
                if (!usedNumbers.has(num)) {
                    dataArray.push(num);
                    usedNumbers.add(num);
                }
            }
            dataArray.sort((a, b) => a - b);
            renderArray();
            createSearchPointer();
            statusMessage.textContent = 'Array terurut baru telah dibuat. Masukkan angka untuk dicari.';
            enableControls();
        }

        function renderArray() {
            arrayContainer.innerHTML = '';
            dataArray.forEach((value, index) => {
                const el = document.createElement('div');
                el.id = `element-${index}`;
                el.className = 'array-element flex items-center justify-center w-12 h-12 md:w-14 md:h-14 bg-gray-200 border-2 border-gray-300 rounded-md text-lg font-bold';
                el.textContent = value;
                arrayContainer.appendChild(el);
            });
        }

        async function performJumpSearch() {
            const targetValue = parseInt(searchInput.value, 10);
            if (isNaN(targetValue)) {
                statusMessage.textContent = 'Harap masukkan angka yang valid.';
                statusMessage.className = 'text-center mt-6 font-medium text-lg text-red-600';
                return;
            }

            disableControls();
            resetVisuals();
            searchPointer.style.opacity = 1;

            const n = dataArray.length;
            const jumpStep = Math.floor(Math.sqrt(n));
            let prev = 0;
            let current = 0;
            let found = false;

            // --- FASE JUMPING ---
            while (current < n && dataArray[current] < targetValue) {
                const jumpElement = document.getElementById(`element-${current}`);
                movePointerTo(current);
                statusMessage.textContent = `Melompat ke indeks ${current}. Nilai: ${dataArray[current]} < ${targetValue}`;
                statusMessage.className = 'text-center mt-6 font-medium text-lg text-orange-600';
                jumpElement.classList.add('bg-yellow-200', 'border-yellow-400', 'transform', 'scale-110');
                
                await sleep(JUMP_SPEED_MS);
                jumpElement.classList.remove('bg-yellow-200', 'border-yellow-400', 'transform', 'scale-110');

                prev = current;
                current += jumpStep;
            }

            // --- FASE LINEAR SEARCH ---
            statusMessage.textContent = `Target mungkin ada di antara indeks ${prev} dan ${Math.min(current, n - 1)}. Mencari...`;
            statusMessage.className = 'text-center mt-6 font-medium text-lg text-blue-700';
            
            // Fade out elemen di luar jangkauan
            for (let i = 0; i < n; i++) {
                if (i < prev || i > Math.min(current, n - 1)) {
                    document.getElementById(`element-${i}`).classList.add('faded');
                }
            }
            await sleep(JUMP_SPEED_MS);

            for (let i = prev; i < Math.min(current, n); i++) {
                const linearElement = document.getElementById(`element-${i}`);
                movePointerTo(i);
                linearElement.classList.add('bg-blue-200', 'border-blue-400', 'transform', 'scale-110');
                statusMessage.textContent = `Mengecek indeks ${i} (nilai: ${dataArray[i]})...`;

                await sleep(LINEAR_SPEED_MS);

                if (dataArray[i] === targetValue) {
                    statusMessage.textContent = `Elemen ${targetValue} ditemukan di indeks ${i}!`;
                    statusMessage.className = 'text-center mt-6 font-medium text-lg text-green-600';
                    linearElement.classList.remove('bg-blue-200', 'border-blue-400');
                    linearElement.classList.add('bg-green-400', 'border-green-600', 'text-white', 'found-animation');
                    found = true;
                    break;
                } else {
                    linearElement.classList.remove('bg-blue-200', 'border-blue-400', 'transform', 'scale-110');
                }
            }

            if (!found) {
                statusMessage.textContent = `Elemen ${targetValue} tidak ditemukan dalam array.`;
                statusMessage.className = 'text-center mt-6 font-medium text-lg text-red-600';
                searchPointer.style.opacity = 0;
            }

            enableControls(false);
        }

        function disableControls() {
            searchInput.disabled = true;
            searchButton.disabled = true;
            resetButton.disabled = true;
            searchButton.classList.add('opacity-50', 'cursor-not-allowed');
            resetButton.classList.add('opacity-50', 'cursor-not-allowed');
        }

        function enableControls(clearInput = true) {
            searchInput.disabled = false;
            searchButton.disabled = false;
            resetButton.disabled = false;
            searchButton.classList.remove('opacity-50', 'cursor-not-allowed');
            resetButton.classList.remove('opacity-50', 'cursor-not-allowed');
            if (clearInput) {
                searchInput.value = '';
                resetVisuals();
            }
        }
        
        function resetVisuals() {
             const allElements = document.querySelectorAll('.array-element');
             allElements.forEach(el => {
                el.className = 'array-element flex items-center justify-center w-12 h-12 md:w-14 md:h-14 bg-gray-200 border-2 border-gray-300 rounded-md text-lg font-bold';
             });
        }

        searchButton.addEventListener('click', performJumpSearch);
        resetButton.addEventListener('click', generateAndRenderArray);
        searchInput.addEventListener('keyup', e => e.key === 'Enter' && searchButton.click());
        window.addEventListener('DOMContentLoaded', generateAndRenderArray);
    </script>
</body>
</html>
